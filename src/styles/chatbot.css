/* src/styles/chatbot.css */

/* Chatbot Container */
.chatbot-container {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1002;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.chatbot-icon-container {
  cursor: pointer;
}

/* Chatbot Icon */
.chatbot-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  position: relative;
  border: 3px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.chatbot-icon-hover {
  transform: scale(1.1);
  box-shadow: 0 8px 30px rgba(102, 126, 234, 0.6);
  animation: pulse 1.5s infinite;
}

.chat-icon {
  transition: transform 0.3s ease;
}

.chatbot-icon:hover .chat-icon {
  transform: scale(1.1);
}

.new-message-indicator {
  position: absolute;
  top: 0;
  right: 0;
  width: 15px;
  height: 15px;
  background-color: #ff4d4f;
  border-radius: 50%;
  border: 2px solid white;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.7); }
  70% { transform: scale(1); box-shadow: 0 0 0 10px rgba(255, 77, 79, 0); }
  100% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(255, 77, 79, 0); }
}

/* Chat Window */
.chatbot-window {
  width: 380px;
  height: 520px;
  background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.15), 0 10px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  margin-right: 40px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
}

@keyframes slideIn {
  from { transform: translateY(30px) scale(0.95); opacity: 0; }
  to { transform: translateY(0) scale(1); opacity: 1; }
}

@keyframes slideInMobile {
  from { transform: translateY(100%) scale(0.95); opacity: 0; }
  to { transform: translateY(0) scale(1); opacity: 1; }
}

/* Chat Header */
.chatbot-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  flex-shrink: 0;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.bot-avatar {
  font-size: 1.8rem;
  background: rgba(255,255,255,0.2);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.header-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.chatbot-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 700;
  letter-spacing: 0.5px;
}

.status-text {
  font-size: 0.8rem;
  opacity: 0.9;
  font-weight: 400;
}

.close-button {
  background: rgba(255,255,255,0.15);
  border: none;
  color: white;
  font-size: 1.2rem;
  font-weight: 400;
  cursor: pointer;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.close-button:hover {
  background: rgba(255,255,255,0.25);
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

/* Messages Area */
.chatbot-messages {
  flex-grow: 1;
  padding: 15px 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Scrollbar styles */
.chatbot-messages::-webkit-scrollbar { width: 6px; }
.chatbot-messages::-webkit-scrollbar-track { background: transparent; }
.chatbot-messages::-webkit-scrollbar-thumb { background: #dce1e6; border-radius: 3px; }
.chatbot-messages::-webkit-scrollbar-thumb:hover { background: #c8ced3; }

/* Chat Bubble */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.chat-bubble {
  padding: 12px 18px;
  border-radius: 20px;
  max-width: 80%;
  word-wrap: break-word;
  line-height: 1.5;
  animation: fadeIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: bottom;
}

.chat-bubble.user {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  align-self: flex-end;
  border-bottom-right-radius: 5px;
  box-shadow: 0 3px 8px rgba(102, 126, 234, 0.3);
}

.chat-bubble.bot {
  background-color: #ffffff;
  color: #343a40;
  align-self: flex-start;
  border-bottom-left-radius: 5px;
  box-shadow: 0 3px 8px rgba(0,0,0,0.08);
  border: 1px solid #f0f0f0;
}

.chat-bubble.has-campaigns {
  background-color: transparent;
  border: none;
  box-shadow: none;
  padding: 10px;
}

.message-text {
  font-size: 0.98rem;
}

.message-text ul { padding-left: 20px; margin: 8px 0 0; }
.message-text a { color: #0056b3; text-decoration: none; font-weight: 500; }
.message-text a:hover { text-decoration: underline; }

/* Campaign Link List in Chat */
.campaign-link-list {
  list-style-type: disc;
  padding-left: 20px;
  margin: 10px 0 0 0;
}

.campaign-link-list li {
  margin-bottom: 8px;
}

.campaign-link-list a {
  color: #0056b3;
  text-decoration: underline;
  font-weight: 500;
  cursor: pointer;
}

.campaign-link-list a:hover {
  color: #003d82;
}

/* Campaign List Buttons */
.campaign-list-buttons ul {
  list-style-type: none;
  padding: 0;
  margin: 10px 0 0 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.campaign-list-buttons button {
  width: 100%;
  padding: 10px 15px;
  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  text-align: left;
  font-size: 0.95rem;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
}

.campaign-list-buttons button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.08);
  border-color: #ccc;
  background: white;
}


/* Chat Bubble Close Button */
.chat-bubble-close-button {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 28px;
  height: 28px;
  border: none;
  background: transparent;
  cursor: pointer;
  z-index: 2;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.chat-bubble-close-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.chat-bubble-close-button svg {
  transition: transform 0.2s ease;
}

.chat-bubble-close-button:hover svg {
  transform: scale(1.1);
}

/* Message Options */
.message-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.campaign-cards-container {
  max-width: 100%;
  padding: 0;
  margin: 0;
  position: relative;
}

.campaign-slider .slick-slide {
  padding: 0 5px; /* Add some spacing between slides */
}

.campaign-slider .slick-list {
  margin: 0 -3px;
}

/*
.custom-arrow {
  display: none !important; 
}

.campaign-slider .slick-prev,
.campaign-slider .slick-next {
  display: none !important;
}
*/

.slider-counter {
  margin-top: 6px;
  font-size: 11px;
}

.option-button {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 10px 16px;
  border-radius: 20px;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
  backdrop-filter: blur(10px);
}

.option-button:hover {
  background: linear-gradient(135deg, #5a67d8, #667eea);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Input Form */
.chatbot-input-form {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  background-color: #fff;
  border-top: 1px solid #e9ecef;
}

.chatbot-input {
  flex-grow: 1;
  border: none;
  background-color: #f1f3f5;
  padding: 14px 18px;
  border-radius: 22px;
  font-size: 1rem;
  margin-right: 12px;
  transition: background-color 0.2s;
  color: #343a40;
}

.chatbot-input:focus {
  outline: none;
  background-color: #e9ecef;
}
.chatbot-input::placeholder {
  color: #868e96;
}

.send-button {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.send-button:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a67d8, #667eea);
}

.send-button:disabled {
  background: #adb5bd;
  cursor: not-allowed;
  transform: scale(1);
  box-shadow: none;
}
.send-button svg {
    transition: transform 0.2s ease-out;
}
.send-button:hover svg {
    transform: rotate(-15deg);
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    padding: 10px 0;
}
.typing-indicator span {
    width: 8px;
    height: 8px;
    margin: 0 3px;
    background-color: #a9a9a9;
    border-radius: 50%;
    display: inline-block;
    animation: bounce 1.4s infinite ease-in-out both;
}

.typing-indicator span:nth-of-type(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-of-type(2) { animation-delay: -0.16s; }

@keyframes bounce {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1.0); }
}

/* Campaign Cards */
.campaign-card {
  background-color: #fff;
  border-radius: 16px;
  overflow: hidden;
  margin: 5px auto;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  max-width: 400px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Contact Form Styles */
.contact-form-body {
  padding: 20px 25px;
  text-align: center;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.contact-form-body p {
  margin-bottom: 25px;
  font-size: 1rem;
  color: #555;
  line-height: 1.6;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  width: 100%;
}

.form-control {
  width: 100%;
  padding: 14px 18px;
  border-radius: 12px;
  border: 1px solid #dce1e6;
  font-size: 1rem;
  background-color: #fff;
  transition: all 0.3s ease;
  box-sizing: border-box;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.form-control:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.submit-button {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 14px 20px;
  border-radius: 12px;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.campaign-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.campaign-card-image {
  width: 100%;
  height: 160px;
  object-fit: cover;
  display: block;
  flex-shrink: 0;
}

.campaign-card-content {
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.campaign-card-content h4 {
  margin: 0 0 4px 0;
  font-size: 1.05rem;
  font-weight: 600;
  color: #212529;
}

.campaign-card-content p {
  margin: 0 0 16px 0;
  font-size: 0.9rem;
  color: #6c757d;
  flex-grow: 1; /* Allows text to take available space */
}

.campaign-card-link,
.campaign-card-link:hover {
  text-decoration: none;
}

.campaign-card-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white !important;
  padding: 12px;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  text-align: center;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  margin-top: auto; /* Pushes button to the bottom */
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.campaign-card-button:hover {
  background: linear-gradient(135deg, #5a67d8, #667eea);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Slider Customization */
.campaign-slider .slick-prev,
.campaign-slider .slick-next {
    z-index: 1;
    width: 30px;
    height: 30px;
}

.campaign-slider .slick-prev {
    left: -15px;
}

.campaign-slider .slick-next {
    right: -15px;
}

.campaign-slider .slick-prev:before,
.campaign-slider .slick-next:before {
    font-size: 30px;
    opacity: 0.75;
}

/* Custom Arrow Styles */
.custom-arrow:hover {
    background-color: rgba(255, 255, 255, 1) !important;
    transform: translateY(-50%) scale(1.1) !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2) !important;
}

.custom-arrow svg {
    transition: all 0.2s ease;
}

.custom-arrow:hover svg {
    stroke: #007bff;
    stroke-width: 2.5;
}

.custom-arrow:active {
    transform: translateY(-50%) scale(0.95) !important;
}

.slider-counter {
  text-align: center;
  margin-top: 8px;
  font-size: 12px;
  color: #888;
}

/* Slick Slider Styles */
.slick-slider {
    padding: 0 5px;
}

.slick-slide > div {
    padding: 0 8px;
}

.slick-prev, .slick-next {
    width: 30px !important;
    height: 30px !important;
    z-index: 1;
}

.slick-prev { left: -10px !important; }
.slick-next { right: -10px !important; }

.slick-prev:before, .slick-next:before {
    font-size: 30px !important;
    color: #1d1d1f !important;
    opacity: 0.7;
}

.slick-dots li button:before {
    color: #1d1d1f !important;
    opacity: 0.5;
}

.slick-dots li.slick-active button:before {
    opacity: 1;
}

/* Make sure the dot container doesn't take up space */
.slick-dots {
    bottom: -15px !important;
}

.slick-dots li {
    margin: 0 !important;
}

.hidden-dot {
  display: none !important; /* Force hide the custom paging dot */
}

/* ==================== INITIAL BUBBLE STYLES ==================== */

.initial-bubble-container {
  position: absolute;
  right: 48px;
  bottom: 32px;
  min-width: 220px;
  max-width: 320px;
  display: flex;
  align-items: flex-end;
  z-index: 1100;
  overflow: visible;
  justify-content: center;
  flex-direction: row;
  height: auto;
}

.initial-bubble-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.initial-bubble-arrow {
  position: absolute;
  right: 16px;
  bottom: -10px;
  width: 0;
  height: 0;
  border-top: 10px solid #fff;
  border-left: 10px solid transparent;
  border-right: 0px solid transparent;
  box-shadow: 2px 2px 8px 0 rgba(0,0,0,0.07);
  z-index: 1;
}

/* ==================== MOBILE OVERLAY ==================== */

.chatbot-mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: none;
}

@media (max-width: 480px) {
  .chatbot-mobile-overlay {
    display: block;
  }
}

/* ==================== MOBILE RESPONSIVE STYLES ==================== */

/* Mobile form improvements */
@media (max-width: 480px) {
  .form-control {
    font-size: 16px !important;
    padding: 16px 18px !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    border-radius: 12px !important;
    background-color: #f8f9fa !important;
    border: 1px solid #e9ecef !important;
    box-sizing: border-box !important;
  }
  
  .form-control:focus {
    background-color: #fff !important;
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2) !important;
    outline: none !important;
  }
  
  .submit-button {
    font-size: 16px !important;
    padding: 16px 20px !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    border: none !important;
    border-radius: 12px !important;
  }
  
  .submit-button:active {
    transform: scale(0.98) !important;
  }
  
  /* Prevent zoom on input focus */
  input[type="text"],
  input[type="email"],
  input[type="tel"],
  input[type="password"] {
    font-size: 16px !important;
  }
}

/* Tablet (768px and below) */
@media (max-width: 768px) {
  /*
  .chat-bubble.has-campaigns {
    padding: 0;
  }
  */

  .chatbot-container {
    bottom: 20px;
    right: 20px;
  }

  .chatbot-window {
    width: calc(100vw - 40px);
    max-width: 350px;
    height: 420px;
    margin-right: 0;
    position: fixed;
    bottom: 80px;
    right: 20px;
  }

  .chatbot-icon {
    width: 56px;
    height: 56px;
  }

  .chatbot-header h3 {
    font-size: 1.1rem;
  }

  .chatbot-messages {
    padding: 12px 16px;
    gap: 10px;
  }

  .chat-bubble {
    max-width: 85%;
    padding: 10px 14px;
  }

  .message-text {
    font-size: 0.95rem;
  }

  .chatbot-input-form {
    padding: 12px 16px;
  }

  .chatbot-input {
    padding: 12px 16px;
    font-size: 0.95rem;
    margin-right: 10px;
  }

  .send-button {
    width: 40px;
    height: 40px;
  }

  .campaign-card {
    max-width: 100%;
  }

  .campaign-card-image {
    height: 140px;
  }

  .campaign-card-content {
    padding: 10px 14px;
  }

  .campaign-card-content h4 {
    font-size: 1rem;
  }

  .campaign-card-content p {
    font-size: 0.85rem;
  }

  .campaign-card-button {
    padding: 10px;
    font-size: 0.9rem;
  }

  .option-button {
    padding: 6px 12px;
    font-size: 0.85rem;
  }

  /* Initial bubble tablet adjustments */
  .initial-bubble-container {
    right: 35px;
    bottom: 25px;
    min-width: 200px;
    max-width: 280px;
  }

  .initial-bubble-arrow {
    right: 14px;
    bottom: -8px;
    border-top: 8px solid #fff;
    border-left: 8px solid transparent;
  }

  /* Close button tablet adjustments */
  .chat-bubble-close-button {
    top: 5px;
    right: 5px;
    width: 30px;
    height: 30px;
  }

  .chat-bubble-close-button svg {
    width: 18px;
    height: 18px;
  }

  /* Hide dots completely on tablet too */
  .campaign-slider .slick-dots,
  .slick-dots {
    display: none !important;
  }
  
  .campaign-slider .slick-dots li {
    display: none !important;
  }
}

/* Mobile (480px and below) */
@media (max-width: 480px) {
  .chatbot-container {
    bottom: 15px;
    right: 15px;
    left: auto;
  }

  .chatbot-window {
    width: calc(100vw - 20px);
    height: 70vh;
    max-height: 600px;
    min-height: 450px;
    border-radius: 16px;
    margin-right: 0;
    position: fixed;
    bottom: 70px;
    right: 10px;
    left: 10px;
    transform: none;
    z-index: 1001;
    animation: slideInMobile 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow: hidden;
  }

  .chatbot-header {
    padding: 12px 16px;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    flex-shrink: 0;
  }

  .bot-avatar {
    width: 28px;
    height: 28px;
    font-size: 1.2rem;
  }

  .chatbot-header h3 {
    font-size: 1rem;
    font-weight: 600;
  }

  .status-text {
    font-size: 0.65rem;
  }

  .close-button {
    width: 28px;
    height: 28px;
    font-size: 1rem;
  }

  .chatbot-messages {
    padding: 8px 12px;
    gap: 6px;
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  .chat-bubble {
    max-width: 90%;
    padding: 8px 12px;
    border-radius: 14px;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  .chat-bubble.user {
    border-bottom-right-radius: 4px;
  }

  .chat-bubble.bot {
    border-bottom-left-radius: 4px;
  }

  .message-text {
    font-size: 0.95rem;
    line-height: 1.5;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  .message-options {
    gap: 6px;
    margin-top: 8px;
  }

  .option-button {
    padding: 6px 10px;
    font-size: 0.8rem;
    border-radius: 14px;
  }

  /* Close button mobile adjustments */
  .chat-bubble-close-button {
    top: 4px;
    right: 4px;
    width: 32px;
    height: 32px;
  }

  .chat-bubble-close-button svg {
    width: 20px;
    height: 20px;
  }

  .chatbot-input-form {
    padding: 10px 12px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    flex-shrink: 0;
    border-top: 1px solid #e9ecef;
  }

  .chatbot-input {
    padding: 12px 16px;
    font-size: 16px;
    margin-right: 8px;
    border-radius: 18px;
    border: 1px solid #e9ecef;
    background-color: #f8f9fa;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  .send-button {
    width: 40px;
    height: 40px;
    min-width: 40px;
  }

  .send-button svg {
    width: 20px;
    height: 20px;
  }

  .chatbot-icon {
    width: 60px;
    height: 60px;
  }

  .new-message-indicator {
    width: 14px;
    height: 14px;
  }

  /* Campaign Card Mobile Optimizations */
  .campaign-card {
    margin: 3px auto;
    border-radius: 12px;
  }

  .campaign-card-image {
    height: 120px;
  }

  .campaign-card-content {
    padding: 8px 12px;
  }

  .campaign-card-content h4 {
    font-size: 0.95rem;
    margin-bottom: 3px;
  }

  .campaign-card-content p {
    font-size: 0.8rem;
    margin-bottom: 12px;
  }

  .campaign-card-button {
    padding: 8px;
    font-size: 0.85rem;
    border-radius: 8px;
  }

  .campaign-cards-container {
    min-height: 300px;
    width: 100%;
  }

  .campaign-slider .slick-slide {
    padding: 0 3px;
  }

  .campaign-slider .slick-list {
    margin: 0 -3px;
  }

  /*
  .custom-arrow {
    display: none !important;
  }

  .campaign-slider .slick-prev,
  .campaign-slider .slick-next {
    display: none !important;
  }
  */

  .slider-counter {
    margin-top: 6px;
    font-size: 11px;
  }

  /* Hide dots completely on mobile */
  /*
  .campaign-slider .slick-dots,
  .slick-dots {
    display: none !important;
  }
  
  .campaign-slider .slick-dots li {
    display: none !important;
  }
  */

  /* Typing indicator mobile */
  .typing-indicator span {
    width: 6px;
    height: 6px;
    margin: 0 2px;
  }

  /* Initial bubble mobile positioning */
  .initial-bubble-container {
    right: 20px;
    bottom: 20px;
    min-width: 180px;
    max-width: calc(100vw - 100px);
  }

  .initial-bubble-arrow {
    right: 12px;
    bottom: -6px;
    border-top: 6px solid #fff;
    border-left: 6px solid transparent;
  }
}

/* Extra Small Mobile (320px and below) */
@media (max-width: 320px) {
  .chatbot-window {
    width: calc(100vw - 20px);
    height: 50vh;
    max-height: 380px;
    position: fixed;
    bottom: 75px;
    right: 10px;
    left: 10px;
    transform: none;
    z-index: 1001;
    animation: slideInMobile 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .chatbot-container {
    bottom: 10px;
    right: 10px;
    left: auto;
  }

  .chatbot-messages {
    padding: 8px 12px;
  }

  .chat-bubble {
    padding: 6px 10px;
    max-width: 95%;
  }

  .message-text {
    font-size: 0.85rem;
  }

  .chatbot-input-form {
    padding: 8px 12px;
  }

  .chatbot-input {
    padding: 8px 12px;
    font-size: 0.85rem;
  }

  .option-button {
    padding: 5px 8px;
    font-size: 0.75rem;
  }

  .campaign-card-content h4 {
    font-size: 0.9rem;
  }

  .campaign-card-content p {
    font-size: 0.75rem;
  }

  .campaign-card-button {
    font-size: 0.8rem;
  }

  /* Initial bubble extra small mobile adjustments */
  .initial-bubble-container {
    right: 15px;
    bottom: 15px;
    min-width: 160px;
    max-width: calc(100vw - 80px);
  }

  .initial-bubble-arrow {
    right: 10px;
    bottom: -5px;
    border-top: 5px solid #fff;
    border-left: 5px solid transparent;
  }

  /* Close button extra small mobile adjustments */
  .chat-bubble-close-button {
    top: 3px;
    right: 3px;
    width: 30px;
    height: 30px;
  }

  .chat-bubble-close-button svg {
    width: 18px;
    height: 18px;
  }
}

/* Landscape mode adjustments for mobile */
@media (max-height: 500px) and (max-width: 768px) {
  .chatbot-window {
    height: calc(100vh - 80px);
    max-height: 400px;
  }

  .chatbot-messages {
    padding: 8px 14px;
  }
} 

/* =================================== */
/* SLIDER OVERRIDE FOR CHATBOT */
/* =================================== */

.chatbot-window .campaign-slider .slick-list {
  overflow: visible;
}

.chatbot-window .campaign-slider .slick-slide {
  height: auto !important;
  min-height: 250px; /* Minimum height to ensure visibility */
}

.chatbot-window .campaign-slider .slick-slide > div {
  height: 100%;
  display: flex;
  align-items: stretch;
} 

/* Custom Mobile Slider Styles */
.custom-mobile-slider {
  position: relative;
  overflow: hidden;
}

.slider-content-wrapper {
  display: flex;
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slider-card {
  flex: 0 0 100%;
  box-sizing: border-box;
}

.custom-slider-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid #ddd;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  font-size: 18px;
  font-weight: bold;
  color: #333;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
}

.custom-slider-arrow.prev {
  left: 5px;
}

.custom-slider-arrow.next {
  right: 5px;
}

.custom-mobile-slider .slider-counter {
  text-align: center;
  font-size: 12px;
  color: #888;
  margin-top: 10px;
} 