/* Mobile-specific fixes and improvements */

/* Prevent horizontal scrolling on mobile */
html, body {
  overflow-x: hidden;
  max-width: 100vw;
}

/* Ensure all containers don't exceed viewport width */
* {
  box-sizing: border-box;
}

/* Mobile-first responsive containers */
.mobile-container {
  width: 100%;
  max-width: 100vw;
  margin: 0 auto;
  padding: 0 8px;
}

@media (min-width: 600px) {
  .mobile-container {
    padding: 0 12px;
  }
}

@media (min-width: 960px) {
  .mobile-container {
    padding: 0 16px;
  }
}

@media (min-width: 1280px) {
  .mobile-container {
    padding: 0 24px;
  }
}

/* Mobile-optimized spacing */
.mobile-spacing-xs {
  padding: 4px;
  margin: 2px;
}

.mobile-spacing-sm {
  padding: 8px;
  margin: 4px;
}

.mobile-spacing-md {
  padding: 12px;
  margin: 6px;
}

.mobile-spacing-lg {
  padding: 16px;
  margin: 8px;
}

/* Mobile-specific grid fixes */
@media (max-width: 599px) {
  .MuiGrid-container {
    margin: 0 -4px !important;
    width: calc(100% + 8px) !important;
  }
  
  .MuiGrid-item {
    padding: 4px !important;
  }
  
  /* Ensure cards don't overflow */
  .MuiCard-root {
    max-width: 100%;
    overflow: hidden;
  }
  
  /* Responsive text sizes */
  .MuiTypography-h1 {
    font-size: 1.75rem !important;
  }
  
  .MuiTypography-h2 {
    font-size: 1.5rem !important;
  }
  
  .MuiTypography-h3 {
    font-size: 1.25rem !important;
  }
  
  .MuiTypography-h4 {
    font-size: 1.125rem !important;
  }
  
  .MuiTypography-h5 {
    font-size: 1rem !important;
  }
  
  .MuiTypography-h6 {
    font-size: 0.875rem !important;
  }
  
  /* Mobile-optimized buttons */
  .MuiButton-root {
    min-height: 44px !important;
    padding: 8px 12px !important;
    font-size: 0.875rem !important;
  }
  
  .MuiButton-small {
    min-height: 36px !important;
    padding: 6px 10px !important;
    font-size: 0.8rem !important;
  }
  
  /* Mobile-optimized form fields */
  .MuiTextField-root {
    margin-bottom: 8px !important;
  }
  
  .MuiOutlinedInput-root {
    font-size: 0.875rem !important;
  }
  
  /* Mobile-optimized paper/cards */
  .MuiPaper-root {
    border-radius: 8px !important;
    margin: 4px 0 !important;
  }
  
  /* Ensure proper touch targets */
  .MuiIconButton-root {
    min-width: 44px !important;
    min-height: 44px !important;
    padding: 10px !important;
  }
  
  /* Mobile navigation fixes */
  .MuiAppBar-root {
    padding: 0 8px !important;
  }
  
  .MuiToolbar-root {
    min-height: 56px !important;
    padding: 0 8px !important;
  }
}

/* Tablet-specific adjustments */
@media (min-width: 600px) and (max-width: 959px) {
  .MuiGrid-container {
    margin: 0 -6px !important;
    width: calc(100% + 12px) !important;
  }

  .MuiGrid-item {
    padding: 6px !important;
  }

  /* Tablet table optimizations */
  .MuiTableCell-root {
    padding: 10px 6px !important;
    font-size: 0.8rem !important;
  }

  .MuiTableCell-head {
    font-size: 0.75rem !important;
    padding: 8px 6px !important;
  }

  /* Tablet DataGrid optimizations */
  .MuiDataGrid-root {
    font-size: 0.8rem !important;
  }

  .MuiDataGrid-cell {
    padding: 6px !important;
    font-size: 0.8rem !important;
  }

  .MuiDataGrid-columnHeader {
    padding: 6px !important;
    font-size: 0.75rem !important;
  }
}

/* Desktop-specific adjustments */
@media (min-width: 960px) {
  .MuiGrid-container {
    margin: 0 -8px !important;
    width: calc(100% + 16px) !important;
  }
  
  .MuiGrid-item {
    padding: 8px !important;
  }
}

/* Touch-friendly elements */
@media (hover: none) and (pointer: coarse) {
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    padding: 12px;
  }
  
  .touch-button {
    min-height: 48px;
    padding: 12px 16px;
    font-size: 1rem;
  }
  
  /* Increase spacing for touch devices */
  .touch-spacing {
    margin: 8px 0;
  }
}

/* Prevent text selection on touch devices for UI elements */
@media (hover: none) and (pointer: coarse) {
  .MuiButton-root,
  .MuiIconButton-root,
  .MuiTab-root,
  .MuiMenuItem-root {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}

/* Smooth scrolling for mobile */
@media (max-width: 599px) {
  html {
    scroll-behavior: smooth;
  }
  
  /* Optimize scroll performance */
  * {
    -webkit-overflow-scrolling: touch;
  }
}

/* Fix for sticky elements on mobile */
@media (max-width: 959px) {
  .sticky-mobile-fix {
    position: static !important;
  }
}

/* Utility classes for responsive visibility */
.mobile-only {
  display: block;
}

.tablet-up {
  display: none;
}

.desktop-up {
  display: none;
}

/* Mobile Search Overlay - Full Screen */
.mobile-search-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  max-height: none !important;
  margin: 0 !important;
  padding: 0 !important;
  background-color: rgba(0, 0, 0, 0.15) !important;
  backdrop-filter: blur(1px) !important;
  z-index: 1300 !important;
}

.mobile-search-overlay .MuiDrawer-paper {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  max-height: none !important;
  margin: 0 !important;
  padding: 0 !important;
  background-color: rgba(0, 0, 0, 0.15) !important;
  backdrop-filter: blur(1px) !important;
  transform: none !important;
  transition: none !important;
}

.mobile-search-content {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: flex-start !important;
  align-items: center !important;
  padding-top: 64px !important;
  padding-left: 16px !important;
  padding-right: 16px !important;
  pointer-events: none !important;
}

.mobile-search-box {
  width: 100% !important;
  max-width: 600px !important;
  background-color: white !important;
  border-radius: 8px !important;
  padding: 16px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
  pointer-events: all !important;
}

/* Mobile Quote Overlay - Full Screen */
.mobile-quote-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  max-height: none !important;
  margin: 0 !important;
  padding: 0 !important;
  background-color: rgba(0, 0, 0, 0.15) !important;
  backdrop-filter: blur(1px) !important;
  z-index: 1300 !important;
}

.mobile-quote-overlay .MuiDrawer-paper {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  max-height: none !important;
  margin: 0 !important;
  padding: 0 !important;
  background-color: rgba(0, 0, 0, 0.15) !important;
  backdrop-filter: blur(1px) !important;
  transform: none !important;
  transition: none !important;
}

.mobile-quote-content {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: flex-start !important;
  align-items: stretch !important;
  padding: 16px !important;
  pointer-events: none !important;
}

.mobile-quote-form-container {
  flex: 1 !important;
  background-color: white !important;
  border-radius: 12px !important;
  margin-top: 40px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
  pointer-events: all !important;
}

/* Quote overlay header responsive fixes */
@media (max-width: 480px) {
  .mobile-quote-content {
    padding: 12px !important;
  }

  .mobile-quote-form-container {
    margin-top: 30px !important;
  }
}

@media (max-width: 360px) {
  .mobile-quote-content {
    padding: 8px !important;
  }

  .mobile-quote-form-container {
    margin-top: 25px !important;
  }
}

/* Campaign Select Dropdown Fixes */
.MuiSelect-select {
  white-space: normal !important;
  word-wrap: break-word !important;
  line-height: 1.4 !important;
  padding: 14px 16px !important;
  min-height: 48px !important;
  display: flex !important;
  align-items: center !important;
}

.MuiMenuItem-root {
  white-space: normal !important;
  word-wrap: break-word !important;
  padding: 12px 16px !important;
  line-height: 1.4 !important;
  min-height: 48px !important;
  max-width: 100% !important;
  display: flex !important;
  align-items: center !important;
}

/* Mobile Quote Form Select Improvements */
@media (max-width: 768px) {
  .mobile-quote-overlay .MuiFormControl-root {
    margin-bottom: 16px !important;
    max-width: 100% !important;
  }

  .mobile-quote-overlay .MuiInputLabel-root {
    font-size: 16px !important;
    font-weight: 500 !important;
    color: #1976d2 !important;
    transform: translate(14px, 16px) scale(1) !important;
  }

  .mobile-quote-overlay .MuiInputLabel-shrink {
    transform: translate(14px, -9px) scale(0.75) !important;
    background-color: white !important;
    padding: 0 8px !important;
  }

  .mobile-quote-overlay .MuiOutlinedInput-root {
    border-radius: 12px !important;
    background-color: #fafafa !important;
    border: 2px solid #e0e0e0 !important;
    transition: all 0.3s ease !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  .mobile-quote-overlay .MuiOutlinedInput-root:hover {
    border-color: #1976d2 !important;
    background-color: #f5f5f5 !important;
  }

  .mobile-quote-overlay .MuiOutlinedInput-root.Mui-focused {
    border-color: #1976d2 !important;
    background-color: white !important;
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1) !important;
  }

  .mobile-quote-overlay .MuiSelect-select {
    padding: 16px 14px !important;
    min-height: 24px !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    color: #333 !important;
    width: 100% !important;
    max-width: 100% !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }

  .mobile-quote-overlay .MuiSelect-icon {
    color: #1976d2 !important;
    font-size: 24px !important;
    right: 12px !important;
  }

  .mobile-quote-overlay .MuiOutlinedInput-notchedOutline {
    border: none !important;
  }

  /* Dropdown Menu Improvements */
  .mobile-quote-overlay .MuiPaper-root.MuiMenu-paper {
    border-radius: 12px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
    margin-top: 8px !important;
    max-height: 300px !important;
    overflow-y: auto !important;
    max-width: calc(100vw - 32px) !important;
    left: 16px !important;
    right: 16px !important;
    width: calc(100vw - 32px) !important;
  }

  .mobile-quote-overlay .MuiMenuItem-root {
    padding: 16px 20px !important;
    min-height: 56px !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    color: #333 !important;
    border-bottom: 1px solid #f0f0f0 !important;
    transition: all 0.2s ease !important;
    white-space: normal !important;
    word-wrap: break-word !important;
    line-height: 1.4 !important;
    max-width: 100% !important;
    overflow: visible !important;
  }

  .mobile-quote-overlay .MuiMenuItem-root:last-child {
    border-bottom: none !important;
  }

  .mobile-quote-overlay .MuiMenuItem-root:hover {
    background-color: #f5f7fa !important;
    color: #1976d2 !important;
  }

  .mobile-quote-overlay .MuiMenuItem-root.Mui-selected {
    background-color: #e3f2fd !important;
    color: #1976d2 !important;
    font-weight: 600 !important;
  }

  .mobile-quote-overlay .MuiMenuItem-root.Mui-selected:hover {
    background-color: #bbdefb !important;
  }

  /* Step indicator improvements */
  .mobile-quote-overlay .MuiStepper-root {
    padding: 16px 0 !important;
  }

  .mobile-quote-overlay .MuiStepLabel-label {
    font-size: 14px !important;
    font-weight: 500 !important;
  }

  .mobile-quote-overlay .MuiStepIcon-root {
    font-size: 28px !important;
  }
}

/* Mobile specific select fixes */
@media (max-width: 480px) {
  .MuiSelect-select {
    font-size: 0.875rem !important;
    padding: 12px 14px !important;
    width: 100% !important;
    max-width: 100% !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }

  .MuiMenuItem-root {
    font-size: 0.875rem !important;
    padding: 10px 14px !important;
    white-space: normal !important;
    word-wrap: break-word !important;
    line-height: 1.4 !important;
    max-width: 100% !important;
  }

  .MuiInputLabel-root {
    font-size: 0.875rem !important;
  }

  /* Language Toggle Fixes - Both Mobile and Desktop */
  .MuiMenu-root .MuiPaper-root {
    min-width: 130px !important;
    max-width: 150px !important;
  }

  .MuiMenu-root .MuiMenuItem-root {
    padding: 12px 20px !important;
    min-height: 46px !important;
    justify-content: center !important;
  }

  .MuiMenu-root .MuiMenuItem-root .MuiTypography-body1 {
    font-size: 0.9rem !important;
    line-height: 1.2 !important;
    text-align: center !important;
  }
}

@media (min-width: 600px) {
  .mobile-only {
    display: none;
  }
  
  .tablet-up {
    display: block;
  }
}

@media (min-width: 960px) {
  .desktop-up {
    display: block;
  }
}

/* Center content on all screen sizes */
.center-content {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

/* Responsive image fixes */
.responsive-image {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Mobile-optimized loading states */
@media (max-width: 599px) {
  .MuiCircularProgress-root {
    width: 32px !important;
    height: 32px !important;
  }

  .MuiLinearProgress-root {
    height: 3px !important;
  }

  /* Mobile-optimized tables */
  .MuiTableContainer-root {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  .MuiTable-root {
    min-width: 600px !important;
  }

  .MuiTableCell-root {
    padding: 8px 4px !important;
    font-size: 0.75rem !important;
    white-space: nowrap !important;
  }

  .MuiTableCell-head {
    font-size: 0.7rem !important;
    font-weight: 600 !important;
    padding: 6px 4px !important;
  }

  /* DataGrid mobile optimizations */
  .MuiDataGrid-root {
    font-size: 0.75rem !important;
  }

  .MuiDataGrid-cell {
    padding: 4px !important;
    font-size: 0.75rem !important;
  }

  .MuiDataGrid-columnHeader {
    padding: 4px !important;
    font-size: 0.7rem !important;
  }

  .MuiDataGrid-columnHeaderTitle {
    font-weight: 600 !important;
  }

  /* Mobile chatbot optimizations */
  .chatbot-container {
    bottom: 16px !important;
    right: 16px !important;
    width: calc(100vw - 32px) !important;
    max-width: 350px !important;
  }

  .chatbot-button {
    width: 48px !important;
    height: 48px !important;
    bottom: 16px !important;
    right: 16px !important;
  }

  .chatbot-messages {
    max-height: 300px !important;
    font-size: 0.875rem !important;
  }

  .chatbot-input {
    font-size: 0.875rem !important;
    padding: 8px 12px !important;
  }

  /* Mobile modal and dialog optimizations */
  .MuiDialog-paper {
    margin: 16px !important;
    width: calc(100vw - 32px) !important;
    max-width: calc(100vw - 32px) !important;
    max-height: calc(100vh - 64px) !important;
  }

  .MuiDialogTitle-root {
    padding: 16px !important;
    font-size: 1.125rem !important;
  }

  .MuiDialogContent-root {
    padding: 8px 16px !important;
  }

  .MuiDialogActions-root {
    padding: 8px 16px 16px !important;
  }

  /* Mobile drawer optimizations */
  .MuiDrawer-paper {
    width: calc(100vw - 56px) !important;
    max-width: 280px !important;
  }

  /* Mobile menu optimizations */
  .MuiMenu-paper {
    max-width: calc(100vw - 32px) !important;
    margin: 16px !important;
  }

  .MuiMenuItem-root {
    padding: 12px 16px !important;
    font-size: 0.875rem !important;
    min-height: 44px !important;
  }

  /* Mobile snackbar and alert optimizations */
  .MuiSnackbar-root {
    left: 16px !important;
    right: 16px !important;
    bottom: 16px !important;
    width: calc(100vw - 32px) !important;
  }

  .MuiAlert-root {
    font-size: 0.875rem !important;
    padding: 8px 12px !important;
  }

  .MuiAlert-message {
    padding: 4px 0 !important;
  }

  .MuiAlert-action {
    padding: 0 0 0 8px !important;
  }

  /* Mobile fab optimizations */
  .MuiFab-root {
    width: 48px !important;
    height: 48px !important;
    bottom: 16px !important;
    right: 16px !important;
  }

  .MuiFab-extended {
    height: 44px !important;
    padding: 0 16px !important;
    font-size: 0.875rem !important;
  }
}
