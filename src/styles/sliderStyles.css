/* Custom Slider Styles */
.slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  user-select: none;
  touch-action: pan-y;
}

.slick-list {
  position: relative;
  display: block;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.slick-slide {
  float: left;
  height: 100%;
  min-height: 1px;
  padding: 0 10px;
  box-sizing: border-box;
}

.slick-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  display: flex !important;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.slick-arrow:hover {
  background-color: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.slick-arrow.slick-prev {
  left: -20px;
}

.slick-arrow.slick-next {
  right: -20px;
}

.slick-dots {
  position: absolute;
  bottom: -30px;
  display: flex !important;
  justify-content: center;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
}

.slick-dots li {
  margin: 0 5px;
}

.slick-dots li button {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ccc;
  border: none;
  font-size: 0;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.slick-dots li.slick-active button {
  background-color: #1976d2;
  width: 10px;
  height: 10px;
}

/* Custom styles for campaign cards in slider */
.campaign-slider .slick-track {
  display: flex;
  padding: 10px 0;
}

.campaign-slider .slick-slide {
  height: auto;
}

.campaign-slider .slick-slide > div {
  height: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .slick-arrow.slick-prev {
    left: 10px;
  }
  
  .slick-arrow.slick-next {
    right: 10px;
  }
  
  .slick-slide {
    padding: 0 5px;
  }
}
