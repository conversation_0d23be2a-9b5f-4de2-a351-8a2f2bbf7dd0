import React, {
  create<PERSON>ontext,
  useContext,
  useState,
  useEffect,
  use<PERSON><PERSON>back,
  ReactNode,
} from "react";
import api from "../utils/api";

// JWT token decode helper
const decodeJWT = (token: string) => {
  try {
    const base64Url = token.split(".")[1];
    const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split("")
        .map(function (c) {
          return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
        })
        .join("")
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error("JWT decode error:", error);
    return null;
  }
};

// Get token from cookie
const getTokenFromCookie = () => {
  try {
    const cookies = document.cookie.split(";");
    
    for (let cookie of cookies) {
      const [name, value] = cookie.trim().split("=");
      if (name === "accessToken" && value && value !== 'undefined' && value !== 'null') {
        return value;
      }
    }
  } catch (error) {
    console.warn('Error reading cookies:', error);
  }
  
  return null;
};

// Define the user structure
export interface User {
  id?: number;
  username: string;
  role: "ADMIN" | "USER" | "CRM_ADMIN";
}

// Define the auth context type
interface AuthContextType {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  loginError: string | null;
  login: (
    username: string,
    password: string,
    rememberMe?: boolean
  ) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
  checkAuthStatus: () => Promise<void>;
}

// Create the context with default values
const AuthContext = createContext<AuthContextType>({
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: true,
  loginError: null,
  login: async () => {},
  logout: async () => {},
  updateUser: () => {},
  checkAuthStatus: async () => {},
});

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Auth provider component
export const AuthProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [loginError, setLoginError] = useState<string | null>(null);

  const checkAuthStatus = useCallback(async () => {
    setIsLoading(true);
    try {
      // Cookie'den token'ı al ve decode et
      const token = getTokenFromCookie();
      
      if (token) {
        const decodedToken = decodeJWT(token);
        
        if (decodedToken && decodedToken.exp > Date.now() / 1000) {
          // Token geçerli, kullanıcı bilgilerini set et
          setToken(token);
          setUser({
            id: decodedToken.id,
            username: decodedToken.sub,
            role: decodedToken.roles as "ADMIN" | "USER" | "CRM_ADMIN",
          });
          return;
        } else {
          console.warn('Token expired or invalid');
        }
      } else {
        console.log('No token found in cookies');
      }

      // Token yoksa veya geçersizse kullanıcıyı logout et
      setUser(null);
      setToken(null);
    } catch (error) {
      console.error('Auth status check error:', error);
      setUser(null);
      setToken(null);
    } finally {
      setIsLoading(false);
    }
  }, []);



  // Login function
  const login = async (
    username: string,
    password: string,
    rememberMe = true
  ) => {
    setIsLoading(true);
    setLoginError(null);

    try {
      // Login isteği gönder - backend HttpOnly cookie'leri set edecek
      const loginResponse = await api.post("/api/Auth_Service/auth/login", {
        username,
        password,
        rememberMe, // Backend bu bilgiyi cookie max-age için kullanabilir
      });

     
      // Login response'unda token varsa onu kullan
      if (loginResponse.data?.accessToken) {
        const accessToken = loginResponse.data.accessToken;
        setToken(accessToken);

        const decodedToken = decodeJWT(accessToken);
        if (decodedToken) {
          // Manuel cookie set et (backend set etmiyorsa)
          const cookieOptions = [
            `accessToken=${accessToken}`,
            'path=/',
            'max-age=900',
            'SameSite=Strict' // Canlı ortam için daha güvenli
          ];
          
          // HTTPS kullanılıyorsa Secure flag ekle
          if (window.location.protocol === 'https:') {
            cookieOptions.push('Secure');
            cookieOptions.push('HttpOnly=false'); // JavaScript erişimi için
          }
          
          document.cookie = cookieOptions.join('; ');

          setUser({
            id: decodedToken.id,
            username: decodedToken.sub,
            role: decodedToken.roles as "ADMIN" | "USER" | "CRM_ADMIN",
          });
          setIsLoading(false);
        } else {
          setUser(null);
          setToken(null);
          setLoginError("Token decode edilemedi.");
          setIsLoading(false);
        }
      } else {
        // Response'da token yoksa, cookie'den almaya çalış
        const token = getTokenFromCookie();
        if (token) {
          const decodedToken = decodeJWT(token);
          if (decodedToken) {
            setUser({
              id: decodedToken.id,
              username: decodedToken.sub,
              role: decodedToken.roles as "ADMIN" | "USER" | "CRM_ADMIN",
            });
            setToken(token);
            setIsLoading(false);
          } else {
            setUser(null);
            setToken(null);
            setLoginError("Token decode edilemedi.");
            setIsLoading(false);
          }
        } else {
          // Kullanıcı bilgilerini login response'undan alalım
          if (loginResponse.data?.user) {
            setUser({
              id: loginResponse.data.user.id,
              username: loginResponse.data.user.username || username,
              role: loginResponse.data.user.role as "ADMIN" | "USER" | "CRM_ADMIN",
            });
            setIsLoading(false);
          } else {
            setUser(null);
            setToken(null);
            setLoginError("Login başarılı ama kullanıcı bilgileri alınamadı.");
            setIsLoading(false);
          }
        }
      }

      // Kullanıcı adını hatırlama (localStorage hala kullanılabilir, token olmadığı için XSS riski daha az)
      if (rememberMe) {
        localStorage.setItem("rememberedUsername", username);
      } else {
        localStorage.removeItem("rememberedUsername");
        // sessionStorage.setItem('rememberedUsername', username); // Oturum boyunca hatırlama için gerekirse
      }
    } catch (err: any) {
      setUser(null);
      setIsLoading(false);
      // Prioritize using the message from the backend response data if available
      if (err.response?.data?.message) {
        setLoginError(err.response.data.message);
      } else if (err.response?.status === 401) {
        // Fallback for 401 if no specific message from backend (e.g. network error or non-standard 401)
        // The backend now provides a message for 401s, so this path is less likely for login failures.
        setLoginError(
          "Kullanıcı adı veya şifre hatalı. Lütfen bilgilerinizi kontrol edin."
        );
      } else {
        // Generic fallback for other types of errors or if no specific message is available
        setLoginError(
          "Giriş sırasında bir hata oluştu. Lütfen tekrar deneyin."
        );
      }
    }
  };

  // Logout function
  const logout = async () => {
    setIsLoading(true);
    try {
      // Backend'den cookie'leri temizlemesini iste
      await api.post("/api/Auth_Service/auth/logout", {});
    } catch (error) {
      // Hata olsa bile frontend'de kullanıcı state'ini temizle
      if (process.env.NODE_ENV === "development") {
        console.warn("Logout request failed:", error);
      }
    } finally {
      // Frontend'de cookie'yi temizle - canlı ortam için daha kapsamlı
      const cookieOptions = [
        'accessToken=',
        'path=/',
        'expires=Thu, 01 Jan 1970 00:00:00 GMT',
        'SameSite=Strict'
      ];
      
      if (window.location.protocol === 'https:') {
        cookieOptions.push('Secure');
      }
      
      document.cookie = cookieOptions.join('; ');
      
      setUser(null);
      setToken(null);
      localStorage.removeItem("rememberedUsername");
      setIsLoading(false);
    }
  };

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      setUser({ ...user, ...userData });
    }
  };

  // Sayfa yenilendiğinde auth durumunu kontrol et ve logout event'ini dinle
  useEffect(() => {
    
    checkAuthStatus();

    // API interceptor'dan gelen logout event'ini dinle
    const handleLogout = () => {
      // Frontend'de cookie'yi temizle - canlı ortam için daha kapsamlı
      const cookieOptions = [
        'accessToken=',
        'path=/',
        'expires=Thu, 01 Jan 1970 00:00:00 GMT',
        'SameSite=Strict'
      ];
      
      if (window.location.protocol === 'https:') {
        cookieOptions.push('Secure');
      }
      
      document.cookie = cookieOptions.join('; ');
      
      setUser(null);
      setToken(null);
      localStorage.removeItem("rememberedUsername");
    };

    window.addEventListener('auth:logout', handleLogout);
    return () => window.removeEventListener('auth:logout', handleLogout);
  }, [checkAuthStatus]); // checkAuthStatus dependency eklendi

  return (
    <AuthContext.Provider
      value={{
        user,
        token,
        isAuthenticated: !!user,
        isLoading,
        loginError,
        login,
        logout,
        updateUser,
        checkAuthStatus,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
