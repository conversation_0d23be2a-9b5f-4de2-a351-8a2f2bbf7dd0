import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import axios from 'axios';
import { isExpired } from '../utils/dateUtils';
import { getAdvancedSmartIcon } from '../utils/iconMatcher';

// Define the category structure based on the API response
export interface Category {
  id: number;
  name: string;
  isActive: boolean;
  parentCategoryId: number | null;
  createdAt: string;
  updatedAt: string | null;
}

// Define campaign structure for type safety
interface Campaign {
  id: number;
  isActive: boolean;
  endDate?: string;
  category?: {
    id: string;
  } | string;
}

// Extended category with additional UI properties
export interface CategoryWithUI extends Category {
  icon: React.ReactElement;
  children: CategoryWithUI[];
  level: number;
}

// Define a category group structure similar to the mock data
export interface CategoryGroup {
  title: string;
  icon: string;
  categories: CategoryWithUI[];
}

interface CategoryContextType {
  allCategories: Category[];
  categoryGroups: Record<string, CategoryGroup>;
  categoryTree: CategoryWithUI[];
  flattenedCategories: CategoryWithUI[];
  loading: boolean;
  error: string | null;
  getCategoryById: (id: number) => CategoryWithUI | undefined;
  getCategoryPath: (id: number) => CategoryWithUI[];
  refreshCategories: () => Promise<void>;
}

const CategoryContext = createContext<CategoryContextType>({
  allCategories: [],
  categoryGroups: {},
  categoryTree: [],
  flattenedCategories: [],
  loading: false,
  error: null,
  getCategoryById: () => undefined,
  getCategoryPath: () => [],
  refreshCategories: async () => {},
});

export const useCategories = () => useContext(CategoryContext);

interface CategoryProviderProps {
  children: ReactNode;
}

// Map parent categories to icons and titles
const categoryGroupMappings: Record<number, { title: string; icon: string }> = {

  // Add more mappings as needed
};

// Legacy smart icon matching system (kept for fallback)
const getSmartIcon = (categoryName: string): React.ReactElement => {
  // Use the new advanced icon matcher
  return getAdvancedSmartIcon(categoryName);
};

export const CategoryProvider: React.FC<CategoryProviderProps> = ({ children }) => {
  const [allCategories, setAllCategories] = useState<Category[]>([]);
  const [categoryTree, setCategoryTree] = useState<CategoryWithUI[]>([]);
  const [flattenedCategories, setFlattenedCategories] = useState<CategoryWithUI[]>([]);
  const [categoryGroups, setCategoryGroups] = useState<Record<string, CategoryGroup>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Helper function to get category by ID
  const getCategoryById = (id: number): CategoryWithUI | undefined => {
    // Recursive function to search through the category tree
    const findCategory = (categories: CategoryWithUI[]): CategoryWithUI | undefined => {
      for (const category of categories) {
        if (category.id === id) {
          return category;
        }

        if (category.children.length > 0) {
          const found = findCategory(category.children);
          if (found) return found;
        }
      }
      return undefined;
    };

    return findCategory(categoryTree);
  };

  // Helper function to get the full path to a category
  const getCategoryPath = (id: number): CategoryWithUI[] => {
    const path: CategoryWithUI[] = [];

    // Recursive function to build the path
    const buildPath = (categories: CategoryWithUI[], targetId: number): boolean => {
      for (const category of categories) {
        if (category.id === targetId) {
          path.push(category);
          return true;
        }

        if (category.children.length > 0) {
          if (buildPath(category.children, targetId)) {
            path.unshift(category);
            return true;
          }
        }
      }
      return false;
    };

    buildPath(categoryTree, id);
    return path;
  };

  // Build a hierarchical tree from flat category list
  const buildCategoryTree = (categories: Category[]): { tree: CategoryWithUI[], flattened: CategoryWithUI[] } => {
    // First, create a map of all categories with their UI properties
    const categoryMap: Record<number, CategoryWithUI> = {};
    const flattenedList: CategoryWithUI[] = [];

    categories.forEach(cat => {
      const categoryWithUI = {
        ...cat,
        icon: getSmartIcon(cat.name),
        children: [],
        level: 0 // Will be calculated later
      };

      categoryMap[cat.id] = categoryWithUI;
    });

    // Then build the tree structure
    const rootCategories: CategoryWithUI[] = [];

    categories.forEach(cat => {
      const categoryWithUI = categoryMap[cat.id];

      if (cat.parentCategoryId === null || cat.parentCategoryId === 0) {
        // This is a root category
        rootCategories.push(categoryWithUI);
      } else if (categoryMap[cat.parentCategoryId]) {
        // This is a child category, add it to its parent
        categoryMap[cat.parentCategoryId].children.push(categoryWithUI);
      }
    });

    // Calculate levels (depth in the tree) and build flattened list
    const calculateLevelsAndFlatten = (categories: CategoryWithUI[], level: number) => {
      categories.forEach(cat => {
        cat.level = level;

        // Add to flattened list with original name
        flattenedList.push({...cat});

        if (cat.children.length > 0) {
          calculateLevelsAndFlatten(cat.children, level + 1);
        }
      });
    };

    calculateLevelsAndFlatten(rootCategories, 0);

    return { tree: rootCategories, flattened: flattenedList };
  };

  const fetchCategories = async () => {
      try {
        setLoading(true);

        // Tüm gerekli verileri çekelim
        const [categoryResponse, campaignResponse] = await Promise.all([
          axios.get('https://360avantajli.com/api/Campaign_Service/category'),
          axios.get('https://360avantajli.com/api/Campaign_Service/campaign')
        ]);

        if (categoryResponse.data && Array.isArray(categoryResponse.data) &&
            campaignResponse.data && Array.isArray(campaignResponse.data)) {

          // Aktif ve süresi dolmamış kampanyaları filtrele
          const activeCampaigns = campaignResponse.data.filter((campaign: Campaign) => {
            // Aktif olup olmadığını kontrol et
            if (campaign.isActive !== true) {
              return false;
            }

            // Süresi geçmiş kampanyaları filtrele
            if (campaign.endDate) {
              return !isExpired(campaign.endDate);
            }

            return true; // Bitiş tarihi yoksa varsayılan olarak göster
          });

          // Aktif kampanyaların kategori ID'lerini topla
          const activeCampaignCategoryIds = new Set<number>();

          activeCampaigns.forEach((campaign: Campaign) => {
            let categoryId: number | null = null;

            // Kampanyanın kategori ID'sini al
            if (typeof campaign.category === 'object' && campaign.category?.id) {
              categoryId = parseInt(campaign.category.id);
            } else if (typeof campaign.category === 'string') {
              categoryId = parseInt(campaign.category);
            }

            if (categoryId && !isNaN(categoryId)) {
              activeCampaignCategoryIds.add(categoryId);
            }
          });

          // Aktif kategorileri filtrele
          const activeCategories = categoryResponse.data.filter((cat: Category) => cat.isActive);

          // Aktif kampanyası olan kategorileri ve onların üst kategorilerini bul
          const categoriesWithActiveCampaigns = new Set<number>();

          // Tüm ana kategorileri ekle (parentCategoryId = 0)
          const mainCategories = activeCategories.filter((cat: Category) => cat.parentCategoryId === 0);
          mainCategories.forEach(cat => {
            categoriesWithActiveCampaigns.add(cat.id);
          });

          // Bir kategorinin tüm üst kategorilerini bul
          const findAllParentCategories = (categoryId: number) => {
            const category = activeCategories.find((cat: Category) => cat.id === categoryId);
            if (category && category.parentCategoryId && category.parentCategoryId !== 0) {
              categoriesWithActiveCampaigns.add(category.parentCategoryId);
              findAllParentCategories(category.parentCategoryId);
            }
          };

          // Aktif kampanyası olan kategorileri ve onların üst kategorilerini ekle
          activeCampaignCategoryIds.forEach(categoryId => {
            categoriesWithActiveCampaigns.add(categoryId);
            findAllParentCategories(categoryId);
          });

          // Sadece aktif kampanyası olan kategorileri, ana kategorileri veya onların üst kategorilerini filtrele
          const filteredCategories = activeCategories.filter((cat: Category) =>
            categoriesWithActiveCampaigns.has(cat.id)
          );

          setAllCategories(filteredCategories);

          // Build the category tree and flattened list
          const { tree, flattened } = buildCategoryTree(filteredCategories);
          setCategoryTree(tree);
          setFlattenedCategories(flattened);

          // Organize categories into groups for the UI
          const groups: Record<string, CategoryGroup> = {};

          // Use top-level categories as groups
          tree.forEach((rootCategory: CategoryWithUI) => {
            const groupKey = `group_${rootCategory.id}`;
            const mapping = categoryGroupMappings[rootCategory.id] || {
              title: rootCategory.name,
              icon: rootCategory.icon
            };

            groups[groupKey] = {
              title: mapping.title,
              icon: mapping.icon,
              categories: [
                // Include all direct children
                ...rootCategory.children
              ]
            };
          });

          setCategoryGroups(groups);
        }
      } catch (err) {
        // eslint-disable-next-line no-console
        console.error('Error fetching categories:', err);
        setError('Failed to load categories');
      } finally {
        setLoading(false);
      }
    };

  const refreshCategories = async () => {
    await fetchCategories();
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  return (
    <CategoryContext.Provider value={{
      allCategories,
      categoryGroups,
      categoryTree,
      flattenedCategories,
      loading,
      error,
      getCategoryById,
      getCategoryPath,
      refreshCategories
    }}>
      {children}
    </CategoryContext.Provider>
  );
};