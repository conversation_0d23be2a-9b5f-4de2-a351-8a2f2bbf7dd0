import axios from 'axios';

export interface Part {
  text: string;
  // Add other part types (e.g., inlineData) as needed
}

export interface GeminiMessage {
  role: 'user' | 'model' | 'system';
  parts: Part[];
}

interface SafetySetting {
  category: string;
  threshold: string;
}

interface GenerateContentRequest {
  contents: GeminiMessage[];
  safetySettings?: SafetySetting[];
  generationConfig?: {
    temperature?: number;
    topP?: number;
    topK?: number;
    maxOutputTokens?: number;
  };
}

// Use Vite's import.meta.env for environment variables
const GEMINI_API_URL = import.meta.env.VITE_GEMINI_PROXY_URL;
const GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY;

export interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Part[];
      role: string;
    };
    finishReason: string;
    index: number;
    safetyRatings: Array<{
      category: string;
      probability: string;
    }>;
  }>;
  usageMetadata?: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
}

export const generateContent = async (contents: GeminiMessage[]): Promise<GeminiResponse> => {
  const safetySettings: SafetySetting[] = [
    { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_MEDIUM_AND_ABOVE" },
    { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_MEDIUM_AND_ABOVE" },
    { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_MEDIUM_AND_ABOVE" },
    { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_MEDIUM_AND_ABOVE" },
  ];

  const requestData: GenerateContentRequest = {
    contents,
    safetySettings,
  };

  const response = await axios.post(
    `${GEMINI_API_URL}?key=${GEMINI_API_KEY}`,
    requestData,
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
  return response.data;
};
