import axios from 'axios';
import crmService from './crmService';

const API_BASE_URL = 'https://360avantajli.com/api';

// Arayüz Tanımları
interface Contact {
  name: string;
  surname: string;
  email: string;
  phoneNumber: string;
  campaignName?: string;
  brandName?: string;
  createdAt: string;
}

interface WebhookEvent {
  number: string;
  webhookTimestamp: string;
  duration: number;
  endReason: string;
  pipeline?: string;
  callId: string;
  event: string;
}

// Yeni interface - contacts-webhook-join endpoint'inden gelen veri
interface JoinedContactData {
  name: string;
  surname: string;
  email: string;
  phone_number: string;
  campaign_name?: string;
  status: string;
  brand: string;
  call_id: string;
  event: string;
  conversation: string;
  analysis: string;
  created_at: string;
  endReason?: string;
  pipeline?: string;
  duration?: number; // duration alanını ekle
}

export interface FunnelData {
  id: string; // Telefon numarası
  customerName: string;
  email: string;
  sourceCampaign?: string;
  sourceBrand?: string;
  leadCreatedAt: string; // Müşterinin sisteme ilk giriş tarihi
  callId?: string;
  callEvent?: string;
  callTimestamp?: string;
  callDuration?: number;
  callOutcome?: string;
  hasBeenCalled: boolean;
  // Yeni alanlar - CRM endpoint eşleştirmesi için
  crmEndpointStatus?: string;
  analysis?: string;
  conversation?: string;
  pipeline?: string;
}

class AnalyticsService {
  private async fetchData<T>(endpoint: string, token?: string): Promise<T[]> {
    try {
      const headers = token ? { 'Authorization': `Bearer ${token}` } : {};
      const response = await axios.get(`${API_BASE_URL}/${endpoint}`, { headers });
      return (Array.isArray(response.data) ? response.data : (response.data.content || response.data.data)) || [];
    } catch (error) {
      console.error(`Error fetching data from ${endpoint}:`, error);
      return [];
    }
  }

  // Eski method - mevcut implementasyon
  public async getComprehensiveData(token: string): Promise<FunnelData[]> {
    const [contacts, webhookEvents] = await Promise.all([
      this.fetchData<Contact>('Campaign_Service/crm-admin/contacts', token),
      this.fetchData<WebhookEvent>('Campaign_Service/crm-admin/webhook-events', token),
    ]);

    const webhookMap = new Map<string, WebhookEvent>();
    webhookEvents.forEach(event => {
        // Telefon numarasından +90'ı kaldırarak normalize et
        const normalizedPhone = event.number.startsWith('+90') ? event.number.substring(3) : event.number;
        webhookMap.set(normalizedPhone, event);
    });

    const funnelData = contacts.map(contact => {
      const webhook = contact.phoneNumber ? webhookMap.get(contact.phoneNumber) : undefined;

      // Her satır için benzersiz ve null olmayan bir ID oluştur
      const uniqueId = contact.phoneNumber || contact.email || `contact-${contact.createdAt}-${Math.random()}`;

      return {
        id: uniqueId,
        customerName: `${contact.name} ${contact.surname}`,
        email: contact.email,
        sourceCampaign: contact.campaignName,
        sourceBrand: contact.brandName,
        leadCreatedAt: contact.createdAt,
        callId: webhook?.callId,
        callEvent: webhook?.event,
        callTimestamp: webhook?.webhookTimestamp,
        callDuration: webhook?.duration,
        callOutcome: webhook?.endReason,
        hasBeenCalled: !!webhook,
      };
    });

    return funnelData;
  }

  // Yeni method - contacts-webhook-join endpoint'i kullanarak ve CRM endpoint'leriyle eşleştirerek
  public async getComprehensiveDataFromJoin(token: string): Promise<FunnelData[]> {
    try {
      const headers = token ? { 'Authorization': `Bearer ${token}` } : {};
      
      // contacts-webhook-join endpoint'inden veri çek (CrmContactsWebhookJoinPage.tsx mantığı)
      const [contactsRes, webhooksRes, crmEndpoints] = await Promise.all([
        axios.get(`${API_BASE_URL}/Campaign_Service/crm-admin/contacts`, { headers }),
        axios.get(`${API_BASE_URL}/Campaign_Service/crm-admin/webhook-events`, { headers }),
        crmService.getEndpoints(), // Admin panelindeki endpoint'leri al
      ]);

      const contactsData = Array.isArray(contactsRes.data)
        ? contactsRes.data
        : contactsRes.data.content || contactsRes.data.data || [];
      
      const webhooksData: WebhookEvent[] = Array.isArray(webhooksRes.data)
        ? webhooksRes.data
        : webhooksRes.data.content || webhooksRes.data.data || [];

      // CRM endpoint durumlarını map'e çevir
      const endpointStatusMap = new Map<string, string>();
      crmEndpoints.forEach(endpoint => {
        endpointStatusMap.set(endpoint.id, endpoint.status);
      });

      // Telefon numarasına göre webhook event'leri map'le (daha verimli)
      const webhookMap = new Map<string, WebhookEvent>();
      webhooksData.forEach(event => {
        const normalizedPhone = (event.number || '').replace(/\s/g, '').replace(/^\+90/, '');
        if (normalizedPhone) {
          webhookMap.set(normalizedPhone, event);
        }
      });

      // Joined data oluştur
      const joinedData: JoinedContactData[] = [];
      contactsData.forEach((contact: Contact) => {
        const normalizedContactPhone = (contact.phoneNumber || '').replace(/\s/g, '');
        const webhook = webhookMap.get(normalizedContactPhone);

        if (webhook) {
          joinedData.push({
            name: contact.name,
            surname: contact.surname,
            email: contact.email,
            phone_number: contact.phoneNumber,
            campaign_name: contact.campaignName,
            status: 'Active', // Default status
            brand: contact.brandName || '',
            call_id: webhook.callId,
            event: webhook.event,
            conversation: '',
            analysis: '',
            created_at: webhook.webhookTimestamp,
            endReason: webhook.endReason,
            pipeline: webhook.pipeline,
            duration: webhook.duration,
          });
        }
      });

      // FunnelData formatına çevir ve CRM endpoint bilgilerini ekle
      const funnelData: FunnelData[] = joinedData.map(data => {
        const uniqueId = data.phone_number || data.email || `joined-${data.created_at}-${Math.random()}`;
        
        // CRM endpoint durumunu belirle (calls endpoint'i için)
        const callsEndpointStatus = endpointStatusMap.get('calls') || 'unknown';
        
        return {
          id: uniqueId,
          customerName: `${data.name} ${data.surname}`,
          email: data.email,
          sourceCampaign: data.campaign_name,
          sourceBrand: data.brand,
          leadCreatedAt: data.created_at,
          callId: data.call_id,
          callEvent: data.event,
          callTimestamp: data.created_at,
          callDuration: data.duration ? Math.round(data.duration / 1000) : 0, // Milisaniyeyi saniyeye çevir
          callOutcome: data.endReason,
          hasBeenCalled: true, // Join edilen veriler zaten aranmış olanlar
          crmEndpointStatus: callsEndpointStatus,
          analysis: data.analysis,
          conversation: data.conversation,
          pipeline: data.pipeline,
        };
      });

      // Aranmamış contact'ları da ekle
      const calledPhones = new Set(joinedData.map(j => j.phone_number));
      const uncalledContacts = contactsData.filter((contact: Contact) => 
        !calledPhones.has(contact.phoneNumber)
      );

      uncalledContacts.forEach((contact: Contact) => {
        const uniqueId = contact.phoneNumber || contact.email || `uncalled-${contact.createdAt}-${Math.random()}`;
        
        // Contacts endpoint durumu
        const contactsEndpointStatus = endpointStatusMap.get('contacts') || 'unknown';
        
        funnelData.push({
          id: uniqueId,
          customerName: `${contact.name} ${contact.surname}`,
          email: contact.email,
          sourceCampaign: contact.campaignName,
          sourceBrand: contact.brandName,
          leadCreatedAt: contact.createdAt,
          hasBeenCalled: false,
          crmEndpointStatus: contactsEndpointStatus,
        });
      });

      return funnelData;
    } catch (error) {
      console.error('Error fetching comprehensive data from join:', error);
      // Fallback to old method
      return this.getComprehensiveData(token);
    }
  }
}

export default new AnalyticsService(); 