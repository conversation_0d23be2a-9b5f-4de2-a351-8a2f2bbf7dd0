import api from '../utils/api';

interface FormData {
  name: string;
  surname: string;
  firstName?: string;
  lastName?: string;
  email: string;
  phoneNumber: string;
  country: string;
  city: string;
  town: string;
  gender: string;
  birthday: string;
  isActive?: boolean;
  contact_email?: string; // Frontend'de contact_email olarak kalacak
  contact_phone?: string; // Frontend'de contact_phone olarak kalacak
}

interface FormToCampaignData {
  formId: number;
  campaignId: number;
  isActive: boolean;
  contact_email?: string; // Frontend'de contact_email olarak kalacak
  contact_phone?: string; // Frontend'de contact_phone olarak kalacak
}

export const formService = {
  // Kullanıcının kampanya için daha önce form gönderip göndermediğini kontrol et
  checkExistingForm: async (email: string, campaignId: number) => {
    try {
      const response = await api.get('/api/Campaign_Service/form-to-campaign');

      if (response.data && Array.isArray(response.data)) {
        const existingForm = response.data.find((formCampaign: any) => {
          const match = formCampaign.campaign?.id === campaignId &&
                       formCampaign.form?.email === email &&
                       formCampaign.isActive;

          return match;
        });

        return !!existingForm;
      }
      return false;
    } catch (error) {
      // API çağrısında hata olursa, mevcut form olmadığını varsayabiliriz.
      // console.error('checkExistingForm API hatası:', error);
      return false;
    }
  },

  // Yeni: Public endpoint ile duplicate kontrolü
  checkDuplicateSubmission: async (email: string, campaignId: number) => {
    try {
      const response = await api.get(`/api/Campaign_Service/public/form-submission/check-duplicate?email=${encodeURIComponent(email)}&campaignId=${campaignId}`);
      
      if (response.data && response.data.isDuplicate) {
        return true;
      }
      return false;
    } catch (error) {
      console.error('Duplicate check error:', error);
      // Hata durumunda false döndür (duplicate değil varsay)
      return false;
    }
  },

  // Form kaydetme
  createForm: async (formData: FormData) => {
    // Önce mevcut formları kontrol et
    const response = await api.get('/api/Campaign_Service/form');
    if (response.data && Array.isArray(response.data)) {
      const existingForm = response.data.find((form: any) =>
        form.email === formData.email && form.isActive
      );

      if (existingForm) {
        return existingForm; // Mevcut formu döndür
      }
    }

    // Eğer form yoksa yeni form oluştur - PUBLIC ENDPOINT
    const createResponse = await api.post('/api/Campaign_Service/public/form-submission/form', {
      ...formData,
      isActive: true
    });
    return createResponse.data;
  },

  // Form ve kampanya ilişkisi oluşturma
  createFormToCampaign: async (data: FormToCampaignData) => {
    // Önce mevcut ilişkiyi kontrol et
    const response = await api.get('/api/Campaign_Service/form-to-campaign');
    if (response.data && Array.isArray(response.data)) {
      const existingRelation = response.data.find((relation: any) =>
        relation.form?.id === data.formId &&
        relation.campaign?.id === data.campaignId &&
        relation.isActive
      );

      if (existingRelation) {
        return existingRelation; // Mevcut ilişkiyi döndür
      }
    }

    // Eğer ilişki yoksa yeni ilişki oluştur - PUBLIC ENDPOINT
    const createResponse = await api.post('/api/Campaign_Service/public/form-submission/form-to-campaign', {
      formId: data.formId,
      campaignId: data.campaignId,
      isActive: true,
      contactEmail: data.contact_email, // Backend'e contactEmail olarak gönder
      contactPhone: data.contact_phone  // Backend'e contactPhone olarak gönder
    });
    return createResponse.data;
  },

  // Form ve kampanya işlemlerini sırayla yapma
  submitFormAndCreateCampaignRelation: async (formData: FormData, campaignId: number) => {
    // Anonim kullanıcı için public endpoint kullan
    const response = await api.post('/api/Campaign_Service/public/form-submission/anonymous', {
      name: formData.name,
      surname: formData.surname,
      email: formData.email,
      phoneNumber: formData.phoneNumber,
      country: formData.country,
      city: formData.city,
      town: formData.town,
      gender: formData.gender,
      birthday: formData.birthday,
      campaignId: campaignId,
      contactEmail: formData.contact_email || formData.email,
      contactPhone: formData.contact_phone || formData.phoneNumber
    });

    return {
      form: { id: response.data.formId },
      formToCampaign: { id: response.data.relationId }
    };
  },

  // Customer ve kampanya ilişkisi oluşturma
  createCustomerToCampaign: async (data: { 
    customerId: number; 
    campaignId: number; 
    isActive: boolean;
    contact_email?: string;
    contact_phone?: string;
    contact_name?: string;
    contact_surname?: string;
  }) => {
    // Önce mevcut ilişkiyi kontrol et
    const response = await api.get('/api/Campaign_Service/customer-to-campaign');
    if (response.data && Array.isArray(response.data)) {
      const existingRelation = response.data.find((relation: any) =>
        relation.customer?.id === data.customerId &&
        relation.campaign?.id === data.campaignId &&
        relation.isActive
      );
      if (existingRelation) {
        return existingRelation;
      }
    }
    // Eğer ilişki yoksa yeni ilişki oluştur - PUBLIC ENDPOINT
    const createResponse = await api.post('/api/Campaign_Service/public/form-submission/customer-to-campaign', {
      customerId: data.customerId,
      campaignId: data.campaignId,
      isActive: true,
      contactEmail: data.contact_email, // Backend'e contactEmail olarak gönder
      contactPhone: data.contact_phone,  // Backend'e contactPhone olarak gönder
      contactName: data.contact_name,    // Backend'e contactName olarak gönder
      contactSurname: data.contact_surname // Backend'e contactSurname olarak gönder
    });
    return createResponse.data;
  },

  // Kullanıcıdan customerId bulma
  findCustomerIdByUsername: async (username: string) => {
    try {
      // Önce kullanıcının kendi profilini al
      const response = await api.get('/api/Auth_Service/customer/profile');
      if (response.data && response.data.id) {
        return response.data.id;
      }
    } catch (error) {
      console.warn('Profile endpoint failed, trying public endpoint:', error);
      // Fallback: Public endpoint kullan
      try {
        const publicResponse = await api.get(`/api/Auth_Service/public/customer/find-id?username=${username}`);
        if (publicResponse.data && publicResponse.data.found) {
          return publicResponse.data.customerId;
        }
      } catch (publicError) {
        console.error('Public endpoint also failed:', publicError);
      }
    }
    return null;
  },

  // Giriş durumuna göre uygun endpointi kullanan yeni submit fonksiyonu
  submitFormAndCreateCampaignRelationV2: async (
    formData: FormData,
    campaignId: number,
    isAuthenticated: boolean,
    username?: string
  ) => {
    if (isAuthenticated && username) {
      // Giriş yapmış kullanıcı için customerId bul
      const customerId = await formService.findCustomerIdByUsername(username);
      if (!customerId) {
        throw new Error('Kullanıcı bilgisi bulunamadı. Lütfen tekrar giriş yapınız.');
      }
      // Daha önce başvuru var mı kontrolü (customer-to-campaign)
      const checkResponse = await api.get('/api/Campaign_Service/customer-to-campaign');
      if (checkResponse.data && Array.isArray(checkResponse.data)) {
        const existing = checkResponse.data.find((rel: any) => rel.customer?.id === customerId && rel.campaign?.id === campaignId && rel.isActive);
        if (existing) {
          throw new Error('Bu kampanya için daha önce teklif aldınız. Başka bir kampanya seçebilirsiniz.');
        }
      }
      // Giriş yapmış kullanıcı için public endpoint kullan
      const submitResponse = await api.post('/api/Campaign_Service/public/form-submission/authenticated', {
        customerId,
        campaignId,
        contactEmail: formData.contact_email || formData.email,
        contactPhone: formData.contact_phone || formData.phoneNumber,
        contactName: formData.firstName || formData.name,
        contactSurname: formData.lastName || formData.surname
      });
      return { customerToCampaign: { id: submitResponse.data.relationId } };
    } else {
      // Giriş yapmamış kullanıcı için mevcut akış
      return formService.submitFormAndCreateCampaignRelation(formData, campaignId);
    }
  }
};