import axios from 'axios';

const TURKIYE_API_BASE_URL = 'https://turkiyeapi.dev/api/v1';

export interface Province {
  id: number;
  name: string;
  population: number;
  areaCode: string;
  isMetropolitan: boolean;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface District {
  id: number;
  name: string;
  population: number;
  provinceId: number;
  areaCode?: string;
}

// Cache için in-memory storage
const cache = {
  provinces: null as Province[] | null,
  districts: new Map<number, District[]>(),
  lastFetch: {
    provinces: null as number | null,
    districts: new Map<number, number>(),
  }
};

const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours

class TurkiyeApiService {
  async getProvinces(): Promise<Province[]> {
    const now = Date.now();
    const lastFetch = cache.lastFetch.provinces;
    
    // Check cache
    if (cache.provinces && lastFetch && (now - lastFetch) < CACHE_DURATION) {
      return cache.provinces;
    }

    try {
      const response = await axios.get<{ data: Province[] }>(`${TURKIYE_API_BASE_URL}/provinces`);
      
      const provinces = response.data?.data || response.data || [];
      
      // Cache the result
      cache.provinces = provinces;
      cache.lastFetch.provinces = now;
      
      return provinces;
    } catch (error) {
      console.warn('Error fetching provinces from Türkiye API:', error);
      
      // Fallback: Return some major Turkish cities
      const fallbackProvinces: Province[] = [
        { id: 1, name: 'Adana', population: 2237940, areaCode: '01', isMetropolitan: true },
        { id: 6, name: 'Ankara', population: 5639076, areaCode: '0312', isMetropolitan: true },
        { id: 7, name: 'Antalya', population: 2619832, areaCode: '0242', isMetropolitan: true },
        { id: 16, name: 'Bursa', population: 3101833, areaCode: '0224', isMetropolitan: true },
        { id: 34, name: 'İstanbul', population: 15840900, areaCode: '0212', isMetropolitan: true },
        { id: 35, name: 'İzmir', population: 4367251, areaCode: '0232', isMetropolitan: true },
        { id: 41, name: 'Kocaeli', population: 1953035, areaCode: '0262', isMetropolitan: false },
        { id: 58, name: 'Sivas', population: 646608, areaCode: '0346', isMetropolitan: false },
      ];
      
      cache.provinces = fallbackProvinces;
      cache.lastFetch.provinces = now;
      
      return fallbackProvinces;
    }
  }

  async getDistricts(provinceId: number): Promise<District[]> {
    const now = Date.now();
    const lastFetch = cache.lastFetch.districts.get(provinceId);
    
    // Check cache
    if (cache.districts.has(provinceId) && lastFetch && (now - lastFetch) < CACHE_DURATION) {
      return cache.districts.get(provinceId)!;
    }

    try {
      const response = await axios.get<{ data: District[] }>(`${TURKIYE_API_BASE_URL}/districts`, {
        params: { provinceId }
      });
      
      const districts = response.data?.data || response.data || [];
      
      // Cache the result
      cache.districts.set(provinceId, districts);
      cache.lastFetch.districts.set(provinceId, now);
      
      return districts;
    } catch (error) {
      console.warn(`Error fetching districts for province ${provinceId}:`, error);
      
      // Fallback: Return empty array or some default districts
      const fallbackDistricts: District[] = [];
      
      cache.districts.set(provinceId, fallbackDistricts);
      cache.lastFetch.districts.set(provinceId, now);
      
      return fallbackDistricts;
    }
  }

  // Get province by name (for reverse lookup)
  async getProvinceByName(name: string): Promise<Province | null> {
    const provinces = await this.getProvinces();
    return provinces.find(province => 
      province.name.toLowerCase() === name.toLowerCase()
    ) || null;
  }

  // Get district by name within a province
  async getDistrictByName(provinceId: number, name: string): Promise<District | null> {
    const districts = await this.getDistricts(provinceId);
    return districts.find(district => 
      district.name.toLowerCase() === name.toLowerCase()
    ) || null;
  }

  // Clear cache (for testing or manual refresh)
  clearCache(): void {
    cache.provinces = null;
    cache.districts.clear();
    cache.lastFetch.provinces = null;
    cache.lastFetch.districts.clear();
  }
}

export const turkiyeApiService = new TurkiyeApiService();
export default turkiyeApiService; 