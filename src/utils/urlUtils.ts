/**
 * URL için güvenli bir slug oluşturur
 * @param text Slug'a dönüştürülecek metin
 * @returns URL için güvenli slug
 */
export const createSlug = (text: string): string => {
  // Türkçe karakterleri değiştir
  const turkishChars: Record<string, string> = {
    ı: "i",
    ğ: "g",
    ü: "u",
    ş: "s",
    ö: "o",
    ç: "c",
    İ: "I",
    Ğ: "G",
    Ü: "U",
    Ş: "S",
    Ö: "O",
    Ç: "C",
  };

  // Türkçe karakterleri değiştir
  let slug = text.replace(/[ıİğĞüÜşŞöÖçÇ]/g, (match) => turkishChars[match]);

  // Küçük harfe çevir, alfanumerik olmayan karakterleri tire ile değiştir
  slug = slug
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/^-+|-+$/g, ""); // Baştaki ve sondaki tireleri kaldır

  return slug;
};

/**
 * Kategori URL'si oluşturur
 * @param id Kategori ID'si
 * @param name Kategori adı
 * @returns Kategori URL'si
 */
export const createCategoryUrl = (id: number, name: string): string => {
  const slug = createSlug(name);
  return `/kategoriler/${id}/${slug}`;
};

/**
 * Kategori URL'sinden ID'yi çıkarır
 * @param url Kategori URL'si veya URL parametresi
 * @returns Kategori ID'si
 */
export const extractCategoryId = (url: string): string => {
  // URL'den sadece ID kısmını çıkar (tire ile ayrılmış ilk parça)
  const match = url.match(/(\d+)-/);
  if (match && match[1]) {
    return match[1];
  }

  // Eğer URL formatı farklıysa, doğrudan parametreyi döndür
  return url;
};
