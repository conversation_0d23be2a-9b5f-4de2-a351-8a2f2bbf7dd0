/**
 * Parses a date string in various formats
 * @param dateString - The date string to parse (ISO format or "DD-MM-YYYY HH:mm:ss" format)
 * @returns A Date object or null if parsing fails
 */
export const parseDate = (dateString: string | null): Date | null => {
  if (!dateString) return null;

  try {
    // Check if the date is in "DD-MM-YYYY HH:mm:ss" format
    if (dateString.includes('-') && dateString.includes(':') && dateString.includes(' ')) {
      const [datePart, timePart] = dateString.split(' ');
      const [day, month, year] = datePart.split('-').map(Number);
      const [hours, minutes, seconds] = timePart.split(':').map(Number);

      // Create date with correct day/month/year order (months are 0-indexed in JS Date)
      return new Date(year, month - 1, day, hours, minutes, seconds);
    }
    // Check if the date is in "DD.MM.YYYY HH:mm:ss" format
    else if (dateString.includes('.') && dateString.includes(':') && dateString.includes(' ')) {
      const [datePart, timePart] = dateString.split(' ');
      const [day, month, year] = datePart.split('.').map(Number);
      const [hours, minutes, seconds] = timePart.split(':').map(Number);

      // Create date with correct day/month/year order (months are 0-indexed in JS Date)
      return new Date(year, month - 1, day, hours, minutes, seconds);
    }
    // Check if the date is in "DD.MM.YYYY" format without time
    else if (dateString.includes('.') && !dateString.includes(':')) {
      const [day, month, year] = dateString.split('.').map(Number);
      return new Date(year, month - 1, day);
    }
    else {
      // Try to parse as ISO format
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return null;
      }
      return date;
    }
  } catch (error) {
    return null;
  }
};

/**
 * Formats a date string to a localized format
 * @param dateString - The date string to format (ISO format or "DD-MM-YYYY HH:mm:ss" format)
 * @param locale - The locale to use for formatting (default: 'tr-TR')
 * @returns A formatted date string or '-' if formatting fails
 */
export const formatDate = (dateString: string | null, locale: string = 'tr-TR'): string => {
  if (!dateString) return '-';

  try {
    const date = parseDate(dateString);

    if (!date) {
      return '-';
    }

    // Format the date according to the locale (day/month/year for tr-TR)
    return date.toLocaleString(locale, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return '-';
  }
};

/**
 * Formats a date string to a specific format with time (DD.MM.YYYY HH:MM)
 * @param dateString - The date string to format
 * @returns A formatted date string with time or '-' if formatting fails
 */
export const formatDateWithTime = (dateString: string | null): string => {
  if (!dateString) return '-';

  try {
    const date = parseDate(dateString);

    if (!date) {
      return '-';
    }

    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${day}.${month}.${year} ${hours}:${minutes}`;
  } catch (error) {
    return '-';
  }
};

/**
 * Calculates the number of days left until the end date
 * @param endDate - The end date string in ISO format or "DD-MM-YYYY HH:mm:ss" format
 * @returns The number of days left, 0 if it's today, or -1 if the date has passed
 */
export const getDaysLeft = (endDate: string): number => {
  try {
    const endDateObj = parseDate(endDate);

    // If the date is invalid, return -1
    if (!endDateObj) {
      return -1;
    }

    // Get current date and reset time to start of day (00:00:00)
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Reset end date time to start of day (00:00:00)
    const endDay = new Date(endDateObj);
    endDay.setHours(0, 0, 0, 0);

    // Calculate the difference in milliseconds
    const diffTime = endDay.getTime() - today.getTime();

    // Convert to days (1000ms * 60s * 60min * 24h = 86400000ms in a day)
    const diffDays = Math.round(diffTime / 86400000);

    return diffDays;
  } catch (error) {
    console.error('Error calculating days left:', error);
    return -1;
  }
};

/**
 * Calculates the number of hours left until the end date
 * @param endDate - The end date string in ISO format or "DD-MM-YYYY HH:mm:ss" format
 * @returns The number of hours left, or -1 if the date has passed
 */
export const getHoursLeft = (endDate: string): number => {
  try {
    const endDateObj = parseDate(endDate);

    // If the date is invalid, return -1
    if (!endDateObj) {
      return -1;
    }

    const now = new Date();
    const diffTime = endDateObj.getTime() - now.getTime();

    // Convert to hours (1000ms * 60s * 60min = 3600000ms in an hour)
    const diffHours = Math.floor(diffTime / 3600000);

    return diffHours;
  } catch (error) {
    return -1;
  }
};

/**
 * Gets a human-readable time remaining string for campaigns
 * @param endDate - The end date string in ISO format or "DD-MM-YYYY HH:mm:ss" format
 * @param locale - The locale to use for formatting (default: 'tr-TR')
 * @returns A string describing time remaining (e.g., "5 hours left", "2 days left")
 */
export const getTimeRemaining = (endDate: string, locale: string = 'tr-TR'): string => {
  try {
    const endDateObj = parseDate(endDate);
    if (!endDateObj) return locale === 'tr-TR' ? 'Süresi doldu' : 'Expired';

    const now = new Date();
    const diffMs = endDateObj.getTime() - now.getTime();

    if (diffMs < 0) return locale === 'tr-TR' ? 'Süresi doldu' : 'Expired';

    // Use the same calculation method as getDaysLeft for consistency
    const diffDays = getDaysLeft(endDate);
    
    // If it's less than a day, show hours
    if (diffDays <= 0) {
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      if (diffHours <= 0) {
        // If it's less than an hour, show minutes
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        if (diffMinutes <= 0) return locale === 'tr-TR' ? 'Süresi doldu' : 'Expired';
        if (diffMinutes === 1) return locale === 'tr-TR' ? '1 dakika kaldı' : '1 minute left';
        return locale === 'tr-TR' ? `${diffMinutes} dakika kaldı` : `${diffMinutes} minutes left`;
      }
      if (diffHours === 1) return locale === 'tr-TR' ? '1 saat kaldı' : '1 hour left';
      return locale === 'tr-TR' ? `${diffHours} saat kaldı` : `${diffHours} hours left`;
    } else {
      if (diffDays === 1) return locale === 'tr-TR' ? '1 gün kaldı' : '1 day left';
      return locale === 'tr-TR' ? `${diffDays} gün kaldı` : `${diffDays} days left`;
    }
  } catch (error) {
    return locale === 'tr-TR' ? 'Süresi doldu' : 'Expired';
  }
};

/**
 * Determines if a campaign is ending soon (within 7 days)
 * @param endDate - The end date string in ISO format
 * @returns Boolean indicating if the campaign is ending soon
 */
export const isEndingSoon = (endDate: string): boolean => {
  // First check if the campaign has expired
  if (isExpired(endDate)) {
    return false;
  }

  const daysLeft = getDaysLeft(endDate);
  return daysLeft >= 0 && daysLeft <= 7;
};

/**
 * Determines if a campaign is newly added (within the last 7 days)
 * @param createdDate - The creation date string (can be ISO format or "DD-MM-YYYY HH:mm:ss" format)
 * @returns Boolean indicating if the campaign was created within the last 7 days
 */
export const isNewlyAdded = (createdDate: string | null | undefined): boolean => {
  if (!createdDate) return false;

  try {
    const createdDateObj = parseDate(createdDate);
    if (!createdDateObj) return false;

    const now = new Date();
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(now.getDate() - 7);

    return createdDateObj >= sevenDaysAgo;
  } catch (error) {
    return false;
  }
};

/**
 * Determines if a campaign is ending soon within a week (within 7 days)
 * @param endDate - The end date string in ISO format
 * @returns Boolean indicating if the campaign is ending within 7 days
 */
export const isEndingSoonInWeek = (endDate: string): boolean => {
  // First check if the campaign has expired
  if (isExpired(endDate)) {
    return false;
  }

  const daysLeft = getDaysLeft(endDate);
  return daysLeft >= 0 && daysLeft <= 7;
};

/**
 * Gets the time elapsed since a campaign was created in a human-readable format
 * @param createdDate - The creation date string (can be ISO format or "DD-MM-YYYY HH:mm:ss" format)
 * @param locale - The locale to use for formatting (default: 'tr-TR')
 * @returns A string describing how long ago the campaign was created (e.g., "2 hours ago", "3 days ago")
 */
export const getTimeAgo = (createdDate: string | null | undefined, locale: string = 'tr-TR'): string => {
  if (!createdDate) return '';

  try {
    const createdDateObj = parseDate(createdDate);
    if (!createdDateObj) return '';

    const now = new Date();
    const diffMs = now.getTime() - createdDateObj.getTime();

    // Convert to different time units
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMinutes < 60) {
      if (diffMinutes <= 1) return locale === 'tr-TR' ? '1 dakika önce' : '1 minute ago';
      return locale === 'tr-TR' ? `${diffMinutes} dakika önce` : `${diffMinutes} minutes ago`;
    } else if (diffHours < 24) {
      if (diffHours === 1) return locale === 'tr-TR' ? '1 saat önce' : '1 hour ago';
      return locale === 'tr-TR' ? `${diffHours} saat önce` : `${diffHours} hours ago`;
    } else if (diffDays <= 7) {
      if (diffDays === 1) return locale === 'tr-TR' ? '1 gün önce' : '1 day ago';
      return locale === 'tr-TR' ? `${diffDays} gün önce` : `${diffDays} days ago`;
    } else {
      return locale === 'tr-TR' ? `${diffDays} gün önce` : `${diffDays} days ago`;
    }
  } catch (error) {
    return '';
  }
};

/**
 * Determines if a campaign is ending very soon (within 3 days)
 * @param endDate - The end date string in ISO format
 * @returns Boolean indicating if the campaign is ending very soon
 */
export const isEndingVerySoon = (endDate: string): boolean => {
  // First check if the campaign has expired
  if (isExpired(endDate)) {
    return false;
  }

  const daysLeft = getDaysLeft(endDate);
  return daysLeft >= 0 && daysLeft <= 3;
};

/**
 * Determines if a campaign has expired (end date has passed)
 * @param endDate - The end date string in ISO format
 * @returns Boolean indicating if the campaign has expired
 */
export const isExpired = (endDate: string): boolean => {
  try {
    const endDateObj = parseDate(endDate);

    // If the date is invalid, consider it expired
    if (!endDateObj) {
      return true;
    }

    const now = new Date();

    // Compare exact timestamps (including hours, minutes, seconds)
    return endDateObj.getTime() < now.getTime();
  } catch (error) {
    return true; // If there's an error, consider it expired for safety
  }
};

/**
 * Standardizes campaign end date format to ensure consistency across the application
 * @param endDate - The end date from API response
 * @param fallbackDate - Optional fallback date if endDate is invalid (default: current date + 16 days)
 * @returns A standardized date string in ISO format
 */
export const standardizeCampaignEndDate = (endDate: string | null | undefined, fallbackDate?: string): string => {
  // Debug log to see what's coming in

  // If endDate is valid, use it
  if (endDate) {
    const parsedDate = parseDate(endDate);
    if (parsedDate && !isNaN(parsedDate.getTime())) {
      return parsedDate.toISOString();
    }
  }

  // If fallbackDate is provided and valid, use it
  if (fallbackDate) {
    const parsedFallback = parseDate(fallbackDate);
    if (parsedFallback && !isNaN(parsedFallback.getTime())) {
      return parsedFallback.toISOString();
    }
  }

  // Default fallback: current date + 16 days (for consistency with the example in screenshots)
  const defaultDate = new Date();
  defaultDate.setDate(defaultDate.getDate() + 16);
  return defaultDate.toISOString();
};

/**
 * Creates a slug from a string
 * @param str - The string to convert to a slug
 * @returns The slug
 */
export const createSlug = (str: string): string => {
  if (!str) return '';

  return str
    .toLowerCase()
    .replace(/ğ/g, 'g')
    .replace(/ü/g, 'u')
    .replace(/ş/g, 's')
    .replace(/ı/g, 'i')
    .replace(/ö/g, 'o')
    .replace(/ç/g, 'c')
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-+|-+$/g, '');
};

/**
 * Sorts campaigns by their end date, from soonest to latest
 * @param campaigns - Array of campaign objects with endDate property
 * @returns Sorted array of campaigns
 */
export const sortCampaignsByEndDate = <T extends { endDate: string }>(campaigns: T[]): T[] => {
  return [...campaigns].sort((a, b) => {
    const dateA = parseDate(a.endDate);
    const dateB = parseDate(b.endDate);

    if (!dateA && !dateB) return 0;
    if (!dateA) return 1;
    if (!dateB) return -1;

    return dateA.getTime() - dateB.getTime();
  });
};

/**
 * Parses a date from a non-standard format to a Date object.
 * Handles formats like 'YYYY-MM-DD HH:mm:ss.SSSSSS'.
 * @param dateString - The date string to parse.
 * @returns A Date object or null if invalid.
 */
export const parseToDate = (dateString: string | null | undefined): Date | null => {
  if (!dateString || (typeof dateString !== 'string' && typeof dateString !== 'number')) {
    return null;
  }

  const dateStr = String(dateString);

  // 1. Check for Unix timestamp (numeric string)
  if (/^\d+$/.test(dateStr)) {
    const timestamp = parseInt(dateStr, 10);
    // If it has 10 digits, it's likely seconds. Convert to milliseconds.
    if (dateStr.length === 10) {
      return new Date(timestamp * 1000);
    }
    // If it has 13 digits, it's likely milliseconds.
    if (dateStr.length === 13) {
      return new Date(timestamp);
    }
  }

  // 2. Check for "DD-MM-YYYY HH:mm:ss" format
  const dmyRegex = /^(\d{2})-(\d{2})-(\d{4})\s(\d{2}):(\d{2}):(\d{2})/;
  const dmyMatch = dateStr.match(dmyRegex);
  if (dmyMatch) {
    const [, day, month, year, hours, minutes, seconds] = dmyMatch.map(Number);
    // JS months are 0-indexed, so month - 1
    return new Date(year, month - 1, day, hours, minutes, seconds);
  }

  // 3. Handle ISO-like format 'YYYY-MM-DD HH:mm:ss.SSSSSS'
  if (dateStr.includes('-') && dateStr.includes(':')) {
    let parsableString = dateStr.replace(' ', 'T');
    const dotIndex = parsableString.lastIndexOf('.');
    if (dotIndex > -1) {
      const fraction = parsableString.substring(dotIndex + 1);
      if (fraction.length > 3) {
        parsableString = parsableString.substring(0, dotIndex + 4);
      }
    }
    const date = new Date(parsableString);
    if (!isNaN(date.getTime())) {
      return date;
    }
  }

  // 4. Fallback for any other valid format that new Date() can handle
  const fallbackDate = new Date(dateStr);
  if (!isNaN(fallbackDate.getTime())) {
    return fallbackDate;
  }

  return null;
};

/**
 * Formats a date from a non-standard format to a readable string.
 * Handles formats like 'YYYY-MM-DD HH:mm:ss.SSSSSS'.
 * @param dateString - The date string to format.
 * @returns A formatted date string or 'Belirtilmemiş' if invalid.
 */
export const formatReadableDate = (dateString: string | null | undefined): string => {
  const date = parseToDate(dateString);

  if (!date) {
    if (dateString) {
      console.error('FORMATLANAMAYAN GEÇERSİZ TARİH:', `"${dateString}"`);
      return 'Geçersiz Tarih';
    }
    return 'Belirtilmemiş';
  }

  return date.toLocaleString('tr-TR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};
