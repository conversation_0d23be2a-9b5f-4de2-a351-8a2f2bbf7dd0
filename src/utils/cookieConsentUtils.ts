/**
 * <PERSON><PERSON> Consent yönetimi için yardımcı fonksiyonlar
 */

/// <reference path="../types/gtm.d.ts" />

export interface CookiePreferences {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
  preferences: boolean;
}

export interface CookieConsentData {
  hasConsented: boolean;
  consentDate: string;
  preferences: CookiePreferences;
  version: string;
  uniqueId: string;
  lastUpdated: string;
  consentType: 'explicit' | 'implicit' | 'default';
}

const COOKIE_CONSENT_KEY = 'cookieConsent';
const COOKIE_CONSENT_VERSION = '1.1';
const UNIQUE_ID_COOKIE = 'visitor_id';
const COOKIE_EXPIRY_DAYS = 365; // 1 yıl

// Development ve production için farklı GTM ID'ler
const GTM_ID = window.location.hostname === 'localhost'
  ? 'GTM-K6XJZXS8' // Aynı ID'yi kullan ama debug mode'da
  : 'GTM-K6XJZXS8';

/**
 * <PERSON>ie'ye değer yazar
 */
const setCookie = (name: string, value: string, days: number): void => {
  const date = new Date();
  date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
  const expires = `expires=${date.toUTCString()}`;
  document.cookie = `${name}=${value};${expires};path=/;SameSite=Lax`;
};

/**
 * Cookie'den değer okur
 */
const getCookie = (name: string): string | null => {
  const nameEQ = `${name}=`;
  const ca = document.cookie.split(';');
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
  }
  return null;
};

/**
 * Cookie'yi siler
 */
const deleteCookie = (name: string): void => {
  setCookie(name, '', -1);
};

/**
 * Benzersiz ziyaretçi ID'si oluşturur veya mevcut olanı getirir
 */
export const getOrCreateUniqueId = (): string => {
  let uniqueId = getCookie(UNIQUE_ID_COOKIE);
  
  if (!uniqueId) {
    // UUID v4 formatında benzersiz ID oluştur
    uniqueId = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
    // ID'yi cookie'ye kaydet (1 yıl geçerli)
    setCookie(UNIQUE_ID_COOKIE, uniqueId, COOKIE_EXPIRY_DAYS);
  }
  
  return uniqueId;
};

/**
 * Varsayılan cookie tercihleri (sadece gerekli çerezler)
 */
export const DEFAULT_COOKIE_PREFERENCES: CookiePreferences = {
  necessary: true, // Gerekli çerezler her zaman aktif
  analytics: false,
  marketing: false,
  preferences: false,
};

/**
 * Özelleştir modal'ında varsayılan olarak açık gelen tercihler
 */
export const CUSTOMIZE_DEFAULT_PREFERENCES: CookiePreferences = {
  necessary: true, // Gerekli çerezler her zaman aktif
  analytics: true,
  marketing: true,
  preferences: true,
};

/**
 * Cookie tercihlerini kaydeder
 */
export const setCookieConsent = (preferences: CookiePreferences, consentType: 'explicit' | 'implicit' | 'default' = 'explicit'): void => {
  const consentData: CookieConsentData = {
    hasConsented: true,
    consentDate: new Date().toISOString(),
    preferences,
    version: COOKIE_CONSENT_VERSION,
    uniqueId: getOrCreateUniqueId(),
    lastUpdated: new Date().toISOString(),
    consentType
  };

  // Cookie consent verilerini cookie'ye kaydet
  setCookie(COOKIE_CONSENT_KEY, JSON.stringify(consentData), COOKIE_EXPIRY_DAYS);
  applyCookiePreferences(preferences);
};

/**
 * Mevcut cookie tercihlerini getirir
 */
export const getCurrentPreferences = (): CookiePreferences => {
  const consentData = getCookie(COOKIE_CONSENT_KEY);
  if (consentData) {
    try {
      const parsed = JSON.parse(consentData) as CookieConsentData;
      return parsed.preferences;
    } catch (e) {
      return DEFAULT_COOKIE_PREFERENCES;
    }
  }
  return DEFAULT_COOKIE_PREFERENCES;
};

/**
 * Kullanıcının onay verip vermediğini kontrol eder
 */
export const hasUserConsented = (): boolean => {
  const consentData = getCookie(COOKIE_CONSENT_KEY);
  if (consentData) {
    try {
      const parsed = JSON.parse(consentData) as CookieConsentData;
      return parsed.hasConsented;
    } catch (e) {
      return false;
    }
  }
  return false;
};

/**
 * Tüm çerezleri kabul eder
 */
export const acceptAllCookies = (): void => {
  const allAcceptedPreferences: CookiePreferences = {
    necessary: true,
    analytics: true,
    marketing: true,
    preferences: true,
  };

  setCookieConsent(allAcceptedPreferences, 'explicit');
};

/**
 * Sadece gerekli çerezleri kabul eder
 */
export const acceptOnlyNecessary = (): void => {
  setCookieConsent(DEFAULT_COOKIE_PREFERENCES, 'explicit');
};

/**
 * Cookie tercihlerini uygular
 */
export const applyCookiePreferences = (preferences: CookiePreferences): void => {
  // Analytics çerezleri
  if (preferences.analytics) {
    enableAnalytics();
  } else {
    disableAnalytics();
  }

  // Marketing çerezleri
  if (preferences.marketing) {
    enableMarketing();
  } else {
    disableMarketing();
  }

  // Tercih çerezleri
  if (preferences.preferences) {
    enablePreferences();
  } else {
    disablePreferences();
  }
};

/**
 * Analytics çerezlerini etkinleştirir
 */
const enableAnalytics = (): void => {
  if (typeof window !== 'undefined') {
    // Google Tag Manager'da analytics consent'i güncelle
    if (window.gtag) {
      window.gtag('consent', 'update', {
        'analytics_storage': 'granted'
      });
    }

    // DataLayer'a event gönder
    if (window.dataLayer) {
      window.dataLayer.push({
        'event': 'analytics_consent_granted'
      });
    }

  }
};

/**
 * Analytics çerezlerini devre dışı bırakır
 */
const disableAnalytics = (): void => {
  if (typeof window !== 'undefined') {
    // Google Tag Manager'da analytics consent'i reddet
    if (window.gtag) {
      window.gtag('consent', 'update', {
        'analytics_storage': 'denied'
      });
    }

    // DataLayer'a event gönder
    if (window.dataLayer) {
      window.dataLayer.push({
        'event': 'analytics_consent_denied'
      });
    }

  }
};

/**
 * Marketing çerezlerini etkinleştirir
 */
const enableMarketing = (): void => {
  if (typeof window !== 'undefined') {
    // Google Tag Manager'da marketing consent'i güncelle
    if (window.gtag) {
      window.gtag('consent', 'update', {
        'ad_storage': 'granted',
        'ad_user_data': 'granted',
        'ad_personalization': 'granted'
      });
    }

    // DataLayer'a event gönder
    if (window.dataLayer) {
      window.dataLayer.push({
        'event': 'marketing_consent_granted'
      });
    }

  }
};

/**
 * Marketing çerezlerini devre dışı bırakır
 */
const disableMarketing = (): void => {
  if (typeof window !== 'undefined') {
    // Google Tag Manager'da marketing consent'i reddet
    if (window.gtag) {
      window.gtag('consent', 'update', {
        'ad_storage': 'denied',
        'ad_user_data': 'denied',
        'ad_personalization': 'denied'
      });
    }

    // DataLayer'a event gönder
    if (window.dataLayer) {
      window.dataLayer.push({
        'event': 'marketing_consent_denied'
      });
    }

    console.log('Marketing cookies disabled');
  }
};

/**
 * Tercih çerezlerini etkinleştirir
 */
const enablePreferences = (): void => {
  if (typeof window !== 'undefined') {
    console.log('Preference cookies enabled');
  }
};

/**
 * Tercih çerezlerini devre dışı bırakır
 */
const disablePreferences = (): void => {
  if (typeof window !== 'undefined') {
    console.log('Preference cookies disabled');
  }
};

/**
 * Örtülü consent - kullanıcı siteyi kullanmaya devam ederse kabul etmiş sayılır
 * Pazarlama şirketi için optimize edilmiş - analytics ve marketing dahil
 */
export const handleImplicitConsent = (): void => {
  if (!hasUserConsented()) {
    // 20 saniye sonra otomatik olarak analytics ve marketing çerezlerini kabul et
    setTimeout(() => {
      if (!hasUserConsented()) {
        const marketingOptimizedPreferences: CookiePreferences = {
          necessary: true,
          analytics: true,    // Kullanıcı davranışı analizi için
          marketing: true,    // Pazarlama ve retargeting için
          preferences: true,  // Kişiselleştirme için
        };
        setCookieConsent(marketingOptimizedPreferences, 'implicit');
        console.log('Implicit consent: Marketing-optimized cookies accepted after 20 seconds');
      }
    }, 20000); // 20 saniye (daha hızlı)
  }
};

/**
 * Cookie consent banner'ın gösterilip gösterilmeyeceğini kontrol eder
 */
export const shouldShowCookieBanner = (): boolean => {
  return !hasUserConsented();
};

/**
 * Kullanıcının cookie tercihlerini sıfırlar
 */
export const resetCookieConsent = (): void => {
  deleteCookie(COOKIE_CONSENT_KEY);
  // Unique ID'yi silme, çünkü bu kullanıcıyı takip etmek için önemli
};

/**
 * Kullanıcının son cookie tercihlerini günceller
 */
export const updateCookiePreferences = (preferences: CookiePreferences): void => {
  const consentData = getCookie(COOKIE_CONSENT_KEY);
  if (consentData) {
    try {
      const parsed = JSON.parse(consentData) as CookieConsentData;
      const updatedData: CookieConsentData = {
        ...parsed,
        preferences,
        lastUpdated: new Date().toISOString(),
        consentType: 'explicit'
      };
      setCookie(COOKIE_CONSENT_KEY, JSON.stringify(updatedData), COOKIE_EXPIRY_DAYS);
      applyCookiePreferences(preferences);
    } catch (e) {
      console.error('Error updating cookie preferences:', e);
    }
  }
};

/**
 * Google Tag Manager'ı yükler
 */
export const loadGTM = (): void => {
  if (typeof window === 'undefined') return;

  // DataLayer'ı başlat
  window.dataLayer = window.dataLayer || [];

  // GTM script'ini yükle
  const script = document.createElement('script');
  script.async = true;
  script.src = `https://www.googletagmanager.com/gtm.js?id=${GTM_ID}`;

  // GTM başlatma kodu
  window.dataLayer.push({
    'gtm.start': new Date().getTime(),
    event: 'gtm.js'
  });

  // Script'i head'e ekle
  const firstScript = document.getElementsByTagName('script')[0];
  if (firstScript && firstScript.parentNode) {
    firstScript.parentNode.insertBefore(script, firstScript);
  }

  // NoScript iframe'ini body'ye ekle
  const noscript = document.createElement('noscript');
  noscript.innerHTML = `<iframe src="https://www.googletagmanager.com/ns.html?id=${GTM_ID}" height="0" width="0" style="display:none;visibility:hidden"></iframe>`;
  document.body.appendChild(noscript);

};

/**
 * Varsayılan consent durumunu ayarlar (GTM yüklenmeden önce)
 */
export const setDefaultConsent = (): void => {
  if (typeof window === 'undefined') return;

  // DataLayer'ı başlat
  window.dataLayer = window.dataLayer || [];

  // Varsayılan consent durumunu ayarla (tümü denied)
  window.dataLayer.push({
    'event': 'default_consent',
    'gtm.start': new Date().getTime(),
    'consent': 'default',
    'analytics_storage': 'denied',
    'ad_storage': 'denied',
    'ad_user_data': 'denied',
    'ad_personalization': 'denied',
    'functionality_storage': 'granted', // Gerekli çerezler
    'security_storage': 'granted' // Gerekli çerezler
  });
};
