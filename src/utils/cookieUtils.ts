/**
 * <PERSON>ie güvenlik kontrolü için yardımcı fonksiyonlar
 */

/**
 * Cookie güvenlik ayarlarını kontrol eder
 * 
 * Bu fonksiyon, refresh token cookie'sinin güvenlik ayarlarını kontrol eder.
 * HttpOnly, Secure ve SameSite=Strict ayarlarının doğru yapılandırıldığından emin olur.
 * 
 * Not: JavaScript, HttpOnly cookie'lere erişemez, bu nedenle bu kontrol dolaylı olarak yapılır.
 * 
 * @returns {boolean} Cookie güvenlik ayarlarının uygun olup olmadığı
 */
export const checkCookieSecurity = (): boolean => {
  // HTTPS kontrolü
  const isSecureConnection = window.location.protocol === 'https:';
  
  // Refresh token cookie'sinin varlığını kontrol et
  // Not: HttpOnly cookie'lere JavaScript ile doğrudan erişilemez
  // <PERSON><PERSON> ne<PERSON>, cookie'nin varlığını dolaylı olarak kontrol ediyoruz
  
  // Bir API isteği yaparak cookie'nin gönderilip gönderilmediğini kontrol edebiliriz
  // Ancak bu, her kontrol için bir API çağrısı gerektirir
  
  // Alternatif olarak, kullanıcının giriş yapmış olup olmadığını kontrol edebiliriz
  const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
  
  // Geliştirme ortamında HTTPS zorunlu değil
  if (process.env.NODE_ENV === 'development') {
    return isLoggedIn;
  }
  
  // Üretim ortamında HTTPS zorunlu
  return isSecureConnection && isLoggedIn;
};

/**
 * Cookie'lerin SameSite ayarını kontrol eder
 * 
 * Bu fonksiyon, tarayıcının SameSite=Strict cookie'leri destekleyip desteklemediğini kontrol eder.
 * 
 * @returns {boolean} Tarayıcının SameSite=Strict cookie'leri destekleyip desteklemediği
 */
export const checkSameSiteSupport = (): boolean => {
  // Modern tarayıcıların çoğu SameSite=Strict'i destekler
  // Ancak eski tarayıcılar desteklemeyebilir
  
  // Tarayıcı sürümünü kontrol etmek için kullanıcı ajanını kullanabiliriz
  const userAgent = navigator.userAgent;
  
  // Chrome 80+, Firefox 69+, Safari 12.1+ ve Edge 79+ SameSite=Strict'i destekler
  const chromeMatch = userAgent.match(/Chrome\/(\d+)/);
  const firefoxMatch = userAgent.match(/Firefox\/(\d+)/);
  const safariMatch = userAgent.match(/Version\/(\d+).*Safari/);
  const edgeMatch = userAgent.match(/Edg\/(\d+)/);
  
  if (chromeMatch && parseInt(chromeMatch[1]) >= 80) return true;
  if (firefoxMatch && parseInt(firefoxMatch[1]) >= 69) return true;
  if (safariMatch && parseInt(safariMatch[1]) >= 12) return true;
  if (edgeMatch && parseInt(edgeMatch[1]) >= 79) return true;
  
  // Desteklenmeyen tarayıcılar için false döndür
  return false;
};

/**
 * Kullanıcının tarayıcısının güvenli cookie'leri destekleyip desteklemediğini kontrol eder
 * 
 * @returns {boolean} Tarayıcının güvenli cookie'leri destekleyip desteklemediği
 */
export const isBrowserSecureCookieCompatible = (): boolean => {
  return checkSameSiteSupport();
};
