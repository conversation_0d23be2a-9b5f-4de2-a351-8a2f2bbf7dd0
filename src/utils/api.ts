import axios from 'axios';

// Create an axios instance with default config
const api = axios.create({
  baseURL: 'https://360avantajli.com', // Base URL for all requests
  timeout: 10000, // Request timeout in milliseconds
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Enable sending cookies with cross-origin requests
});

// Request interceptor for adding auth token from context if available
api.interceptors.request.use(
  (config) => {
    // Cookie'den JWT token'ı al ve Authorization header'ına ekle
    const cookies = document.cookie.split(';');
    const accessTokenCookie = cookies.find(c => c.trim().startsWith('accessToken='));

    if (accessTokenCookie) {
      const token = accessTokenCookie.split('=')[1];
      if (token && token !== 'undefined' && token !== 'null') {
        config.headers.Authorization = `Bearer ${token}`;
        console.log('Token added to request:', token.substring(0, 20) + '...');
      } else {
        console.warn('Invalid token found in cookie:', token);
      }
    } else {
      console.warn('No accessToken cookie found for request to:', config.url);
    }

    // CORS için gerekli header'ları ekle
    config.headers['Access-Control-Allow-Credentials'] = 'true';
    
    // Content-Type header'ını sadece body olan istekler için ekle
    if (config.data && typeof config.data === 'object') {
      config.headers['Content-Type'] = 'application/json';
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for handling token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // Token refresh sadece 401 hatası için yapılmalı, 403 için değil
    // Ayrıca access-token endpoint'ine yapılan isteklerde sonsuz döngüyü önle
    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      !originalRequest.url?.includes('/api/Auth_Service/auth/access-token') &&
      !originalRequest.url?.includes('/api/Auth_Service/auth/login') &&
      !originalRequest.url?.includes('/api/Auth_Service/auth/current-user')
    ) {  
      originalRequest._retry = true;

      try {
        // Token refresh isteği
        const refreshResponse = await axios.post(
          'https://360avantajli.com/api/Auth_Service/auth/access-token',
          {},
          {
            withCredentials: true,
            timeout: 5000, // Kısa timeout
          }
        );

        // Refresh başarılıysa orijinal isteği tekrarla
        if (refreshResponse.status === 200 || refreshResponse.status === 204) {
          return api(originalRequest);
        } else {
          // Refresh başarısızsa logout
          window.dispatchEvent(new Event('auth:logout'));
          return Promise.reject(error);
        }
      } catch (refreshError) {
        // Refresh hatası durumunda logout
        console.warn('Token refresh failed:', refreshError);
        window.dispatchEvent(new Event('auth:logout'));
        return Promise.reject(error);
      }
    }

    // 403 hatası için daha detaylı log ve kontrol
    if (error.response?.status === 403) {
      console.warn('Access forbidden (403) for URL:', error.config?.url);
      console.warn('Request headers:', error.config?.headers);
      console.warn('Response data:', error.response?.data);
      
      // Sadece admin endpoint'leri için logout yap
      if (error.config?.url?.includes('/admin') || 
          error.config?.url?.includes('/api/Campaign_Service') ||
          error.config?.url?.includes('/api/Auth_Service')) {
        console.warn('Admin endpoint 403 - logging out');
        window.dispatchEvent(new Event('auth:logout'));
      }
    }

    return Promise.reject(error);
  }
);

// Kullanıcı bilgilerini almak için yardımcı fonksiyon
export const getCurrentUser = async () => {
  try {
    const response = await api.get('/api/Auth_Service/auth/current-user');
    if (response.data) {
      return {
        ...response.data,
        role: response.data.role
      };
    }
    return null;
  } catch (error) {
    console.warn('Failed to get current user:', error);
    return null;
  }
};

// Debug utility - cookie durumunu kontrol et
export const debugCookies = () => {
  const cookies = document.cookie.split(';');
  console.log('Current cookies:', cookies);
  
  const accessTokenCookie = cookies.find(c => c.trim().startsWith('accessToken='));
  if (accessTokenCookie) {
    const token = accessTokenCookie.split('=')[1];
    console.log('Access token found:', token ? `${token.substring(0, 20)}...` : 'empty');
    
    // Token'ı decode et ve bilgileri göster
    try {
      const base64Url = token.split(".")[1];
      const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split("")
          .map(function (c) {
            return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
          })
          .join("")
      );
      const decoded = JSON.parse(jsonPayload);
      console.log('Token payload:', {
        sub: decoded.sub,
        roles: decoded.roles,
        exp: new Date(decoded.exp * 1000).toLocaleString(),
        iat: new Date(decoded.iat * 1000).toLocaleString()
      });
    } catch (e) {
      console.error('Token decode error:', e);
    }
  } else {
    console.log('No access token cookie found');
  }
};

// Test API endpoint'e erişim
export const testApiAccess = async () => {
  try {
    console.log('Testing API access...');
    const response = await api.get('/api/Campaign_Service/form');
    console.log('API access successful:', response.status);
    return true;
  } catch (error: any) {
    console.error('API access failed:', {
      status: error.response?.status,
      data: error.response?.data,
      url: error.config?.url
    });
    return false;
  }
};

export default api;
