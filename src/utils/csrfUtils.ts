/**
 * CSRF token yö<PERSON>imi i<PERSON>in yardımcı fonksiyonlar
 */

/**
 * Backend'den CSRF token alır ve meta tag'e yerleştirir
 */
export const fetchCsrfToken = async (): Promise<void> => {
  try {
    // Backend'den CSRF token almak için istek at
    // Temporarily removed credentials due to CORS issues
    const response = await fetch('https://360avantajli.com/api/Auth_Service/auth/csrf-token', {
      method: 'GET',
      // credentials: 'include', // <PERSON>ie'leri dahil et - CORS sorunu nedeniyle geçici olarak kaldırıldı
    });

    if (response.ok) {
      const data = await response.json();
      if (data.csrfToken) {
        // Meta tag'i güncelle
        const metaTag = document.querySelector('meta[name="csrf-token"]');
        if (metaTag) {
          metaTag.setAttribute('content', data.csrfToken);
        }
      }
    }
  } catch (error) {
    console.warn('CSRF token alınamadı:', error);
  }
};

/**
 * Mevcut CSRF token'ı döndürür
 */
export const getCsrfToken = (): string => {
  const metaTag = document.querySelector('meta[name="csrf-token"]');
  return metaTag?.getAttribute('content') || '';
};

/**
 * CSRF token'ı günceller
 */
export const updateCsrfToken = (token: string): void => {
  const metaTag = document.querySelector('meta[name="csrf-token"]');
  if (metaTag) {
    metaTag.setAttribute('content', token);
  }
};
