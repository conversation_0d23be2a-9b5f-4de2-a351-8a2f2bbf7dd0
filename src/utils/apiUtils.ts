import axios, { AxiosRequestConfig } from 'axios';

/**
 * Birden fazla API isteğini paralel olarak çalıştırır
 * @param requests İstek URL'leri ve seçenekleri
 * @returns Promise<any[]> - Tüm isteklerin sonuçları
 */
export async function fetchParallel(requests: { url: string; options?: AxiosRequestConfig }[]): Promise<any[]> {
  try {
    const promises = requests.map(request => 
      axios(request.url, request.options)
        .then(response => response.data)
        .catch(error => {
          return null; // Hata durumunda null döndür, böylece diğer istekler etkilenmez
        })
    );
    
    return await Promise.all(promises);
  } catch (error) {
    console.error('Error in parallel fetch:', error);
    throw error;
  }
}

/**
 * API isteklerini belirli bir süre içinde yeniden dener
 * @param fn API isteği yapan fonksiyon
 * @param retries Deneme sayısı
 * @param delay Denemeler arasındaki gecikme (ms)
 * @returns Promise<T>
 */
export async function retryFetch<T>(
  fn: () => Promise<T>,
  retries: number = 3,
  delay: number = 1000
): Promise<T> {
  try {
    return await fn();
  } catch (error) {
    if (retries <= 0) {
      throw error;
    }
    
    await new Promise(resolve => setTimeout(resolve, delay));
    return retryFetch(fn, retries - 1, delay);
  }
}

/**
 * API isteklerini hızlandırmak için önbelleğe alma dekoratörü
 * @param fn API isteği yapan fonksiyon
 * @param cacheKey Önbellek anahtarı
 * @param cacheTime Önbellek süresi (ms)
 * @returns Promise<T>
 */
export function withCache<T>(
  fn: () => Promise<T>,
  cacheKey: string,
  cacheTime: number = 5 * 60 * 1000
): () => Promise<T> {
  const cache: Record<string, { data: T; timestamp: number }> = {};
  
  return async () => {
    const cachedItem = cache[cacheKey];
    
    if (cachedItem && Date.now() - cachedItem.timestamp < cacheTime) {
      return cachedItem.data;
    }
    
    const data = await fn();
    
    cache[cacheKey] = {
      data,
      timestamp: Date.now()
    };
    
    return data;
  };
}

/**
 * API isteklerini belirli bir süre içinde iptal eder (timeout)
 * @param promise API isteği promise'i
 * @param timeoutMs Zaman aşımı süresi (ms)
 * @returns Promise<T>
 */
export function withTimeout<T>(promise: Promise<T>, timeoutMs: number = 10000): Promise<T> {
  return Promise.race([
    promise,
    new Promise<T>((_, reject) => 
      setTimeout(() => reject(new Error(`Request timed out after ${timeoutMs}ms`)), timeoutMs)
    )
  ]);
}

export default {
  fetchParallel,
  retryFetch,
  withCache,
  withTimeout
};
