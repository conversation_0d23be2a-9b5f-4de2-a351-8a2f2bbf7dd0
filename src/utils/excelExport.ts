import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';

/**
 * Convert table data to Excel file and trigger download
 * 
 * @param data - Array of objects to export
 * @param filename - Name of the Excel file (without extension)
 * @param sheetName - Name of the Excel sheet
 * @param columnMapping - Optional mapping to rename columns and control their order
 */
export const exportToExcel = <T extends Record<string, any>>(
  data: T[], 
  filename: string, 
  sheetName: string = 'Sheet1',
  columnMapping?: { [key: string]: string }
) => {
  // If there's no data, return
  if (!data || data.length === 0) {
    console.warn('No data to export');
    return;
  }

  let exportData: any[];

  if (columnMapping) {
    // Map data to have the desired column names and order
    exportData = data.map(item => {
      const mappedItem: Record<string, any> = {};
      
      Object.keys(columnMapping).forEach(key => {
        // Handle nested properties (e.g., 'category.name')
        if (key.includes('.')) {
          const parts = key.split('.');
          let value = item;
          for (const part of parts) {
            value = value?.[part];
            if (value === undefined || value === null) break;
          }
          mappedItem[columnMapping[key]] = value !== undefined && value !== null ? value : '';
        } else {
          mappedItem[columnMapping[key]] = item[key] !== undefined && item[key] !== null ? item[key] : '';
        }
      });
      
      return mappedItem;
    });
  } else {
    // Use original data with all columns
    exportData = data.map(item => {
      const flatItem: Record<string, any> = {};
      
      // Flatten nested objects for Excel export
      const flattenObject = (obj: any, prefix = '') => {
        Object.keys(obj).forEach(key => {
          if (
            obj[key] !== null && 
            typeof obj[key] === 'object' && 
            !Array.isArray(obj[key]) &&
            !(obj[key] instanceof Date)
          ) {
            flattenObject(obj[key], `${prefix}${key}.`);
          } else {
            flatItem[`${prefix}${key}`] = obj[key];
          }
        });
      };
      
      flattenObject(item);
      return flatItem;
    });
  }

  // Format dates for Excel
  exportData = exportData.map(row => {
    const formattedRow = { ...row };
    Object.keys(formattedRow).forEach(key => {
      // Check if value is a date
      if (formattedRow[key] instanceof Date) {
        formattedRow[key] = formattedRow[key].toISOString().split('T')[0];
      }
      // Check if value is a string that looks like a date
      else if (typeof formattedRow[key] === 'string' && formattedRow[key].match(/^\d{4}-\d{2}-\d{2}T/)) {
        formattedRow[key] = formattedRow[key].split('T')[0];
      }
      // Handle boolean values for better readability
      else if (typeof formattedRow[key] === 'boolean') {
        formattedRow[key] = formattedRow[key] ? 'Evet' : 'Hayır';
      }
    });
    return formattedRow;
  });

  // Create worksheet
  const worksheet = XLSX.utils.json_to_sheet(exportData);
  
  // Create workbook
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
  
  // Generate buffer
  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  
  // Create a Blob
  const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  
  // Save file
  saveAs(blob, `${filename}.xlsx`);
};

/**
 * Create column mapping from table headers
 * 
 * @param headCells - Array of table header cell definitions
 * @returns A mapping object for exportToExcel
 */
export const createColumnMappingFromHeaders = <T>(
  headCells: Array<{id: keyof T | string, label: string}>,
) => {
  return headCells.reduce((mapping, cell) => {
    if (cell.id !== '') { // Skip action columns or empty IDs
      mapping[cell.id.toString()] = cell.label;
    }
    return mapping;
  }, {} as Record<string, string>);
}; 