import React, { useEffect, lazy, Suspense } from "react";
import { Routes, Route, Navigate, useLocation } from "react-router-dom";
import CssBaseline from "@mui/material/CssBaseline";
import { Box, CircularProgress } from '@mui/material';
import Layout from "./components/layout/Layout";
import ScrollToTop from "./components/layout/ScrollToTop";
import { ThemeProvider } from "./store/ThemeContext";
import { LanguageProvider } from "./i18n/LanguageContext";
import { CategoryProvider } from "./contexts/CategoryContext";
import { AuthProvider } from "./contexts/AuthContext";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { tr } from "date-fns/locale";
import ProtectedRoute from "./components/common/ProtectedRoute";
import CookieConsentBanner from "./components/common/CookieConsentBanner";

import {
  setDefaultConsent,
  loadGTM,
  hasUserConsented,
  getCurrentPreferences,
  applyCookiePreferences,
  handleImplicitConsent,
} from "./utils/cookieConsentUtils";

// Loading component for Suspense fallback
const LoadingComponent = () => (
  <Box
    sx={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      minHeight: "100vh",
      width: '100%'
    }}
  >
    <CircularProgress />
  </Box>
);

// Admin Pages - Lazy Loaded
const DashboardPage = lazy(() => import("./pages/admin/DashboardPage"));
const CategoriesPage = lazy(() => import("./pages/admin/CategoriesPage"));
const CampaignsPage = lazy(() => import("./pages/admin/CampaignsPage"));
const BrandsPage = lazy(() => import("./pages/admin/BrandsPage"));
const CustomersPage = lazy(() => import("./pages/admin/CustomersPage"));
const UsersPage = lazy(() => import("./pages/admin/UsersPage"));
const FormsPage = lazy(() => import("./pages/admin/FormsPage"));
const BrandCategoriesPage = lazy(() => import("./pages/admin/BrandCategoriesPage"));
const BrandCampaignsPage = lazy(() => import("./pages/admin/BrandCampaignsPage"));
const CustomerCampaignsPage = lazy(() => import("./pages/admin/CustomerCampaignsPage"));
const CustomerCampaignsManagementPage = lazy(() => import("./pages/admin/CustomerCampaignsManagementPage"));
const FormCampaignsPage = lazy(() => import("./pages/admin/FormCampaignsPage"));
const CampaignUrlsPage = lazy(() => import("./pages/admin/CampaignUrlsPage"));
const JobsPage = lazy(() => import("./pages/admin/JobsPage"));
const CampaignDetailsPage = lazy(() => import("./pages/admin/CampaignDetailsPage"));
const CampaignImagesPage = lazy(() => import("./pages/admin/CampaignImagesPage"));
const CampaignFormsManagementPage = lazy(() => import("./pages/admin/CampaignFormsManagementPage"));
const SubscribedUsersPage = lazy(() => import("./pages/admin/SubscribedUsersPage"));

// CRM Admin imports - Lazy Loaded
const CrmAdminLayout = lazy(() => import("./components/layout/CrmAdminLayout"));
const CrmDashboardPage = lazy(() => import("./pages/crm-admin/CrmDashboardPage"));
const CrmContactsPage = lazy(() => import("./pages/crm-admin/CrmContactsPage"));
const CrmWebhooksPage = lazy(() => import("./pages/crm-admin/CrmWebhooksPage"));
 const CrmSyncPage = lazy(() => import("./pages/crm-admin/CrmSyncPage"));
const CrmAnalyticsPage = lazy(() => import("./pages/crm-admin/CrmAnalyticsPage"));
const CustomerJourneyPage = lazy(() => import("./pages/crm-admin/CustomerJourneyPage"));
import UnreachableUsersPage from './pages/crm-admin/UnreachableUsersPage';
import CrmDatabasePage from './pages/crm-admin/CrmDatabasePage';
import CrmSettingsPage from './pages/crm-admin/CrmSettingsPage';
const CrmContactsWebhookJoinPage = lazy(() => import("./pages/crm-admin/CrmContactsWebhookJoinPage"));

const Chatbot = lazy(() => import("./components/features/chatbot/Chatbot"));

const AppContent: React.FC = () => {
  const location = useLocation();
  const showChatbot = !location.pathname.startsWith('/admin') && !location.pathname.startsWith('/crm-admin');

  return (
    <>
      <Suspense fallback={<LoadingComponent />}>
        <Routes>
          <Route
            path="/admin"
            element={
              <ProtectedRoute requiredRole="ADMIN">
                <Navigate to="/admin/dashboard" replace />
              </ProtectedRoute>
            }
          />
          <Route path="/admin/dashboard" element={<ProtectedRoute requiredRole="ADMIN"><DashboardPage /></ProtectedRoute>} />
          <Route path="/admin/categories" element={<ProtectedRoute requiredRole="ADMIN"><CategoriesPage /></ProtectedRoute>} />
          <Route path="/admin/campaigns" element={<ProtectedRoute requiredRole="ADMIN"><CampaignsPage /></ProtectedRoute>} />
          <Route path="/admin/brands" element={<ProtectedRoute requiredRole="ADMIN"><BrandsPage /></ProtectedRoute>} />
          <Route path="/admin/customers" element={<ProtectedRoute requiredRole="ADMIN"><CustomersPage /></ProtectedRoute>} />
          <Route path="/admin/users" element={<ProtectedRoute requiredRole="ADMIN"><UsersPage /></ProtectedRoute>} />
          <Route path="/admin/forms" element={<ProtectedRoute requiredRole="ADMIN"><FormsPage /></ProtectedRoute>} />
          <Route path="/admin/brand-categories" element={<ProtectedRoute requiredRole="ADMIN"><BrandCategoriesPage /></ProtectedRoute>} />
          <Route path="/admin/brand-campaigns" element={<ProtectedRoute requiredRole="ADMIN"><BrandCampaignsPage /></ProtectedRoute>} />
          <Route path="/admin/customer-campaigns" element={<ProtectedRoute requiredRole="ADMIN"><CustomerCampaignsPage /></ProtectedRoute>} />
          <Route path="/admin/customer-campaigns-management" element={<ProtectedRoute requiredRole="ADMIN"><CustomerCampaignsManagementPage /></ProtectedRoute>} />
          <Route path="/admin/form-campaigns" element={<ProtectedRoute requiredRole="ADMIN"><FormCampaignsPage /></ProtectedRoute>} />
          <Route path="/admin/campaign-urls" element={<ProtectedRoute requiredRole="ADMIN"><CampaignUrlsPage /></ProtectedRoute>} />
          <Route path="/admin/jobs" element={<ProtectedRoute requiredRole="ADMIN"><JobsPage /></ProtectedRoute>} />
          <Route path="/admin/campaign-details" element={<ProtectedRoute requiredRole="ADMIN"><CampaignDetailsPage /></ProtectedRoute>} />
          <Route path="/admin/campaign-images" element={<ProtectedRoute requiredRole="ADMIN"><CampaignImagesPage /></ProtectedRoute>} />
          <Route path="/admin/campaign-forms-management" element={<ProtectedRoute requiredRole="ADMIN"><CampaignFormsManagementPage /></ProtectedRoute>} />
          <Route path="/admin/subscribed-users" element={<ProtectedRoute requiredRole="ADMIN"><SubscribedUsersPage /></ProtectedRoute>} />

          {/* CRM Admin Routes */}
          <Route
            path="/crm-admin"
            element={
              <ProtectedRoute requiredRole={["ADMIN", "CRM_ADMIN"]}>
                <Navigate to="/crm-admin/dashboard" replace />
              </ProtectedRoute>
            }
          />
          <Route
            path="/crm-admin/*"
            element={
              <ProtectedRoute requiredRole={["ADMIN", "CRM_ADMIN"]}>
                <CrmAdminLayout />
              </ProtectedRoute>
            }
          >
            <Route path="dashboard" element={<CrmDashboardPage />} />
            <Route path="analytics" element={<CrmAnalyticsPage />} />
            <Route path="customer-journey" element={<CustomerJourneyPage />} />
            <Route path="unreachable-users" element={<UnreachableUsersPage />} />
            <Route path="contacts" element={<CrmContactsPage />} />
            <Route path="webhooks" element={<CrmWebhooksPage />} />
            <Route path="sync" element={<CrmSyncPage />} />
            <Route path="contacts-webhook-join" element={<CrmContactsWebhookJoinPage />} />
            <Route path="database" element={<CrmDatabasePage />} />
            <Route path="settings" element={<CrmSettingsPage />} />
          </Route>

          <Route path="/*" element={<Layout />} />
        </Routes>
      </Suspense>
      <ScrollToTop />
      <CookieConsentBanner
        onConsentGiven={() => {
          const preferences = getCurrentPreferences();
          applyCookiePreferences(preferences);
        }}
      />
      <Suspense fallback={null}>
        {showChatbot && <Chatbot />}
      </Suspense>
    </>
  );
};


const App: React.FC = () => {
  useEffect(() => {
    // Uygulama başlatıldığında GTM'i başlat
    setDefaultConsent(); // Önce varsayılan consent'i ayarla
    loadGTM(); // Sonra GTM'i yükle

    // Eğer kullanıcı daha önce consent vermişse, tercihlerini uygula
    if (hasUserConsented()) {
      const preferences = getCurrentPreferences();
      applyCookiePreferences(preferences);
    } else {
      // Kullanıcı consent vermemişse, örtülü consent'i başlat
      handleImplicitConsent();
    }
  }, []);

  return (
    <ThemeProvider>
      <LanguageProvider>
        <CategoryProvider>
          <AuthProvider>
            <LocalizationProvider
              dateAdapter={AdapterDateFns}
              adapterLocale={tr}
            >
              <CssBaseline />
              <AppContent />
            </LocalizationProvider>
          </AuthProvider>
        </CategoryProvider>
      </LanguageProvider>
    </ThemeProvider>
  );
};

export default App;
