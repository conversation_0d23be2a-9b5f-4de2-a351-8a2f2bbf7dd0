import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useIntl } from "react-intl";
import axios from "axios";
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CircularProgress,
  useTheme,
  CardActionArea,
} from "@mui/material";
import ModernBreadcrumbs from "../components/common/ModernBreadcrumbs";

interface Brand {
  id: number;
  name: string;
  logo?: string;
  logoUrl?: string;
  brandUrl?: string;
  isActive?: boolean;
  imagePath?: string;
  [key: string]: unknown;
}

interface Category {
  id: number;
  name: string;
  isActive: boolean;
  parentCategoryId: number | null;
}

interface BrandToCategory {
  id: number;
  brand: Brand;
  category: Category;
  isActive: boolean;
}

const BrandsPage: React.FC = () => {
  const standardNavigate = useNavigate();
  const intl = useIntl();
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === "dark";

  const [categories, setCategories] = useState<Category[]>([]);
  const [brandToCategories, setBrandToCategories] = useState<BrandToCategory[]>(
    []
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Logo işleme için yardımcı fonksiyon
  const renderLogo = (brandId?: number, fallbackUrl?: string) => {
    if (brandId) {
      return `https://360avantajli.com/api/Campaign_Service/brand/${brandId}/image`;
    }
    if (fallbackUrl) return fallbackUrl;
    return "/placeholder-logo.jpg";
  };

  useEffect(() => {
    const fetchAll = async () => {
      setLoading(true);
      setError(null);
      try {
        const [catRes, btcRes] = await Promise.all([
          axios.get("https://360avantajli.com/api/Campaign_Service/category"),
          axios.get(
            "https://360avantajli.com/api/Campaign_Service/brand-to-category"
          ),
        ]);
        if (
          catRes.data &&
          Array.isArray(catRes.data) &&
          btcRes.data &&
          Array.isArray(btcRes.data)
        ) {
          setCategories(catRes.data.filter((c: Category) => c.isActive));
          setBrandToCategories(
            btcRes.data.filter(
              (b: BrandToCategory) => b.isActive && b.brand.isActive
            )
          );
        } else {
          setCategories([]);
          setBrandToCategories([]);
          setError("Veri alınamadı.");
        }
      } catch (err) {
        setError("Veriler yüklenirken hata oluştu.");
        setCategories([]);
        setBrandToCategories([]);
      } finally {
        setLoading(false);
      }
    };
    fetchAll();
  }, []);

  // Kategorileri hiyerarşik olarak gruplama
  const anaKategoriler = categories.filter(
    (cat) => !cat.parentCategoryId || cat.parentCategoryId === 0
  );
  const altKategoriler = categories.filter(
    (cat) => cat.parentCategoryId && cat.parentCategoryId !== 0
  );

  // Alt kategorileri ana kategoriye göre grupla
  const getAltKategoriler = (anaKategoriId: number) =>
    altKategoriler.filter((cat) => cat.parentCategoryId === anaKategoriId);

  // Bir kategoriye ait markaları getir
  const getMarkalarByKategori = (kategoriId: number) =>
    brandToCategories
      .filter((btc) => btc.category.id === kategoriId)
      .map((btc) => btc.brand);

  // Slugify fonksiyonu (Türkçe karakter desteğiyle)
  const slugify = (str: string) =>
    str
      .toLowerCase()
      .replace(/ç/g, "c")
      .replace(/ğ/g, "g")
      .replace(/ı/g, "i")
      .replace(/ö/g, "o")
      .replace(/ş/g, "s")
      .replace(/ü/g, "u")
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-+|-+$/g, "");

  // Ortak BrandCard bileşeni
  const BrandCard = ({
    brand,
    onClick,
  }: {
    brand: Brand;
    onClick: () => void;
  }) => (
    <Card
      sx={{
        minHeight: { xs: 140, sm: 170, md: 200 },
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        borderRadius: { xs: 2, sm: 3 },
        boxShadow: "0 2px 12px 0 rgba(60,72,100,0.10)",
        border: "none",
        transition: "transform 0.2s, box-shadow 0.2s",
        cursor: "pointer",
        "&:hover": {
          transform: {
            xs: "translateY(-2px)",
            sm: "translateY(-6px) scale(1.04)",
          },
          boxShadow: `0 8px 30px rgba(60,72,100,0.18)`,
        },
      }}
    >
      <CardActionArea
        sx={{
          height: "100%",
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          p: { xs: 1.5, sm: 2 },
        }}
        onClick={onClick}
      >
        <Box
          sx={{
            width: { xs: 60, sm: 70, md: 80 },
            height: { xs: 60, sm: 70, md: 80 },
            borderRadius: "50%",
            backgroundColor: "#fff",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            mb: { xs: 1.5, sm: 2 },
            boxShadow: `0 2px 8px rgba(60,72,100,0.07)`,
            border: `1px solid #ececec`,
            overflow: "hidden",
          }}
        >
          <img
            src={renderLogo(brand.id, brand.logo || brand.logoUrl)}
            alt={`${brand.name} logo`}
            style={{
              maxWidth: "100%",
              maxHeight: "100%",
              objectFit: "contain",
            }}
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.onerror = null;
              target.src = "/placeholder-logo.jpg";
            }}
          />
        </Box>
        <Typography
          variant="subtitle1"
          component="h2"
          sx={{
            fontWeight: 700,
            fontSize: { xs: "0.875rem", sm: "1rem" },
            color: "text.primary",
            textAlign: "center",
            lineHeight: 1.3,
            px: { xs: 0.5, sm: 0 },
          }}
        >
          {brand.name}
        </Typography>
      </CardActionArea>
    </Card>
  );

  const breadcrumbItems = [
    {
      label: intl.formatMessage({ id: "breadcrumb.home" }),
      to: "/",
    },
    {
      label: intl.formatMessage({ id: "breadcrumb.brands" }),
    },
  ];

  return (
    <Box>
      <ModernBreadcrumbs items={breadcrumbItems} />
      <Box
        sx={{ py: { xs: 2, sm: 4 }, background: "#f7f8fa", minHeight: "100vh" }}
      >
        <Container maxWidth="lg" sx={{ px: { xs: 1, sm: 2, md: 3 } }}>
          {/* Page Title */}
          <Typography
            variant="h4"
            component="h1"
            gutterBottom
            sx={{
              mb: { xs: 3, sm: 4 },
              fontWeight: 800,
              color: "primary.main",
              letterSpacing: { xs: 0.5, sm: 1 },
              fontSize: { xs: "1.75rem", sm: "2.125rem" },
              textAlign: { xs: "center", sm: "left" },
              px: { xs: 1, sm: 0 },
            }}
          >
            {intl.formatMessage({ id: "breadcrumb.brands" })}
          </Typography>

          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center", py: 4 }}>
              <CircularProgress size={40} />
            </Box>
          ) : error ? (
            <Typography color="error" align="center" sx={{ py: 2 }}>
              {error}
            </Typography>
          ) : (
            <Box>
              {anaKategoriler.map((anaKategori) => {
                // Alt kategorilerden en az birinde marka varsa veya ana kategorinin kendisinde marka varsa göster
                const altKats = getAltKategoriler(anaKategori.id);
                const anaKategoriMarkalar = getMarkalarByKategori(
                  anaKategori.id
                );
                const altKategorilerVeMarkalar = altKats
                  .map((altKategori) => ({
                    altKategori,
                    markalar: getMarkalarByKategori(altKategori.id),
                  }))
                  .filter(({ markalar }) => markalar.length > 0);
                if (
                  altKategorilerVeMarkalar.length === 0 &&
                  anaKategoriMarkalar.length === 0
                )
                  return null;
                return (
                  <Box
                    key={anaKategori.id}
                    sx={{ mb: { xs: 4, sm: 6, md: 8 } }}
                  >
                    {/* Ana kategori başlığı */}
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        mb: { xs: 2, sm: 2 },
                        mx: { xs: 1, sm: 0 },
                      }}
                    >
                      <Box
                        sx={{
                          width: { xs: 4, sm: 6 },
                          height: { xs: 24, sm: 36 },
                          bgcolor: "primary.main",
                          borderRadius: 2,
                          mr: { xs: 1.5, sm: 2 },
                        }}
                      />
                      <Typography
                        variant="h5"
                        sx={{
                          fontWeight: 800,
                          fontSize: { xs: 20, sm: 24, md: 28 },
                          color: "primary.main",
                          letterSpacing: { xs: 0.5, sm: 1 },
                          lineHeight: 1.2,
                        }}
                      >
                        {intl.formatMessage({
                          id: `category.${anaKategori.name
                            .toLowerCase()
                            .replace(/\s+/g, "")}`,
                          defaultMessage: anaKategori.name,
                        })}
                      </Typography>
                    </Box>
                    {/* Alt kategoriler */}
                    {altKategorilerVeMarkalar.length > 0
                      ? altKategorilerVeMarkalar.map(
                          ({ altKategori, markalar }) => (
                            <Box
                              key={altKategori.id}
                              sx={{
                                mb: { xs: 3, sm: 5 },
                                ml: { xs: 1, sm: 2, md: 4 },
                                mr: { xs: 1, sm: 0 },
                              }}
                            >
                              <Typography
                                variant="subtitle1"
                                sx={{
                                  fontWeight: 700,
                                  fontSize: { xs: 16, sm: 18, md: 20 },
                                  mb: 1,
                                  color: "text.secondary",
                                  textAlign: { xs: "left", sm: "left" },
                                }}
                              >
                                {intl.formatMessage({
                                  id: `category.${altKategori.name
                                    .toLowerCase()
                                    .replace(/\s+/g, "")}`,
                                  defaultMessage: altKategori.name,
                                })}
                              </Typography>
                              <Box
                                sx={{
                                  borderBottom: "1px solid #ececec",
                                  mb: 2,
                                  width: "100%",
                                }}
                              />
                              <Grid
                                container
                                spacing={{ xs: 2, sm: 3, md: 4 }}
                                justifyContent="flex-start"
                              >
                                {markalar.map((brand) => (
                                  <Grid
                                    item
                                    xs={6}
                                    sm={6}
                                    md={4}
                                    lg={3}
                                    key={brand.id}
                                  >
                                    <BrandCard
                                      brand={brand}
                                      onClick={() => {
                                        let brandSlug: string;
                                        if (brand.name) {
                                          brandSlug = slugify(brand.name);
                                        } else if (
                                          brand.brandUrl &&
                                          !brand.brandUrl.includes("://")
                                        ) {
                                          brandSlug = brand.brandUrl;
                                        } else {
                                          brandSlug = brand.id.toString();
                                        }
                                        // Kategori adı slug'ı ile route'a ekle
                                        const categoryName = altKategori
                                          ? altKategori.name
                                          : anaKategori.name;
                                        standardNavigate(
                                          `/marka/${brandSlug}/${slugify(
                                            categoryName
                                          )}`
                                        );
                                      }}
                                    />
                                  </Grid>
                                ))}
                              </Grid>
                            </Box>
                          )
                        )
                      : // Eğer alt kategori yoksa, ana kategoriye bağlı markaları göster
                        (() => {
                          if (anaKategoriMarkalar.length === 0) return null;
                          return (
                            <Grid
                              container
                              spacing={{ xs: 2, sm: 3, md: 4 }}
                              justifyContent="flex-start"
                              sx={{
                                ml: { xs: 1, sm: 2, md: 4 },
                                mr: { xs: 1, sm: 0 },
                              }}
                            >
                              {anaKategoriMarkalar.map((brand) => (
                                <Grid
                                  item
                                  xs={6}
                                  sm={6}
                                  md={4}
                                  lg={3}
                                  key={brand.id}
                                >
                                  <BrandCard
                                    brand={brand}
                                    onClick={() => {
                                      let brandSlug: string;
                                      if (brand.name) {
                                        brandSlug = slugify(brand.name);
                                      } else if (
                                        brand.brandUrl &&
                                        !brand.brandUrl.includes("://")
                                      ) {
                                        brandSlug = brand.brandUrl;
                                      } else {
                                        brandSlug = brand.id.toString();
                                      }
                                      // Kategori adı slug'ı ile route'a ekle
                                      const categoryName = anaKategori.name;
                                      standardNavigate(
                                        `/marka/${brandSlug}/${slugify(
                                          categoryName
                                        )}`
                                      );
                                    }}
                                  />
                                </Grid>
                              ))}
                            </Grid>
                          );
                        })()}
                  </Box>
                );
              })}
            </Box>
          )}
        </Container>
      </Box>
    </Box>
  );
};

export default BrandsPage;
