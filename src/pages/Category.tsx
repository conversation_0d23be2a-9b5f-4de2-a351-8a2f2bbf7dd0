import React from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Typography,
  Button,
} from '@mui/material';

<Grid container spacing={3}>
  {selectedCategory?.campaigns?.map((campaign) => (
    <Grid item xs={12} sm={6} md={4} key={campaign.id}>
      <Card
        component={RouterLink}
        to={`/campaign/${campaign.id}`}
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          textDecoration: 'none',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: 4,
            transition: 'all 0.3s ease-in-out'
          }
        }}
      >
        <CardMedia
          component="img"
          height="250" // 200'den 250'ye yükseltildi
          image={campaign.imageUrl}
          alt={campaign.title}
          sx={{
            objectFit: 'cover',
            objectPosition: 'center',
          }}
        />
        <CardContent sx={{ flexGrow: 1 }}>
          <Typography gutterBottom variant="h6" component="div">
            {campaign.title}
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              display: '-webkit-box',
              WebkitLineClamp: 4,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              wordBreak: 'break-word',
              hyphens: 'auto',
              minHeight: '4.8rem',
              maxHeight: '4.8rem',
              lineHeight: 1.4,
            }}
          >
            {campaign.description}
          </Typography>
        </CardContent>
        <CardActions>
          <Button size="small" color="primary">
            Detayları Gör
          </Button>
          {campaign.price && (
            <Typography
              variant="subtitle1"
              color="primary"
              sx={{ ml: 'auto' }}
            >
              {campaign.price}
            </Typography>
          )}
        </CardActions>
      </Card>
    </Grid>
  ))}
</Grid>