import React, { useState, useEffect } from "react";
import { useNavigate, useLocation, Link as RouterLink } from "react-router-dom";
import { useIntl } from "react-intl";
import { useAuth } from "../contexts/AuthContext";
import {
  Box,
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  FormControlLabel,
  Checkbox,
  Alert,
  CircularProgress,
  useTheme,
  Link,
} from "@mui/material";
import { LockOutlined as LockIcon } from "@mui/icons-material";

const LoginPage: React.FC = () => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);

  const { login, isAuthenticated, isLoading, loginError } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const intl = useIntl();
  const theme = useTheme();

  // Get the redirect path from location state or default to '/'
  // If the path contains '/admin', keep it as is, otherwise default to '/'
  const from = (location.state as any)?.from?.pathname || "/";

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      // Doğrudan yönlendirme yap, başarı mesajı gösterme
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, from]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!username || !password) {
      return;
    }

    await login(username, password, rememberMe);

    // If remember me is checked, save username to localStorage
    if (rememberMe) {
      localStorage.setItem("rememberedUsername", username);
    } else {
      localStorage.removeItem("rememberedUsername");
      sessionStorage.setItem("rememberedUsername", username); // Geçici olarak session'a kaydet
    }
  };

  // Load remembered username if available
  useEffect(() => {
    const rememberedUsername = localStorage.getItem("rememberedUsername");
    if (rememberedUsername) {
      setUsername(rememberedUsername);
      setRememberMe(true);
    }
  }, []);

  return (
    <Container component="main" maxWidth="xs" sx={{ px: { xs: 2, sm: 3 } }}>
      <Box
        sx={{
          marginTop: { xs: 4, sm: 6, md: 8 },
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          mb: { xs: 4, sm: 6, md: 8 },
          px: { xs: 1, sm: 0 },
        }}
      >
        <Paper
          elevation={3}
          sx={{
            p: { xs: 2, sm: 3, md: 4 },
            width: "100%",
            borderRadius: 2,
            boxShadow: "0 8px 24px rgba(149, 157, 165, 0.2)",
          }}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            <Box
              sx={{
                width: 56,
                height: 56,
                borderRadius: "50%",
                bgcolor: theme.palette.primary.main,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                mb: 2,
              }}
            >
              <LockIcon sx={{ color: "white", fontSize: 28 }} />
            </Box>

            <Typography component="h1" variant="h5" fontWeight={600} mb={3}>
              {intl.formatMessage({ id: "login.title" })}
            </Typography>

            {loginError && (
              <Alert severity="error" sx={{ width: "100%", mb: 2 }}>
                {loginError}
              </Alert>
            )}

            <Box
              component="form"
              onSubmit={handleSubmit}
              sx={{ mt: 1, width: "100%" }}
            >
              <TextField
                margin="normal"
                required
                fullWidth
                id="username"
                label={intl.formatMessage({ id: "login.username" })}
                name="username"
                autoComplete="username"
                autoFocus
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                disabled={isLoading}
              />

              <TextField
                margin="normal"
                required
                fullWidth
                name="password"
                label={intl.formatMessage({ id: "login.password" })}
                type="password"
                id="password"
                autoComplete="current-password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={isLoading}
              />

              <FormControlLabel
                control={
                  <Checkbox
                    value="remember"
                    color="primary"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                    disabled={isLoading}
                  />
                }
                label={intl.formatMessage({ id: "login.rememberMe" })}
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                sx={{
                  mt: 3,
                  mb: 2,
                  py: 1.5,
                  borderRadius: "12px",
                  textTransform: "none",
                  fontWeight: 600,
                  fontSize: "1rem",
                  boxShadow:
                    "0 8px 16px rgba(0, 0, 0, 0.1), inset 0 -2px 4px rgba(0, 0, 0, 0.05)",
                  background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                  backdropFilter: "blur(8px)",
                  color: "#fff",
                  border: "none",
                  transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                  "&:hover": {
                    boxShadow:
                      "0 12px 24px rgba(0, 0, 0, 0.15), inset 0 -2px 6px rgba(0, 0, 0, 0.08)",
                    transform: "translateY(-2px)",
                    background: `linear-gradient(135deg, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`,
                    border: "none",
                  },
                  "&:active": {
                    transform: "translateY(0)",
                    boxShadow:
                      "0 4px 8px rgba(0, 0, 0, 0.1), inset 0 -1px 2px rgba(0, 0, 0, 0.05)",
                  },
                }}
                disabled={isLoading}
              >
                {isLoading ? (
                  <CircularProgress size={24} color="inherit" />
                ) : (
                  intl.formatMessage({ id: "login.submit" })
                )}
              </Button>

              <Box
                sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}
              >
                <Link href="#" variant="body2" color="primary.main">
                  {intl.formatMessage({ id: "login.forgotPassword" })}
                </Link>
                <Link
                  component={RouterLink}
                  to="/register"
                  variant="body2"
                  color="primary.main"
                >
                  {intl.formatMessage({ id: "login.register" })}
                </Link>
              </Box>
            </Box>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default LoginPage;
