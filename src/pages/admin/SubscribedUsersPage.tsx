import React, { useState, useEffect, useMemo } from "react";
import axios from "axios";
import { useAuth } from "../../contexts/AuthContext";
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  CircularProgress,
  TableSortLabel,
  Card,
  CardContent,
  useTheme,
  Checkbox,
  Toolbar,
  alpha,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  OutlinedInput,
  Chip,
  ListItemText,
  SelectChangeEvent,
} from "@mui/material";
import { Email as EmailIcon } from "@mui/icons-material";
import AdminLayout from "../../components/admin/AdminLayout";
import ExportButton from "../../components/admin/ExportButton";
import {
  maskCustomersArray,
  conditionalMask,
} from "../../utils/dataMaskingUtils";

interface Campaign {
  id: number;
  name: string;
  category: {
    id: number;
    name: string;
  };
}

interface CustomerCampaign {
  id: number;
  customer: Customer;
  campaign: Campaign;
  isActive: boolean;
  // <PERSON><PERSON>er alanlar gerekirse eklenebilir
}

interface Customer {
  id: number;
  name: string;
  surname: string;
  email: string;
  phoneNumber: string;
  country: string;
  city: string;
  town: string;
  gender: string;
  jobId: number;
  remindMe: boolean;
  birthday: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  job: {
    id: number;
    description: string;
    active?: boolean;
  };
  interestedCategories?: string[];
}

// Sorting type definition
type Order = "asc" | "desc";

interface HeadCell {
  id: keyof Customer | "select" | "action" | "interestedCategories";
  label: string;
  numeric: boolean;
  sortable: boolean;
}

export default function SubscribedUsersPage() {
  const { token } = useAuth();
  const theme = useTheme();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [customerCampaigns, setCustomerCampaigns] = useState<
    CustomerCampaign[]
  >([]);
  const [isLoading, setIsLoading] = useState(false);
  const [errorDialog, setErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [selected, setSelected] = useState<number[]>([]);
  const [categoryFilter, setCategoryFilter] = useState<string[]>([]);

  // Sorting states
  const [order, setOrder] = useState<Order>("asc");
  const [orderBy, setOrderBy] = useState<keyof Customer>("id");

  useEffect(() => {
    const fetchInitialData = async () => {
      setIsLoading(true);
      try {
        const [customersRes, campaignsRes, customerCampaignsRes] =
          await Promise.all([
            axios.get("https://360avantajli.com/api/Auth_Service/customer", {
              headers: { Authorization: `Bearer ${token}` }
            }),
            axios.get("https://360avantajli.com/api/Campaign_Service/campaign", {
              headers: { Authorization: `Bearer ${token}` }
            }),
            axios.get(
              "https://360avantajli.com/api/Campaign_Service/customer-to-campaign",
              { headers: { Authorization: `Bearer ${token}` } }
            ),
          ]);

        const subscribedCustomers = (customersRes.data || []).filter(
          (c: Customer) => c.remindMe
        );
        setCustomers(subscribedCustomers);
        setCampaigns(campaignsRes.data || []);
        setCustomerCampaigns(customerCampaignsRes.data || []);
      } catch (error: any) {
        setCustomers([]);
        if (error.response?.data?.message) {
          setErrorMessage(error.response.data.message);
        } else if (error.message) {
          setErrorMessage(error.message);
        } else {
          setErrorMessage("Bilinmeyen bir hata oluştu.");
        }
        setErrorDialog(true);
      } finally {
        setIsLoading(false);
      }
    };

    fetchInitialData();
  }, []);

  const allCategories = useMemo(() => {
    const categories = new Set<string>();
    campaigns.forEach((campaign) => {
      if (campaign.category && campaign.category.name) {
        categories.add(campaign.category.name);
      }
    });
    return Array.from(categories).sort();
  }, [campaigns]);

  const customersWithCategories = useMemo(() => {
    if (!customers.length || !campaigns.length || !customerCampaigns.length) {
      return customers;
    }
    const campaignsById = new Map(campaigns.map((c) => [c.id, c]));
    return customers.map((customer) => {
      // Bu müşterinin tüm aktif campaign ilişkilerini bul
      const userCampaignRelations = customerCampaigns.filter(
        (cc) => cc.customer.id === customer.id && cc.isActive
      );
      const categoryCounts: { [key: string]: number } = {};
      userCampaignRelations.forEach((cc) => {
        const campaign = campaignsById.get(cc.campaign.id);
        if (campaign && campaign.category && campaign.category.name) {
          const categoryName = campaign.category.name;
          categoryCounts[categoryName] =
            (categoryCounts[categoryName] || 0) + 1;
        }
      });
      const sortedCategories = Object.keys(categoryCounts).sort(
        (a, b) => categoryCounts[b] - categoryCounts[a]
      );
      return {
        ...customer,
        interestedCategories: sortedCategories.slice(0, 3), // En çok başvurduğu 3 kategori
      };
    });
  }, [customers, campaigns, customerCampaigns]);

  const filteredCustomers = useMemo(() => {
    if (categoryFilter.length === 0) {
      return customersWithCategories;
    }
    return customersWithCategories.filter((customer) =>
      categoryFilter.some((category) =>
        customer.interestedCategories?.includes(category)
      )
    );
  }, [customersWithCategories, categoryFilter]);

  const handleCategoryFilterChange = (
    event: SelectChangeEvent<typeof categoryFilter>
  ) => {
    const {
      target: { value },
    } = event;
    setCategoryFilter(typeof value === "string" ? value.split(",") : value);
  };

  const handleSendEmail = (email: string) => {
    const subject = encodeURIComponent(
      "Kampanyalarımız Hakkında Bilgilendirme"
    );
    const body = encodeURIComponent(
      `Merhaba,\n\nİlginizi çekeceğini düşündüğümüz yeni kampanyalarımız hakkında sizi bilgilendirmek istedik.\n\nDetaylar için web sitemizi ziyaret edebilirsiniz.\n\nİyi günler dileriz.`
    );
    window.location.href = `mailto:${email}?subject=${subject}&body=${body}`;
  };

  const handleSendBulkEmail = () => {
    const selectedEmails = customers
      .filter((customer) => selected.includes(customer.id))
      .map((customer) => customer.email);

    if (selectedEmails.length === 0) {
      alert("Lütfen en az bir kullanıcı seçin.");
      return;
    }

    const subject = encodeURIComponent(
      "Kampanyalarımız Hakkında Bilgilendirme"
    );
    const body = encodeURIComponent(
      `Merhaba,\n\nİlginizi çekeceğini düşündüğümüz yeni kampanyalarımız hakkında sizi bilgilendirmek istedik.\n\nDetaylar için web sitemizi ziyaret edebilirsiniz.\n\nİyi günler dileriz.`
    );
    const bcc = selectedEmails.join(",");
    window.location.href = `mailto:?bcc=${bcc}&subject=${subject}&body=${body}`;
  };

  // Sorting functions
  function descendingComparator<T>(a: T, b: T, orderBy: keyof T) {
    if (b[orderBy] < a[orderBy]) {
      return -1;
    }
    if (b[orderBy] > a[orderBy]) {
      return 1;
    }
    return 0;
  }

  function getComparator(
    order: Order,
    orderBy: keyof Customer
  ): (a: Customer, b: Customer) => number {
    return order === "desc"
      ? (a, b) => descendingComparator(a, b, orderBy)
      : (a, b) => -descendingComparator(a, b, orderBy);
  }

  function stableSort<T>(
    array: readonly T[],
    comparator: (a: T, b: T) => number
  ) {
    const stabilizedThis = array.map((el, index) => [el, index] as [T, number]);
    stabilizedThis.sort((a, b) => {
      const order = comparator(a[0], b[0]);
      if (order !== 0) return order;
      return a[1] - b[1];
    });
    return stabilizedThis.map((el) => el[0]);
  }

  const handleRequestSort = (property: keyof Customer) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
  };

  const handleSelectAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelecteds = filteredCustomers.map((n) => n.id);
      setSelected(newSelecteds);
      return;
    }
    setSelected([]);
  };

  const handleClick = (event: React.MouseEvent<unknown>, id: number) => {
    const selectedIndex = selected.indexOf(id);
    let newSelected: number[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      );
    }
    setSelected(newSelected);
  };

  const isSelected = (id: number) => selected.indexOf(id) !== -1;

  const headCells: HeadCell[] = [
    { id: "select", numeric: false, label: "", sortable: false },
    { id: "id", numeric: true, label: "ID", sortable: true },
    { id: "name", numeric: false, label: "Ad", sortable: true },
    { id: "surname", numeric: false, label: "Soyad", sortable: true },
    { id: "email", numeric: false, label: "E-posta", sortable: true },
    { id: "phoneNumber", numeric: false, label: "Telefon", sortable: true },
    {
      id: "interestedCategories",
      numeric: false,
      label: "İlgilendiği Kategoriler",
      sortable: false,
    },
    { id: "action", numeric: false, label: "E-posta Gönder", sortable: false },
  ];

  const numSelected = selected.length;
  const rowCount = filteredCustomers.length;

  // Excel export için render fonksiyonu
  const renderExcelExport = () => {
    return (
      <ExportButton
        data={filteredCustomers}
        filename={`Abone_Kullanıcılar_${
          new Date().toISOString().split("T")[0]
        }`}
        headCells={[
          { id: "id", label: "ID" },
          { id: "email", label: "E-posta" },
          { id: "campaigns", label: "Kampanyalar" },
          { id: "createdAt", label: "Abone Olma Tarihi" },
        ]}
        disabled={filteredCustomers.length === 0}
        tooltip="Tüm abone kullanıcıları Excel olarak indir"
      />
    );
  };

  // Başlık bileşeni güncellemesi
  const renderToolbar = () => {
    return (
      <Toolbar
        sx={{
          pl: { sm: 2 },
          pr: { xs: 1, sm: 1 },
          bgcolor:
            theme.palette.mode === "light"
              ? alpha(theme.palette.primary.main, 0.1)
              : alpha(theme.palette.primary.dark, 0.2),
          borderRadius: 1,
          mb: 2,
        }}
      >
        <Typography
          sx={{ flex: "1 1 100%" }}
          variant="h6"
          id="tableTitle"
          component="div"
        >
          Aboneler
        </Typography>

        <Box sx={{ display: "flex", gap: 1 }}>
          {renderExcelExport()}

          <Button
            variant="contained"
            startIcon={<EmailIcon />}
            onClick={handleSendBulkEmail}
            disabled={selected.length === 0}
          >
            {selected.length} Kişiye E-posta Gönder
          </Button>
        </Box>
      </Toolbar>
    );
  };

  return (
    <AdminLayout>
      <Box
        sx={{
          mb: 2,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography variant="h5" component="h1">
          Abone Kullanıcılar
        </Typography>
        <ExportButton
          data={filteredCustomers}
          filename={`Abone_Kullanıcılar_${
            new Date().toISOString().split("T")[0]
          }`}
          headCells={[
            { id: "id", label: "ID" },
            { id: "email", label: "E-posta" },
            { id: "campaigns", label: "Kampanyalar" },
            { id: "createdAt", label: "Abone Olma Tarihi" },
          ]}
          disabled={filteredCustomers.length === 0}
          tooltip="Tüm abone kullanıcıları Excel olarak indir"
        />
      </Box>

      {/* Kampanya seçici */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <FormControl fullWidth>
            <InputLabel id="campaign-filter-label">
              Kampanyalara Göre Filtrele
            </InputLabel>
            <Select
              labelId="campaign-filter-label"
              id="campaign-filter"
              multiple
              value={categoryFilter}
              onChange={handleCategoryFilterChange}
              input={<OutlinedInput label="Kampanyalara Göre Filtrele" />}
              renderValue={(selected) => (
                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                  {selected.map((value) => (
                    <Chip key={value} label={value} />
                  ))}
                </Box>
              )}
            >
              {allCategories.map((category) => (
                <MenuItem key={category} value={category}>
                  <Checkbox checked={categoryFilter.indexOf(category) > -1} />
                  <ListItemText primary={category} />
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </CardContent>
      </Card>

      {renderToolbar()}

      <Paper sx={{ width: "100%", mb: 2 }}>
        {isLoading ? (
          <CircularProgress sx={{ display: "block", margin: "auto", my: 4 }} />
        ) : (
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox">
                    <Checkbox
                      color="primary"
                      indeterminate={numSelected > 0 && numSelected < rowCount}
                      checked={rowCount > 0 && numSelected === rowCount}
                      onChange={handleSelectAllClick}
                      inputProps={{
                        "aria-label": "select all users",
                      }}
                    />
                  </TableCell>
                  {headCells.slice(1).map((headCell) => (
                    <TableCell
                      key={headCell.id}
                      sortDirection={orderBy === headCell.id ? order : false}
                    >
                      {headCell.sortable ? (
                        <TableSortLabel
                          active={orderBy === headCell.id}
                          direction={orderBy === headCell.id ? order : "asc"}
                          onClick={() =>
                            handleRequestSort(headCell.id as keyof Customer)
                          }
                        >
                          {headCell.label}
                        </TableSortLabel>
                      ) : (
                        headCell.label
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {conditionalMask(
                  stableSort(filteredCustomers, getComparator(order, orderBy)),
                  maskCustomersArray
                ).map((customer: Customer) => {
                  const isItemSelected = isSelected(customer.id);
                  const labelId = `enhanced-table-checkbox-${customer.id}`;

                  return (
                    <TableRow
                      hover
                      onClick={(event) => handleClick(event, customer.id)}
                      role="checkbox"
                      aria-checked={isItemSelected}
                      tabIndex={-1}
                      key={customer.id}
                      selected={isItemSelected}
                    >
                      <TableCell padding="checkbox">
                        <Checkbox
                          color="primary"
                          checked={isItemSelected}
                          inputProps={{
                            "aria-labelledby": labelId,
                          }}
                        />
                      </TableCell>
                      <TableCell>{customer.id}</TableCell>
                      <TableCell>{customer.name}</TableCell>
                      <TableCell>{customer.surname}</TableCell>
                      <TableCell>{customer.email}</TableCell>
                      <TableCell>{customer.phoneNumber}</TableCell>
                      <TableCell>
                        {customer.interestedCategories &&
                        customer.interestedCategories.length > 0
                          ? customer.interestedCategories.join(", ")
                          : "Veri Yok"}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="contained"
                          color="primary"
                          startIcon={<EmailIcon />}
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent row click
                            handleSendEmail(customer.email);
                          }}
                        >
                          E-posta Gönder
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>
    </AdminLayout>
  );
}
