import React, { useState, useEffect, useMemo } from "react";
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  Grid,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
  LinearProgress,
} from "@mui/material";
import { DataGrid, GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import {
  Visibility as ViewIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  Person as PersonIcon,
  Campaign as CampaignIcon,
  Refresh as RefreshIcon,
  Close as CloseIcon,
  TrendingUp as TrendingUpIcon,
} from "@mui/icons-material";
import AdminLayout from "../../components/admin/AdminLayout";
import AdminFilterBar, {
  AdminFilterData,
} from "../../components/admin/AdminFilterBar";
import CustomerCampaignExcelExport, {
  CustomerCampaign,
} from "../../components/admin/CustomerCampaignExcelExport";
import axios from "axios";
import { useAuth } from "../../contexts/AuthContext";
import { formatDateWithTime } from "../../utils/dateUtils";
import {
  maskCustomerData,
  conditionalMask,
} from "../../utils/dataMaskingUtils";

interface Customer {
  id: number;
  name: string;
  surname: string;
  email: string;
  phoneNumber: string;
  country: string;
  city: string;
  town: string;
  gender: string;
  birthday: string | null;
  username: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string | null;
  job?: { description: string };
  lastLogin?: string;
}

interface Campaign {
  id: number;
  name: string;
  title: string;
  description: string;
  title2?: string;
  description2?: string;
  title3?: string;
  description3?: string;
  category: {
    id: number;
    name: string;
    isActive: boolean;
    parentCategoryId: number;
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string | null;
}

const CustomerCampaignsManagementPage: React.FC = () => {
  const { token } = useAuth();
  const [customerCampaigns, setCustomerCampaigns] = useState<
    CustomerCampaign[]
  >([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedCustomerCampaign, setSelectedCustomerCampaign] =
    useState<CustomerCampaign | null>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  const [filterData, setFilterData] = useState<AdminFilterData>({
    searchText: "",
    isActive: "all",
    dateRange: {
      startDate: null,
      endDate: null,
    },
    searchIn: {
      name: true,
      email: true,
      phone: true,
      campaign: true,
      category: true,
      city: true,
    },
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        fetchCustomerCampaigns(),
        fetchCustomers(),
        fetchCampaigns(),
      ]);
    } catch {
      // Error fetching data
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCustomerCampaigns = async () => {
    try {
      const response = await axios.get(
        "https://360avantajli.com/api/Campaign_Service/customer-to-campaign",
        { headers: { Authorization: `Bearer ${token}` } }
      );
      if (response.data && Array.isArray(response.data)) {
        setCustomerCampaigns(response.data);
      } else {
        setCustomerCampaigns([]);
      }
    } catch {
      setCustomerCampaigns([]);
    }
  };

  const fetchCustomers = async () => {
    try {
      const response = await axios.get(
        "https://360avantajli.com/api/Auth_Service/customer",
        { headers: { Authorization: `Bearer ${token}` } }
      );
      if (response.data && Array.isArray(response.data)) {
        setCustomers(response.data);
      } else {
        setCustomers([]);
      }
    } catch {
      setCustomers([]);
    }
  };

  const fetchCampaigns = async () => {
    try {
      const response = await axios.get(
        "https://360avantajli.com/api/Campaign_Service/campaign",
        { headers: { Authorization: `Bearer ${token}` } }
      );
      if (response.data && Array.isArray(response.data)) {
        setCampaigns(response.data);
      } else {
        setCampaigns([]);
      }
    } catch {
      setCampaigns([]);
    }
  };

  const handleViewCustomerCampaign = (customerCampaign: CustomerCampaign) => {
    setSelectedCustomerCampaign(customerCampaign);
    setViewDialogOpen(true);
  };

  // Memoize filtered data for better performance
  const filteredCustomerCampaigns = useMemo(() => {
    return customerCampaigns.filter((item) => {
      // Arama metni kontrolü
      let matchesSearch = true;
      if (filterData.searchText) {
        const searchText = filterData.searchText.toLowerCase();
        const searchIn = filterData.searchIn || {};

        const searchMatches: boolean[] = [];

        // Müşteri adı ve soyadı araması
        if (searchIn.name !== false) {
          searchMatches.push(
            (typeof item.customer.name === "string" &&
              item.customer.name.toLowerCase().includes(searchText)) ||
              (typeof item.customer.surname === "string" &&
                item.customer.surname.toLowerCase().includes(searchText)) ||
              ((item.customer.name || "") + " " + (item.customer.surname || ""))
                .toLowerCase()
                .includes(searchText)
          );
        }

        // E-posta araması
        if (searchIn.email !== false) {
          searchMatches.push(
            typeof item.customer.email === "string" &&
              item.customer.email.toLowerCase().includes(searchText)
          );
        }

        // Telefon araması
        if (searchIn.phone !== false) {
          searchMatches.push(
            typeof item.customer.phoneNumber === "string" &&
              item.customer.phoneNumber.includes(searchText)
          );
        }

        // Şehir araması
        if (searchIn.city !== false) {
          searchMatches.push(
            (typeof item.customer.city === "string" &&
              item.customer.city.toLowerCase().includes(searchText)) ||
              (typeof item.customer.country === "string" &&
                item.customer.country.toLowerCase().includes(searchText)) ||
              (typeof item.customer.town === "string" &&
                item.customer.town.toLowerCase().includes(searchText))
          );
        }

        // Kampanya araması
        if (searchIn.campaign !== false) {
          searchMatches.push(
            typeof item.campaign.name === "string" &&
              item.campaign.name.toLowerCase().includes(searchText)
          );
        }

        // Kategori araması
        if (searchIn.category !== false) {
          searchMatches.push(
            typeof item.campaign.category?.name === "string" &&
              item.campaign.category.name.toLowerCase().includes(searchText)
          );
        }

        matchesSearch = searchMatches.some((match) => match);
      }

      // Aktiflik durumu filtresi
      const matchesActive =
        filterData.isActive === "all" ||
        (filterData.isActive === "active" && item.isActive) ||
        (filterData.isActive === "inactive" && !item.isActive);

      // Müşteri filtresi
      const matchesCustomer =
        !filterData.customerId ||
        item.customer.id.toString() === filterData.customerId;

      // Kampanya filtresi
      const matchesCampaign =
        !filterData.campaignId ||
        item.campaign.id.toString() === filterData.campaignId;

      // Kategori filtresi
      const matchesCategory =
        !filterData.categoryId ||
        item.campaign.category?.id.toString() === filterData.categoryId;

      // Şehir filtresi
      const matchesCity =
        !filterData.cityFilter ||
        (item.customer.city &&
          item.customer.city
            .toLowerCase()
            .includes(filterData.cityFilter.toLowerCase()));

      // Tarih aralığı filtresi
      let matchesDateRange = true;
      if (filterData.dateRange?.startDate || filterData.dateRange?.endDate) {
        const relationDate = new Date(item.createdAt);

        if (filterData.dateRange.startDate) {
          const startDate = new Date(filterData.dateRange.startDate);
          matchesDateRange = matchesDateRange && relationDate >= startDate;
        }

        if (filterData.dateRange.endDate) {
          const endDate = new Date(filterData.dateRange.endDate);
          endDate.setHours(23, 59, 59, 999);
          matchesDateRange = matchesDateRange && relationDate <= endDate;
        }
      }

      return (
        matchesSearch &&
        matchesActive &&
        matchesCustomer &&
        matchesCampaign &&
        matchesCategory &&
        matchesCity &&
        matchesDateRange
      );
    });
  }, [customerCampaigns, filterData]);

  // Calculate statistics
  const statistics = useMemo(() => {
    const totalRelations = customerCampaigns.length;
    const activeRelations = customerCampaigns.filter(
      (cc) => cc.isActive
    ).length;
    const calledRelations = customerCampaigns.filter(
      (cc) => cc.isCalled
    ).length;
    const uniqueCustomers = [
      ...new Set(customerCampaigns.map((cc) => cc.customer.id)),
    ].length;
    const uniqueCampaigns = [
      ...new Set(customerCampaigns.map((cc) => cc.campaign.id)),
    ].length;
    const uniqueCities = [
      ...new Set(
        customerCampaigns
          .filter((cc) => cc.customer.city)
          .map((cc) => cc.customer.city)
      ),
    ].length;

    return {
      totalRelations,
      activeRelations,
      calledRelations,
      uniqueCustomers,
      uniqueCampaigns,
      uniqueCities,
    };
  }, [customerCampaigns]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await fetchData();
      setLastUpdated(new Date());
    } finally {
      setIsRefreshing(false);
    }
  };

  const customerCampaignColumns: GridColDef[] = [
    {
      field: "id",
      headerName: "ID",
      width: 60,
    },
    {
      field: "customerInfo",
      headerName: "Müşteri",
      width: 140,
      renderCell: (params: GridRenderCellParams) => {
        const maskedCustomer = conditionalMask(
          params.row.customer,
          maskCustomerData
        );
        return (
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <PersonIcon fontSize="small" color="primary" />
            <Typography
              variant="body2"
              sx={{ fontSize: "0.875rem", fontWeight: 500 }}
            >
              {`${maskedCustomer.name} ${maskedCustomer.surname}`}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: "username",
      headerName: "Kullanıcı Adı",
      width: 120,
      renderCell: (params: GridRenderCellParams) => {
        const maskedCustomer = conditionalMask(
          params.row.customer,
          maskCustomerData
        );
        return (
          <Typography variant="caption" color="text.secondary">
            {maskedCustomer.username}
          </Typography>
        );
      },
    },
    {
      field: "customerContact",
      headerName: "İletişim",
      width: 180,
      renderCell: (params: GridRenderCellParams) => {
        const maskedCustomer = conditionalMask(
          params.row.customer,
          maskCustomerData
        );
        return (
          <Box sx={{ display: "flex", flexDirection: "column", gap: 0.5 }}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
              <EmailIcon fontSize="small" color="primary" />
              <Typography variant="caption" sx={{ fontSize: "0.75rem" }}>
                {maskedCustomer.email}
              </Typography>
            </Box>
            <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
              <PhoneIcon fontSize="small" color="primary" />
              <Typography variant="caption" sx={{ fontSize: "0.75rem" }}>
                {maskedCustomer.phoneNumber}
              </Typography>
            </Box>
          </Box>
        );
      },
    },
    // Kampanyaya özel iletişim bilgileri
    {
      field: "campaignContact",
      headerName: "Kampanya İletişim",
      width: 200,
      renderCell: (params: GridRenderCellParams) => {
        const hasCustomContact =
          params.row.contactEmail || params.row.contactPhone;

        return hasCustomContact ? (
          <Box sx={{ display: "flex", flexDirection: "column", gap: 0.5 }}>
            {params.row.contactEmail && (
              <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                <EmailIcon fontSize="small" color="secondary" />
                <Typography variant="caption" sx={{ fontSize: "0.75rem" }}>
                  {params.row.contactEmail}
                </Typography>
              </Box>
            )}
            {params.row.contactPhone && (
              <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                <PhoneIcon fontSize="small" color="secondary" />
                <Typography variant="caption" sx={{ fontSize: "0.75rem" }}>
                  {params.row.contactPhone}
                </Typography>
              </Box>
            )}
          </Box>
        ) : (
          <Typography variant="caption" color="text.secondary">
            Kampanyaya özel iletişim yok
          </Typography>
        );
      },
    },
    {
      field: "customerLocation",
      headerName: "Konum",
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
          <LocationIcon fontSize="small" color="primary" />
          <Typography variant="caption" sx={{ fontSize: "0.75rem" }}>
            {[params.row.customer.city, params.row.customer.town]
              .filter(Boolean)
              .join(", ")}
          </Typography>
        </Box>
      ),
    },
    {
      field: "job",
      headerName: "Meslek",
      width: 140,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" color="text.secondary">
          {params.row.customer.job?.description || "Belirtilmemiş"}
        </Typography>
      ),
    },
    {
      field: "lastLogin",
      headerName: "Son Giriş",
      width: 140,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" color="text.secondary">
          {params.row.customer.lastLogin
            ? formatDateWithTime(params.row.customer.lastLogin)
            : "Yok"}
        </Typography>
      ),
    },
    {
      field: "campaignInfo",
      headerName: "Kampanya",
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: "flex", flexDirection: "column", gap: 0.5 }}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
            <CampaignIcon fontSize="small" color="primary" />
            <Typography
              variant="body2"
              sx={{ fontSize: "0.875rem", fontWeight: 500 }}
            >
              {params.row.campaign.name}
            </Typography>
          </Box>
          {params.row.campaign.category?.name && (
            <Chip
              label={params.row.campaign.category.name}
              size="small"
              color="secondary"
              variant="outlined"
              sx={{
                fontSize: "0.7rem",
                height: "20px",
                alignSelf: "flex-start",
              }}
            />
          )}
        </Box>
      ),
    },
    {
      field: "relationDate",
      headerName: "İlişki Tarihi",
      width: 140,
      renderCell: (params: GridRenderCellParams) => {
        const formattedDate = formatDateWithTime(params.row.createdAt);
        if (formattedDate === "-") {
          return (
            <Typography variant="caption" color="error">
              Geçersiz Tarih
            </Typography>
          );
        }

        const [datePart, timePart] = formattedDate.split(" ");
        return (
          <Box sx={{ display: "flex", flexDirection: "column" }}>
            <Typography
              variant="body2"
              sx={{ fontSize: "0.875rem", fontWeight: 500 }}
            >
              {datePart}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {timePart}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: "isCalled",
      headerName: "Arama Durumu",
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value ? "Arandı" : "Aranmadı"}
          color={params.value ? "success" : "warning"}
          size="small"
          sx={{ fontSize: "0.75rem" }}
        />
      ),
    },
    {
      field: "isActive",
      headerName: "Durum",
      width: 90,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value ? "Aktif" : "Pasif"}
          color={params.value ? "success" : "default"}
          size="small"
          sx={{ fontSize: "0.75rem" }}
        />
      ),
    },
    {
      field: "actions",
      headerName: "İşlemler",
      width: 100,
      sortable: false,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <Tooltip title="Detayları Görüntüle">
            <IconButton
              size="small"
              onClick={() => handleViewCustomerCampaign(params.row)}
              color="primary"
            >
              <ViewIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      ),
    },
  ];

  return (
    <AdminLayout>
      <Box sx={{ p: 3 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 3,
          }}
        >
          <Typography variant="h4" sx={{ fontWeight: 600 }}>
            Müşteri-Kampanya İlişkileri Yönetimi
          </Typography>
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <CustomerCampaignExcelExport
              data={filteredCustomerCampaigns}
              disabled={isLoading || customerCampaigns.length === 0}
              includeDetailedInfo={true}
            />
            <Typography variant="body2" color="text.secondary">
              Son güncelleme: {formatDateWithTime(lastUpdated.toISOString())}
            </Typography>
            <Tooltip title="Yenile">
              <IconButton onClick={handleRefresh} disabled={isRefreshing}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {isLoading && <LinearProgress sx={{ mb: 3 }} />}

        {/* İstatistik Kartları */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card
              sx={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                color: "white",
              }}
            >
              <CardContent>
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <PersonIcon sx={{ fontSize: 40, color: "white" }} />
                  <Box sx={{ flex: 1 }}>
                    <Typography
                      variant="h4"
                      sx={{ fontWeight: 600, color: "white" }}
                    >
                      {statistics.totalRelations}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255,255,255,0.8)" }}
                    >
                      Toplam İlişki
                    </Typography>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                    <TrendingUpIcon sx={{ color: "rgba(255,255,255,0.8)" }} />
                    <Typography
                      variant="caption"
                      sx={{ color: "rgba(255,255,255,0.8)" }}
                    >
                      +8%
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card
              sx={{
                background: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
                color: "white",
              }}
            >
              <CardContent>
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <CampaignIcon sx={{ fontSize: 40, color: "white" }} />
                  <Box>
                    <Typography
                      variant="h4"
                      sx={{ fontWeight: 600, color: "white" }}
                    >
                      {statistics.uniqueCustomers}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255,255,255,0.8)" }}
                    >
                      Benzersiz Müşteri
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card
              sx={{
                background: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
                color: "white",
              }}
            >
              <CardContent>
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <PhoneIcon sx={{ fontSize: 40, color: "white" }} />
                  <Box>
                    <Typography
                      variant="h4"
                      sx={{ fontWeight: 600, color: "white" }}
                    >
                      {statistics.calledRelations}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255,255,255,0.8)" }}
                    >
                      Aranan İlişki
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card
              sx={{
                background: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
                color: "white",
              }}
            >
              <CardContent>
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <LocationIcon sx={{ fontSize: 40, color: "white" }} />
                  <Box>
                    <Typography
                      variant="h4"
                      sx={{ fontWeight: 600, color: "white" }}
                    >
                      {statistics.uniqueCities}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255,255,255,0.8)" }}
                    >
                      Farklı Şehir
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <AdminFilterBar
          filterData={filterData}
          setFilterData={setFilterData}
          onFilter={() => {}}
          filteredCount={filteredCustomerCampaigns.length}
          totalCount={customerCampaigns.length}
          showActiveFilter={true}
          showDateFilter={true}
          searchPlaceholder="Müşteri Adı, E-posta, Telefon veya Kampanya Adı ile ara..."
          additionalFilters={[
            {
              field: "customerId",
              label: "Müşteri",
              type: "select",
              options: [
                { value: "", label: "Tüm Müşteriler" },
                ...customers.map((customer) => ({
                  value: customer.id.toString(),
                  label: `${customer.name} ${customer.surname}`,
                })),
              ],
            },
            {
              field: "campaignId",
              label: "Kampanya",
              type: "select",
              options: [
                { value: "", label: "Tüm Kampanyalar" },
                ...campaigns.map((campaign) => ({
                  value: campaign.id.toString(),
                  label: campaign.name,
                })),
              ],
            },
          ]}
        />

        <Paper sx={{ mt: 2 }}>
          <DataGrid
            rows={filteredCustomerCampaigns}
            columns={customerCampaignColumns}
            loading={isLoading}
            pageSizeOptions={[10, 25, 50, 100]}
            initialState={{
              pagination: { paginationModel: { pageSize: 25 } },
              sorting: {
                sortModel: [{ field: "createdAt", sort: "desc" }],
              },
            }}
            disableRowSelectionOnClick
            sx={{
              "& .MuiDataGrid-cell": {
                borderBottom: "1px solid #f0f0f0",
              },
              "& .MuiDataGrid-row:hover": {
                backgroundColor: "rgba(0, 0, 0, 0.04)",
              },
              "& .MuiDataGrid-columnHeaders": {
                position: "sticky",
                top: 0,
                backgroundColor: "background.paper",
                zIndex: 1,
                borderBottom: "2px solid #e0e0e0",
              },
              minHeight: 400,
            }}
          />
        </Paper>

        {/* Müşteri-Kampanya Detay Dialog */}
        <Dialog
          open={viewDialogOpen}
          onClose={() => setViewDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <Typography variant="h6">
                Müşteri-Kampanya İlişki Detayları
              </Typography>
              <IconButton onClick={() => setViewDialogOpen(false)} size="small">
                <CloseIcon />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent>
            {selectedCustomerCampaign &&
              (() => {
                const maskedCustomer = conditionalMask(
                  selectedCustomerCampaign.customer,
                  maskCustomerData
                );
                return (
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Card>
                        <CardContent>
                          <Typography variant="h6" gutterBottom color="primary">
                            Müşteri Bilgileri
                          </Typography>
                          <Box
                            sx={{
                              display: "flex",
                              flexDirection: "column",
                              gap: 1,
                            }}
                          >
                            <Typography>
                              <strong>Ad Soyad:</strong> {maskedCustomer.name}{" "}
                              {maskedCustomer.surname}
                            </Typography>
                            <Typography>
                              <strong>Kullanıcı Adı:</strong>{" "}
                              {maskedCustomer.username}
                            </Typography>
                            <Typography>
                              <strong>E-posta:</strong> {maskedCustomer.email}
                            </Typography>
                            <Typography>
                              <strong>Telefon:</strong>{" "}
                              {maskedCustomer.phoneNumber}
                            </Typography>
                            <Typography>
                              <strong>Cinsiyet:</strong>{" "}
                              {selectedCustomerCampaign.customer.gender ||
                                "Belirtilmemiş"}
                            </Typography>
                            <Typography>
                              <strong>Doğum Tarihi:</strong>{" "}
                              {selectedCustomerCampaign.customer.birthday ||
                                "Belirtilmemiş"}
                            </Typography>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <Card>
                        <CardContent>
                          <Typography variant="h6" gutterBottom color="primary">
                            Konum Bilgileri
                          </Typography>
                          <Box
                            sx={{
                              display: "flex",
                              flexDirection: "column",
                              gap: 1,
                            }}
                          >
                            <Typography>
                              <strong>Ülke:</strong>{" "}
                              {selectedCustomerCampaign.customer.country ||
                                "Belirtilmemiş"}
                            </Typography>
                            <Typography>
                              <strong>Şehir:</strong>{" "}
                              {selectedCustomerCampaign.customer.city ||
                                "Belirtilmemiş"}
                            </Typography>
                            <Typography>
                              <strong>İlçe:</strong>{" "}
                              {selectedCustomerCampaign.customer.town ||
                                "Belirtilmemiş"}
                            </Typography>
                            <Typography>
                              <strong>Müşteri Durumu:</strong>
                              <Chip
                                label={
                                  selectedCustomerCampaign.customer.isActive
                                    ? "Aktif"
                                    : "Pasif"
                                }
                                color={
                                  selectedCustomerCampaign.customer.isActive
                                    ? "success"
                                    : "default"
                                }
                                size="small"
                                sx={{ ml: 1 }}
                              />
                            </Typography>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>

                    <Grid item xs={12}>
                      <Card>
                        <CardContent>
                          <Typography variant="h6" gutterBottom color="primary">
                            Kampanya Bilgileri
                          </Typography>
                          <Box
                            sx={{
                              display: "flex",
                              flexDirection: "column",
                              gap: 1,
                            }}
                          >
                            <Typography>
                              <strong>Kampanya Adı:</strong>{" "}
                              {selectedCustomerCampaign.campaign.name}
                            </Typography>
                            <Typography>
                              <strong>Başlık:</strong>{" "}
                              {selectedCustomerCampaign.campaign.title}
                            </Typography>
                            <Typography>
                              <strong>Açıklama:</strong>{" "}
                              {selectedCustomerCampaign.campaign.description ||
                                "Açıklama yok"}
                            </Typography>
                            {selectedCustomerCampaign.campaign.title2 && (
                              <Typography>
                                <strong>Başlık 2:</strong>{" "}
                                {selectedCustomerCampaign.campaign.title2}
                              </Typography>
                            )}
                            {selectedCustomerCampaign.campaign.description2 && (
                              <Typography>
                                <strong>Açıklama 2:</strong>{" "}
                                {selectedCustomerCampaign.campaign.description2}
                              </Typography>
                            )}
                            {selectedCustomerCampaign.campaign.title3 && (
                              <Typography>
                                <strong>Başlık 3:</strong>{" "}
                                {selectedCustomerCampaign.campaign.title3}
                              </Typography>
                            )}
                            {selectedCustomerCampaign.campaign.description3 && (
                              <Typography>
                                <strong>Açıklama 3:</strong>{" "}
                                {selectedCustomerCampaign.campaign.description3}
                              </Typography>
                            )}
                            <Typography>
                              <strong>Kategori:</strong>{" "}
                              {selectedCustomerCampaign.campaign.category
                                ?.name || "Kategori yok"}
                            </Typography>
                            <Typography>
                              <strong>Kampanya Durumu:</strong>
                              <Chip
                                label={
                                  selectedCustomerCampaign.campaign.isActive
                                    ? "Aktif"
                                    : "Pasif"
                                }
                                color={
                                  selectedCustomerCampaign.campaign.isActive
                                    ? "success"
                                    : "default"
                                }
                                size="small"
                                sx={{ ml: 1 }}
                              />
                            </Typography>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>

                    <Grid item xs={12}>
                      <Card>
                        <CardContent>
                          <Typography variant="h6" gutterBottom color="primary">
                            İlişki Bilgileri
                          </Typography>
                          <Box
                            sx={{
                              display: "flex",
                              flexDirection: "column",
                              gap: 1,
                            }}
                          >
                            <Typography>
                              <strong>İlişki Durumu:</strong>
                              <Chip
                                label={
                                  selectedCustomerCampaign.isActive
                                    ? "Aktif"
                                    : "Pasif"
                                }
                                color={
                                  selectedCustomerCampaign.isActive
                                    ? "success"
                                    : "default"
                                }
                                size="small"
                                sx={{ ml: 1 }}
                              />
                            </Typography>
                            <Typography>
                              <strong>Arama Durumu:</strong>
                              <Chip
                                label={
                                  selectedCustomerCampaign.isCalled
                                    ? "Arandı"
                                    : "Aranmadı"
                                }
                                color={
                                  selectedCustomerCampaign.isCalled
                                    ? "success"
                                    : "warning"
                                }
                                size="small"
                                sx={{ ml: 1 }}
                              />
                            </Typography>
                            <Typography>
                              <strong>İlişki Kurulma:</strong>{" "}
                              {formatDateWithTime(
                                selectedCustomerCampaign.createdAt
                              )}
                            </Typography>
                            <Typography>
                              <strong>Son Güncelleme:</strong>{" "}
                              {selectedCustomerCampaign.updatedAt
                                ? formatDateWithTime(
                                    selectedCustomerCampaign.updatedAt
                                  )
                                : "Güncellenmemiş"}
                            </Typography>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  </Grid>
                );
              })()}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setViewDialogOpen(false)}>Kapat</Button>
          </DialogActions>
        </Dialog>
      </Box>
    </AdminLayout>
  );
};

export default CustomerCampaignsManagementPage;
