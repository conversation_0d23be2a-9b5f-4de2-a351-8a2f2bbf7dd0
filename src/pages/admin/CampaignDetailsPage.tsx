import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from "../../contexts/AuthContext";
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  CircularProgress,
  Switch,
  Card,
  CardContent,
  FormControlLabel,
  RadioGroup,
  Radio,
  FormGroup,
  Checkbox,
} from '@mui/material';
import { Edit as EditIcon, Add as AddIcon, Delete as DeleteIcon } from '@mui/icons-material';
import AdminLayout from '../../components/admin/AdminLayout';

interface CampaignDetail {
  id: number;
  details: {
    [key: string]: {
      type: 'text' | 'radio' | 'checkbox';
      label: string;
      options?: string[];
      required?: boolean;
    };
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface FormField {
  type: 'text' | 'radio' | 'checkbox';
  label: string;
  options?: string[];
  required?: boolean;
  value: string | string[];
};

type Order = 'asc' | 'desc';

export default function CampaignDetailsPage() {
  const { token } = useAuth();
  const [campaignDetails, setCampaignDetails] = useState<CampaignDetail[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedDetail, setSelectedDetail] = useState<CampaignDetail | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<{
    details: { [key: string]: FormField };
    isActive: boolean;
  }>({
    details: {},
    isActive: true,
  });


  const [errorDialog, setErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [formError, setFormError] = useState('');

  useEffect(() => {
    fetchCampaignDetails();
  }, []);

  const fetchCampaignDetails = async () => {
    try {
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/campaign-detail');
      const newData = response.data;
      
      // ID'ye göre artan şekilde sırala
      const sortedData = newData.sort((a: CampaignDetail, b: CampaignDetail) => a.id - b.id);
      setCampaignDetails(sortedData);
    } catch {
      // Hata durumunda sessizce devam et
    }
  };

  const handleOpenDialog = (detail?: CampaignDetail) => {
    if (detail) {
      setSelectedDetail(detail);
      const transformedDetails = Object.fromEntries(
        Object.entries(detail.details).map(([key, field]) => [
          key,
          {
            ...field,
            value: field.type === 'checkbox' ? [] : '', // Add default value
          } as FormField,
        ])
      );
      setFormData({
        details: transformedDetails,
        isActive: detail.isActive,
      });
    } else {
      setSelectedDetail(null);
      setFormData({
        details: {},
        isActive: true,
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedDetail(null);
    setFormData({
      details: {},
      isActive: true,
    });
  };

  const handleAddField = () => {
    const newField = {
      type: 'text' as const,
      label: '',
      required: false,
      value: '',
    };
    setFormData({
      ...formData,
      details: {
        ...formData.details,
        [`field_${Object.keys(formData.details).length + 1}`]: newField,
      },
    });
  };

  const handleFieldChange = (fieldKey: string, field: keyof FormField, value: string | boolean | string[]) => {
    const currentField = formData.details[fieldKey];
    
    // Eğer tip değişiyorsa, değeri sıfırla
    if (field === 'type') {
      setFormData({
        ...formData,
        details: {
          ...formData.details,
          [fieldKey]: {
            ...currentField,
            type: value as 'text' | 'radio' | 'checkbox',
            value: value === 'checkbox' ? [] : '',
            options: value === 'text' ? undefined : currentField.options || [],
          },
        },
      });
      return;
    }

    setFormData({
      ...formData,
      details: {
        ...formData.details,
        [fieldKey]: {
          ...currentField,
          [field]: value,
        },
      },
    });
  };

  const handleAddOption = (fieldKey: string) => {
    const currentField = formData.details[fieldKey];
    setFormData({
      ...formData,
      details: {
        ...formData.details,
        [fieldKey]: {
          ...currentField,
          options: [...(currentField.options || []), ''],
        },
      },
    });
  };

  const handleRemoveOption = (fieldKey: string, index: number) => {
    const currentField = formData.details[fieldKey];
    const newOptions = currentField.options?.filter((_: string, i: number) => i !== index) || [];
    setFormData({
      ...formData,
      details: {
        ...formData.details,
        [fieldKey]: {
          ...currentField,
          options: newOptions,
        },
      },
    });
  };

  const handleOptionChange = (fieldKey: string, index: number, value: string) => {
    const currentField = formData.details[fieldKey];
    const newOptions = [...(currentField.options || [])];
    newOptions[index] = value;
    setFormData({
      ...formData,
      details: {
        ...formData.details,
        [fieldKey]: {
          ...currentField,
          options: newOptions,
        },
      },
    });
  };

  const handleRemoveField = (fieldKey: string) => {
    const newDetails = { ...formData.details };
    delete newDetails[fieldKey];
    setFormData({
      ...formData,
      details: newDetails,
    });
  };

  const validateForm = () => {
    if (!formData.details || Object.keys(formData.details).length === 0) {
      setFormError('Kampanya detayları boş olamaz.');
      return false;
    }
    setFormError('');
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
    try {
      setIsLoading(true);
      if (selectedDetail) {
        await axios.put(`https://360avantajli.com/api/Campaign_Service/campaign-detail/${selectedDetail.id}`, {
          details: formData.details,
          isActive: formData.isActive,
        });
      } else {
        await axios.post('https://360avantajli.com/api/Campaign_Service/campaign-detail', {
          details: formData.details,
          isActive: formData.isActive,
        });
      }
      await fetchCampaignDetails();
      handleCloseDialog();
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      setIsLoading(true);
      await axios.delete(`https://360avantajli.com/api/Campaign_Service/campaign-detail/${id}`);
      await fetchCampaignDetails();
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleActive = async (id: number) => {
    try {
      setIsLoading(true);
      const detail = campaignDetails.find(d => d.id === id);
      if (detail) {
        await axios.put(`https://360avantajli.com/api/Campaign_Service/campaign-detail/${id}`, {
          details: detail.details,
          isActive: !detail.isActive
        });
        await fetchCampaignDetails();
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const renderFieldInput = (fieldKey: string, field: FormField) => {
    switch (field.type) {
      case 'radio':
        return (
          <Box key={fieldKey} sx={{ mb: 2 }}>
            <Typography variant="subtitle1">{field.label}</Typography>
            <RadioGroup
              value={field.value || ''}
              onChange={(e) => handleFieldChange(fieldKey, 'value', e.target.value)}
            >
              {field.options?.map((option: string) => (
                <FormControlLabel
                  key={option}
                  value={option}
                  control={<Radio />}
                  label={option}
                />
              ))}
            </RadioGroup>
          </Box>
        );
      case 'checkbox':
        return (
          <Box key={fieldKey} sx={{ mb: 2 }}>
            <Typography variant="subtitle1">{field.label}</Typography>
            <FormGroup>
              {field.options?.map((option: string) => {
                const isChecked = Array.isArray(field.value) && field.value.includes(option);
                return (
                  <FormControlLabel
                    key={option}
                    control={
                      <Checkbox
                        checked={isChecked}
                        onChange={(e) => {
                          const currentValue = Array.isArray(field.value) ? field.value : [];
                          const newValue = e.target.checked
                            ? [...currentValue, option]
                            : currentValue.filter((v: string) => v !== option);
                          handleFieldChange(fieldKey, 'value', newValue);
                        }}
                      />
                    }
                    label={option}
                  />
                );
              })}
            </FormGroup>
          </Box>
        );
      default:
        return (
          <TextField
            key={fieldKey}
            fullWidth
            label={field.label}
            value={field.value || ''}
            onChange={(e) => handleFieldChange(fieldKey, 'value', e.target.value)}
            sx={{ mb: 2 }}
            required={field.required}
          />
        );
    }
  };

  return (
    <AdminLayout>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5" component="h1">
          Kampanya Detay Şablonları
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          disabled={isLoading}
        >
          Yeni Şablon Ekle
        </Button>
      </Box>

      {campaignDetails.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6" color="text.secondary">
            Henüz kampanya detay şablonu bulunmamaktadır.
          </Typography>
        </Paper>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Detaylar</TableCell>
                <TableCell>Durum</TableCell>
                <TableCell>İşlemler</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {campaignDetails.map((detail) => (
                <TableRow key={detail.id}>
                  <TableCell>{detail.id}</TableCell>
                  <TableCell>
                    {Object.entries(detail.details).map(([key, value]) => (
                      <Typography key={key} variant="body2">
                        {value.label}: {value.type}
                      </Typography>
                    ))}
                  </TableCell>
                  <TableCell>
                    <Switch
                      checked={detail.isActive}
                      onChange={() => handleToggleActive(detail.id)}
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton onClick={() => handleOpenDialog(detail)}>
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDelete(detail.id)}>
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      <Dialog 
        open={openDialog} 
        onClose={handleCloseDialog} 
        maxWidth="md" 
        fullWidth
        disableEnforceFocus
        disableAutoFocus
      >
        <DialogTitle>
          {selectedDetail ? 'Şablon Düzenle' : 'Yeni Şablon Ekle'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            {formError && (
              <Typography color="error" sx={{ mb: 2 }}>{formError}</Typography>
            )}
            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, isActive: e.target.checked })}
                />
              }
              label="Aktif"
              sx={{ mb: 2 }}
            />
            {Object.entries(formData.details).map(([key, field]) => (
              <Box key={key} sx={{ mb: 2, p: 2, border: '1px solid #ccc', borderRadius: 1 }}>
                <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                  <TextField
                    label="Alan Adı"
                    value={field.label}
                    onChange={(e) => handleFieldChange(key, 'label', e.target.value)}
                    fullWidth
                  />
                  <FormControl fullWidth>
                    <InputLabel>Tip</InputLabel>
                    <Select
                      value={field.type}
                      label="Tip"
                      onChange={(e) => handleFieldChange(key, 'type', e.target.value)}
                    >
                      <MenuItem value="text">Metin</MenuItem>
                      <MenuItem value="radio">Radio Buton</MenuItem>
                      <MenuItem value="checkbox">Çoklu Seçim</MenuItem>
                    </Select>
                  </FormControl>
                  <IconButton
                    color="error"
                    onClick={() => handleRemoveField(key)}
                    sx={{ mt: 1 }}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>

                {(field.type === 'radio' || field.type === 'checkbox') && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>Seçenekler</Typography>
                    {field.options?.map((option: string, index: number) => (
                      <Box key={index} sx={{ display: 'flex', gap: 1, mb: 1 }}>
                        <TextField
                          value={option}
                          onChange={(e) => handleOptionChange(key, index, e.target.value)}
                          fullWidth
                        />
                        <FormControlLabel
                          key={option}
                          control={
                            <Checkbox
                              checked={(field.value as string[]).includes(option)}
                              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                const newValue = [...(field.value as string[])];
                                if (e.target.checked) {
                                  newValue.push(option);
                                } else {
                                  const optionIndex = newValue.indexOf(option);
                                  if (optionIndex > -1) {
                                    newValue.splice(optionIndex, 1);
                                  }
                                }
                                handleFieldChange(key, 'value', newValue);
                              }}
                            />
                          }
                          label={option}
                        />
                        <IconButton
                          color="error"
                          onClick={() => handleRemoveOption(key, index)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    ))}
                    <Button
                      variant="outlined"
                      onClick={() => handleAddOption(key)}
                      startIcon={<AddIcon />}
                    >
                      Seçenek Ekle
                    </Button>
                  </Box>
                )}

                <FormControlLabel
                  control={
                    <Checkbox
                      checked={field.required || false}
                      onChange={(e) => handleFieldChange(key, 'required', e.target.checked)}
                    />
                  }
                  label="Zorunlu Alan"
                />
              </Box>
            ))}

            <Button
              variant="outlined"
              onClick={handleAddField}
              startIcon={<AddIcon />}
              sx={{ mt: 2 }}
            >
              Yeni Alan Ekle
            </Button>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} disabled={isLoading}>İptal</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={isLoading}
            startIcon={isLoading ? <CircularProgress size={20} /> : null}
          >
            {selectedDetail ? 'Güncelle' : 'Ekle'}
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={errorDialog}
        onClose={() => setErrorDialog(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            background: 'transparent',
            boxShadow: 'none',
          }
        }}
      >
        <Box
          sx={{
            bgcolor: '#fff',
            borderRadius: 3,
            boxShadow: 6,
            p: 0,
            position: 'relative',
          }}
        >
          <Card
            sx={{
              bgcolor: '#ffeaea',
              border: '1px solid #ffb4b4',
              borderRadius: 3,
              boxShadow: 3,
              mx: 3,
              mt: 3,
              mb: 0,
              p: 0,
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ color: '#d32f2f', fontWeight: 700, mb: 1 }}>
                Hata
              </Typography>
              <Typography variant="body1" sx={{ color: '#b71c1c', fontSize: 17, fontWeight: 500 }}>
                {errorMessage}
              </Typography>
            </CardContent>
          </Card>
          <DialogActions sx={{ justifyContent: 'flex-end', px: 3, pb: 2, pt: 1 }}>
            <Button
              onClick={() => setErrorDialog(false)}
              variant="contained"
              sx={{
                bgcolor: '#d32f2f',
                color: '#fff',
                fontWeight: 600,
                borderRadius: 2,
                px: 4,
                py: 1.2,
                boxShadow: 2,
                '&:hover': { bgcolor: '#b71c1c' }
              }}
            >
              Tamam
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </AdminLayout>
  );
} 