import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Box,
  Typography,
  InputAdornment,
  IconButton,
  Tooltip
} from '@mui/material';
import { Visibility, VisibilityOff, Info as InfoIcon } from '@mui/icons-material';
import axios from 'axios';

interface MindhuntersLoginModalProps {
  open: boolean;
  onClose: () => void;
  onLoginSuccess: () => void;
}

const MindhuntersLoginModal: React.FC<MindhuntersLoginModalProps> = ({ open, onClose, onLoginSuccess }) => {
  const [credentials, setCredentials] = useState({
    email: '',
    password: '',
    subdomain: 'crm.360avantajli.com'
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [tokenStatus, setTokenStatus] = useState<'valid' | 'invalid' | 'checking' | 'none'>('none');

  useEffect(() => {
    if (open) {
      // Load saved subdomain if exists
      const savedSubdomain = localStorage.getItem('mindhuntersSubdomain');
      if (savedSubdomain) {
        setCredentials(prev => ({ ...prev, subdomain: savedSubdomain }));
      }
      checkExistingToken();
    }
  }, [open]);

  const checkExistingToken = async () => {
    const token = localStorage.getItem('mindhuntersToken');
    const subdomain = localStorage.getItem('mindhuntersSubdomain') || credentials.subdomain;
    
    if (token) {
      setTokenStatus('checking');
      try {
        // Try to make a simple API call to verify the token
        await axios.get(`https://${subdomain}.mindhunters.ai/api/v1/calls`, {
          headers: {
            Authorization: `Bearer ${token}`
          },
          params: {
            page: 1,
            per_page: 1
          }
        });
        setTokenStatus('valid');
      } catch (error) {
        console.error('Token validation error:', error);
        setTokenStatus('invalid');
        localStorage.removeItem('mindhuntersToken');
      }
    } else {
      setTokenStatus('none');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCredentials(prev => ({ ...prev, [name]: value }));
  };

  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };

  const handleLogin = async () => {
    if (!credentials.email || !credentials.password || !credentials.subdomain) {
      setError('Tüm alanları doldurun.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Use the subdomain in the URL
      const response = await axios.post(`https://${credentials.subdomain}.mindhunters.ai/api/v1/auth/login`, {
        email: credentials.email,
        password: credentials.password
      });

      if (response.data?.token) {
        localStorage.setItem('mindhuntersToken', response.data.token);
        localStorage.setItem('mindhuntersSubdomain', credentials.subdomain);
        onLoginSuccess();
        setTokenStatus('valid');
      } else {
        setError('Beklenmeyen API yanıtı.');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      setError(
        error.response?.data?.message || 
        'Giriş yapılamadı. Lütfen bilgilerinizi kontrol edin ve tekrar deneyin.'
      );
      setTokenStatus('invalid');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('mindhuntersToken');
    localStorage.removeItem('mindhuntersSubdomain');
    setTokenStatus('none');
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Mindhunters API Bağlantısı</DialogTitle>
      <DialogContent>
        {tokenStatus === 'checking' ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
            <CircularProgress />
          </Box>
        ) : tokenStatus === 'valid' ? (
          <Box sx={{ my: 2 }}>
            <Alert severity="success" sx={{ mb: 2 }}>
              Mindhunters API'ye başarıyla bağlandınız.
            </Alert>
            <Typography variant="body2" color="text.secondary">
              API URL: https://{localStorage.getItem('mindhuntersSubdomain') || credentials.subdomain}.mindhunters.ai/api/v1
            </Typography>
          </Box>
        ) : (
          <Box sx={{ my: 2 }}>
            {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
            <TextField
              fullWidth
              margin="normal"
              label="Subdomain"
              name="subdomain"
              value={credentials.subdomain}
              onChange={handleInputChange}
              placeholder="crm.360avantajli.com"
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <Tooltip title={`Bu alan API URL'ini oluşturmak için kullanılır. 
                    https://{subdomain}.mindhunters.ai/api/v1 formatında olmalıdır.`}>
                      <InfoIcon color="info" fontSize="small" />
                    </Tooltip>
                  </InputAdornment>
                ),
              }}
              helperText={`Oluşturulacak URL: https://${credentials.subdomain}.mindhunters.ai/api/v1`}
            />
            <TextField
              fullWidth
              margin="normal"
              label="E-posta"
              name="email"
              type="email"
              value={credentials.email}
              onChange={handleInputChange}
            />
            <TextField
              fullWidth
              margin="normal"
              label="Şifre"
              name="password"
              type={showPassword ? 'text' : 'password'}
              value={credentials.password}
              onChange={handleInputChange}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleTogglePassword}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Kapat</Button>
        {tokenStatus === 'valid' ? (
          <Button onClick={handleLogout} color="error">
            Bağlantıyı Kes
          </Button>
        ) : (
          <Button 
            onClick={handleLogin} 
            color="primary" 
            variant="contained"
            disabled={loading || !credentials.email || !credentials.password || !credentials.subdomain}
          >
            {loading ? <CircularProgress size={24} /> : 'Giriş Yap'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default MindhuntersLoginModal; 