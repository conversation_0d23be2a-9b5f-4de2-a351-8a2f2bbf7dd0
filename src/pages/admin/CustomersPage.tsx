import React, { useState, useEffect } from "react";
import axios from "axios";
import api from '../../utils/api';
import { useAuth } from "../../contexts/AuthContext";
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Switch,
  FormControlLabel,
  Typography,
  CircularProgress,
  TableSortLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  useTheme,
  alpha,
} from "@mui/material";
import {
  Edit as EditIcon,
  Add as AddIcon
} from "@mui/icons-material";
import AdminLayout from "../../components/admin/AdminLayout";
import AdminFilterBar, {
  AdminFilterData,
} from "../../components/admin/AdminFilterBar";
import ExportButton from "../../components/admin/ExportButton";
import CityDistrictSelector from "../../components/common/CityDistrictSelector";
import {
  maskCustomersArray,
  conditionalMask,
} from "../../utils/dataMaskingUtils";

interface Customer {
  id: number;
  name: string;
  surname: string;
  email: string;
  phoneNumber: string;
  country: string;
  city: string;
  town: string;
  gender: string;
  jobId: number;
  remindMe: boolean;
  birthday: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  job: {
    id: number;
    description: string;
    active?: boolean;
  };
}

interface Job {
  id: number;
  description: string;
  isActive: boolean;
}

// Sorting type definition
type Order = "asc" | "desc";

interface HeadCell {
  id: keyof Customer | "";
  label: string;
  numeric: boolean;
  sortable: boolean;
}

export default function CustomersPage() {
  const { token } = useAuth();
  const theme = useTheme();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  const [jobs, setJobs] = useState<Job[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(false);
  const [errorDialog, setErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  const [filterData, setFilterData] = useState<AdminFilterData>({
    searchText: "",
    isActive: "all",
    jobId: "",
    gender: "",
    remindMe: "all",
  });

  const [formData, setFormData] = useState({
    name: "",
    surname: "",
    email: "",
    phoneNumber: "",
    country: "",
    city: "",
    town: "",
    gender: "",
    jobId: 0,
    remindMe: true,
    birthday: "",
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  });

  // Form validasyon durumları
  const [formErrors, setFormErrors] = useState({
    name: "",
    surname: "",
    email: "",
    phoneNumber: "",
    country: "",
    city: "",
    town: "",
    gender: "",
    jobId: "",
  });

  // Validasyon için regex pattern'ları
  const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;

  // Türkiye telefon formatı için daha sıkı kontrol (tam 11 hane)
  // 5XX XXX XX XX formatında olmalı
  const phonePattern = /^5\d{2}[0-9]{3}[0-9]{2}[0-9]{2}$/;

  // Telefon numarasını formatlama fonksiyonu
  const formatPhoneNumber = (value: string) => {
    // Sadece rakamları al
    const numbers = value.replace(/\D/g, "");

    // Eğer 11 haneden fazla ise, ilk 11 haneyi al
    const trimmed = numbers.substring(0, 11);

    // Eğer boşsa, boş dön
    if (trimmed.length === 0) return "";

    // 5 ile başlamasını sağla
    if (trimmed.length === 1 && trimmed[0] !== "5") return "5";

    return trimmed;
  };

  // Sorting states
  const [order, setOrder] = useState<Order>("asc");
  const [orderBy, setOrderBy] = useState<keyof Customer>("id");

  useEffect(() => {
    fetchCustomers();
    fetchJobs();
  }, []);

  // Filter customers logic
  useEffect(() => {
    let filtered = customers;

    // Filter by search text
    if (filterData.searchText && filterData.searchText.trim() !== "") {
      const lowercasedFilter = filterData.searchText.toLowerCase();
      filtered = filtered.filter(
        (customer) =>
          (customer.name &&
            customer.name.toLowerCase().includes(lowercasedFilter)) ||
          (customer.surname &&
            customer.surname.toLowerCase().includes(lowercasedFilter)) ||
          (customer.email &&
            customer.email.toLowerCase().includes(lowercasedFilter)) ||
          (customer.phoneNumber &&
            customer.phoneNumber.includes(lowercasedFilter))
      );
    }

    // Filter by active status
    if (filterData.isActive !== "all") {
      const isActiveValue = filterData.isActive === "active";
      filtered = filtered.filter(
        (customer) => customer.isActive === isActiveValue
      );
    }

    // Filter by Job
    if (filterData.jobId) {
      filtered = filtered.filter(
        (customer) => customer.job?.id.toString() === filterData.jobId
      );
    }

    // Filter by Gender
    if (filterData.gender) {
      filtered = filtered.filter(
        (customer) => customer.gender === filterData.gender
      );
    }

    // Filter by Remind Me status
    if (filterData.remindMe !== "all") {
      const remindMeValue = filterData.remindMe === "active";
      filtered = filtered.filter(
        (customer) => customer.remindMe === remindMeValue
      );
    }

    setFilteredCustomers(filtered);
  }, [filterData, customers]);

  const fetchCustomers = async () => {
    try {
      setIsLoading(true);
      const response = await api.get(
        "/api/Auth_Service/customer"
      );
      // API'den gelen yanıtın bir dizi olduğundan emin ol
      const customersData = Array.isArray(response.data) ? response.data : [];
      setCustomers(customersData);
      setFilteredCustomers(customersData);
    } catch (error: any) {
      setCustomers([]);
      setFilteredCustomers([]);
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage("Bilinmeyen bir hata oluştu.");
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchJobs = async () => {
    try {
      const response = await api.get(
        "/api/Campaign_Service/job"
      );
      // API'den gelen yanıtın bir dizi olduğundan emin ol
      const jobsData = Array.isArray(response.data) ? response.data : [];
      setJobs(jobsData);
    } catch (error: any) {
      setJobs([]);
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage("Bilinmeyen bir hata oluştu.");
      }
      setErrorDialog(true);
    }
  };

  // Sorting functions
  function descendingComparator<T>(a: T, b: T, orderBy: keyof T) {
    if (b[orderBy] < a[orderBy]) {
      return -1;
    }
    if (b[orderBy] > a[orderBy]) {
      return 1;
    }
    return 0;
  }

  function getComparator<Key extends keyof any>(
    order: Order,
    orderBy: Key
  ): (a: { [key in Key]: any }, b: { [key in Key]: any }) => number {
    return order === "desc"
      ? (a, b) => descendingComparator(a, b, orderBy)
      : (a, b) => -descendingComparator(a, b, orderBy);
  }

  function stableSort<T>(
    array: readonly T[],
    comparator: (a: T, b: T) => number
  ) {
    const stabilizedThis = array.map((el, index) => [el, index] as [T, number]);
    stabilizedThis.sort((a, b) => {
      const order = comparator(a[0], b[0]);
      if (order !== 0) {
        return order;
      }
      return a[1] - b[1];
    });
    return stabilizedThis.map((el) => el[0]);
  }

  const handleRequestSort = (property: keyof Customer) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
  };

  // Table header cells configuration
  const headCells: readonly HeadCell[] = [
    { id: "id", numeric: true, sortable: true, label: "ID" },
    { id: "name", numeric: false, sortable: true, label: "Ad" },
    { id: "surname", numeric: false, sortable: true, label: "Soyad" },
    { id: "email", numeric: false, sortable: true, label: "E-posta" },
    { id: "phoneNumber", numeric: false, sortable: true, label: "Telefon" },
    { id: "country", numeric: false, sortable: true, label: "Ülke" },
    { id: "city", numeric: false, sortable: true, label: "Şehir" },
    { id: "town", numeric: false, sortable: true, label: "İlçe" },
    { id: "gender", numeric: false, sortable: true, label: "Cinsiyet" },
    { id: "job", numeric: false, sortable: false, label: "Meslek" },
    { id: "remindMe", numeric: false, sortable: true, label: "Hatırlatma" },
    { id: "birthday", numeric: false, sortable: true, label: "Doğum Tarihi" },
    { id: "isActive", numeric: false, sortable: true, label: "Aktif" },
    {
      id: "createdAt",
      numeric: false,
      sortable: true,
      label: "Oluşturulma Tarihi",
    },
    {
      id: "updatedAt",
      numeric: false,
      sortable: true,
      label: "Güncellenme Tarihi",
    },
    { id: "", numeric: false, sortable: false, label: "İşlemler" },
  ];

  const handleOpenDialog = (customer?: Customer) => {
    // Form hatalarını sıfırla
    setFormErrors({
      name: "",
      surname: "",
      email: "",
      phoneNumber: "",
      country: "",
      city: "",
      town: "",
      gender: "",
      jobId: "",
    });

    if (customer) {
      setSelectedCustomer(customer);
      let birthday = customer.birthday || "";

      // Eğer tarih DD-MM-YYYY formatındaysa, YYYY-MM-DD formatına çevir
      if (birthday && birthday.includes("-")) {
        const [day, month, year] = birthday.split("-");
        if (day && month && year) {
          birthday = `${year}-${month}-${day}`;
        }
      }

      setFormData({
        name: customer.name,
        surname: customer.surname,
        email: customer.email,
        phoneNumber: customer.phoneNumber,
        country: customer.country,
        city: customer.city,
        town: customer.town,
        gender: customer.gender,
        jobId: customer.job?.id || 0,
        remindMe: customer.remindMe,
        birthday: birthday,
        isActive: customer.isActive,
        createdAt: customer.createdAt,
        updatedAt: customer.updatedAt,
      });
    } else {
      setSelectedCustomer(null);
      setFormData({
        name: "",
        surname: "",
        email: "",
        phoneNumber: "",
        country: "",
        city: "",
        town: "",
        gender: "",
        jobId: 0,
        remindMe: true,
        birthday: "",
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedCustomer(null);
    setFormData({
      name: "",
      surname: "",
      email: "",
      phoneNumber: "",
      country: "",
      city: "",
      town: "",
      gender: "",
      jobId: 0,
      remindMe: true,
      birthday: "",
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });
    // Form hatalarını sıfırla
    setFormErrors({
      name: "",
      surname: "",
      email: "",
      phoneNumber: "",
      country: "",
      city: "",
      town: "",
      gender: "",
      jobId: "",
    });
  };

  // Form alanlarını doğrula
  const validateForm = () => {
    let isValid = true;
    const errors = {
      name: "",
      surname: "",
      email: "",
      phoneNumber: "",
      country: "",
      city: "",
      town: "",
      gender: "",
      jobId: "",
    };

    if (!formData.name.trim()) {
      errors.name = "Ad alanı zorunludur";
      isValid = false;
    }

    if (!formData.surname.trim()) {
      errors.surname = "Soyad alanı zorunludur";
      isValid = false;
    }

    if (!formData.email.trim()) {
      errors.email = "E-posta alanı zorunludur";
      isValid = false;
    } else if (!emailPattern.test(formData.email.trim())) {
      errors.email = "Geçerli bir e-posta adresi giriniz";
      isValid = false;
    }

    if (!formData.phoneNumber.trim()) {
      errors.phoneNumber = "Telefon alanı zorunludur";
      isValid = false;
    } else if (!phonePattern.test(formData.phoneNumber.trim())) {
      errors.phoneNumber =
        "Telefon numarası 5 ile başlamalı ve 10 haneli olmalıdır";
      isValid = false;
    }

    if (!formData.country.trim()) {
      errors.country = "Ülke alanı zorunludur";
      isValid = false;
    }

    if (!formData.city.trim()) {
      errors.city = "Şehir alanı zorunludur";
      isValid = false;
    }

    if (!formData.town.trim()) {
      errors.town = "İlçe alanı zorunludur";
      isValid = false;
    }

    if (!formData.gender.trim()) {
      errors.gender = "Cinsiyet seçimi zorunludur";
      isValid = false;
    }

    if (!formData.jobId) {
      errors.jobId = "Meslek seçimi zorunludur";
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setIsLoading(true);
      const customerData = {
        name: formData.name.trim(),
        surname: formData.surname.trim(),
        email: formData.email.trim(),
        phoneNumber: formData.phoneNumber.trim(),
        country: formData.country.trim(),
        city: formData.city.trim(),
        town: formData.town.trim(),
        gender: formData.gender.trim(),
        jobId: formData.jobId,
        remindMe: formData.remindMe,
        birthday: formData.birthday.trim(),
        isActive: formData.isActive,
      };

      if (selectedCustomer) {
        await axios.put(
          `https://360avantajli.com/api/Auth_Service/customer/${selectedCustomer.id}`,
          customerData
        );
      } else {
        await axios.post(
          "https://360avantajli.com/api/Auth_Service/customer",
          customerData
        );
      }
      fetchCustomers();
      handleCloseDialog();
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage("Bilinmeyen bir hata oluştu.");
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm("Bu müşteriyi silmek istediğinizden emin misiniz?")) {
      try {
        await axios.delete(
          `https://360avantajli.com/api/Auth_Service/customer/${id}`
        );
        fetchCustomers();
      } catch (error: any) {
        if (error.response?.data?.message) {
          setErrorMessage(error.response.data.message);
        } else if (error.message) {
          setErrorMessage(error.message);
        } else {
          setErrorMessage("Bilinmeyen bir hata oluştu.");
        }
        setErrorDialog(true);
      }
    }
  };

  const handleToggleActive = async (id: number) => {
    try {
      const customer = customers.find((c) => c.id === id);
      if (customer) {
        // Birthday formatını değiştir
        let formattedBirthday = customer.birthday;
        if (customer.birthday && customer.birthday.includes("-")) {
          const [day, month, year] = customer.birthday.split("-");
          if (day && month && year) {
            formattedBirthday = `${year}-${month}-${day}`;
          }
        }

        const requestData = {
          name: customer.name,
          surname: customer.surname,
          email: customer.email,
          phoneNumber: customer.phoneNumber,
          country: customer.country,
          city: customer.city,
          town: customer.town,
          gender: customer.gender,
          jobId: customer.job.id,
          remindMe: customer.remindMe,
          birthday: formattedBirthday,
          isActive: !customer.isActive,
        };

        await axios.put(
          `https://360avantajli.com/api/Auth_Service/customer/${id}`,
          requestData
        );
        fetchCustomers();
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage("Bilinmeyen bir hata oluştu.");
      }
      setErrorDialog(true);
    }
  };

  const handleToggleRemindMe = async (id: number) => {
    try {
      const customer = customers.find((c) => c.id === id);
      if (customer) {
        // Birthday formatını değiştir
        let formattedBirthday = customer.birthday;
        if (customer.birthday && customer.birthday.includes("-")) {
          const [day, month, year] = customer.birthday.split("-");
          if (day && month && year) {
            formattedBirthday = `${year}-${month}-${day}`;
          }
        }

        const requestData = {
          name: customer.name,
          surname: customer.surname,
          email: customer.email,
          phoneNumber: customer.phoneNumber,
          country: customer.country,
          city: customer.city,
          town: customer.town,
          gender: customer.gender,
          jobId: customer.job.id,
          remindMe: !customer.remindMe,
          birthday: formattedBirthday,
          isActive: customer.isActive,
        };

        await axios.put(
          `https://360avantajli.com/api/Auth_Service/customer/${id}`,
          requestData
        );
        fetchCustomers();
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage("Bilinmeyen bir hata oluştu.");
      }
      setErrorDialog(true);
    }
  };

  const formatDate = (dateString: string | null | undefined): string => {
    if (
      !dateString ||
      typeof dateString !== "string" ||
      dateString.trim() === ""
    ) {
      return "-";
    }

    try {
      // Check if it's likely a date-only string (e.g., YYYY-MM-DD or DD-MM-YYYY)
      if (dateString.includes(" ") === false) {
        let year = 0,
          month = 0,
          day = 0; // Initialize with default values
        let formatRecognized = false;
        if (dateString.includes("-")) {
          const parts = dateString.split("-");
          if (parts.length === 3) {
            const p0 = parseInt(parts[0], 10);
            const p1 = parseInt(parts[1], 10);
            const p2 = parseInt(parts[2], 10);

            if (
              parts[0].length === 4 &&
              !isNaN(p0) &&
              !isNaN(p1) &&
              !isNaN(p2)
            ) {
              // YYYY-MM-DD
              year = p0;
              month = p1 - 1; // JS months are 0-indexed
              day = p2;
              formatRecognized = true;
            } else if (
              parts[2].length === 4 &&
              !isNaN(p0) &&
              !isNaN(p1) &&
              !isNaN(p2)
            ) {
              // DD-MM-YYYY
              day = p0;
              month = p1 - 1; // JS months are 0-indexed
              year = p2;
              formatRecognized = true;
            }
          }
        }

        if (formatRecognized) {
          const date = new Date(year, month, day);
          if (!isNaN(date.getTime())) {
            return date.toLocaleDateString("tr-TR", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
            });
          }
        }
        // If not a recognized YYYY-MM-DD or DD-MM-YYYY, or parsing failed, try direct parsing
        const parsedSimpleDate = new Date(dateString);
        if (!isNaN(parsedSimpleDate.getTime())) {
          return parsedSimpleDate.toLocaleDateString("tr-TR", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
          });
        }
        return "-"; // Unrecognized date-only format
      }

      // Assumed "DD-MM-YYYY HH:mm:ss" format if it contains a space
      const [datePart, timePart] = dateString.split(" ");
      if (!datePart || !timePart) {
        // Fallback for unexpected space-containing format, try direct parse
        const fallbackDate = new Date(dateString);
        if (!isNaN(fallbackDate.getTime())) {
          return fallbackDate.toLocaleString("tr-TR", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
          });
        }
        return "-";
      }

      const [dayStr, monthStr, yearStr] = datePart.split("-");
      const [hoursStr, minutesStr, secondsStr] = timePart.split(":");

      if (
        !dayStr ||
        !monthStr ||
        !yearStr ||
        !hoursStr ||
        !minutesStr ||
        !secondsStr
      ) {
        return "-"; // Incomplete date/time parts
      }

      const year = parseInt(yearStr, 10);
      const month = parseInt(monthStr, 10) - 1; // JS months are 0-indexed
      const day = parseInt(dayStr, 10);
      const hours = parseInt(hoursStr, 10);
      const minutes = parseInt(minutesStr, 10);
      const seconds = parseInt(secondsStr, 10);

      if (
        isNaN(year) ||
        isNaN(month) ||
        isNaN(day) ||
        isNaN(hours) ||
        isNaN(minutes) ||
        isNaN(seconds)
      ) {
        return "-"; // Parsing resulted in NaN
      }

      const date = new Date(year, month, day, hours, minutes, seconds);

      if (isNaN(date.getTime())) {
        const fallbackDate = new Date(dateString); // Try direct parsing as a final attempt
        if (!isNaN(fallbackDate.getTime())) {
          return fallbackDate.toLocaleString("tr-TR", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
          });
        }
        return "-";
      }

      return date.toLocaleString("tr-TR", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch (error) {
      // Final fallback: if any error occurs, try to parse with new Date() and format if valid
      try {
        const parsedDate = new Date(dateString);
        if (!isNaN(parsedDate.getTime())) {
          // Check if it has a significant time component to decide on toLocaleString vs toLocaleDateString
          if (
            parsedDate.getHours() !== 0 ||
            parsedDate.getMinutes() !== 0 ||
            parsedDate.getSeconds() !== 0 ||
            parsedDate.getMilliseconds() !== 0 ||
            dateString.includes(":")
          ) {
            return parsedDate.toLocaleString("tr-TR", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
            });
          } else {
            return parsedDate.toLocaleDateString("tr-TR", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
            });
          }
        }
      } catch (e) {
        // Fallback parsing also failed
      }
      return "-";
    }
  };

  const getStatusBackgroundColor = (isActive: boolean) => {
    if (isActive) {
      return alpha(theme.palette.success.main, 0.15);
    }
    return alpha(theme.palette.error.main, 0.12);
  };

  // Excel export için render fonksiyonu ekleyin
  const renderExportButton = () => {
    return (
      <ExportButton
        data={filteredCustomers}
        filename={`Müşteriler_${new Date().toISOString().split("T")[0]}`}
        headCells={headCells.filter((cell) => cell.id !== "")} // İşlem sütununu hariç tut
        disabled={filteredCustomers.length === 0}
        tooltip="Filtrelenmiş müşteri verilerini Excel olarak indir"
      />
    );
  };

  return (
    <AdminLayout>
      <Box
        sx={{
          mb: 2,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography variant="h5" component="h1">
          Müşteriler
        </Typography>
        <Box sx={{ display: "flex", gap: 1 }}>
          <ExportButton
            data={filteredCustomers}
            filename={`Müşteriler_${new Date().toISOString().split("T")[0]}`}
            headCells={headCells.filter((cell) => cell.id !== "")}
            disabled={filteredCustomers.length === 0}
            tooltip="Filtrelenmiş müşteri verilerini Excel olarak indir"
          />
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
            disabled={isLoading}
          >
            Yeni Müşteri Ekle
          </Button>
        </Box>
      </Box>

      <AdminFilterBar
        filterData={filterData}
        setFilterData={setFilterData}
        onFilter={() => {}}
        filteredCount={filteredCustomers.length}
        totalCount={customers.length}
        renderExportButton={renderExportButton}
        searchPlaceholder="Ad, Soyad, Email, veya Telefon ile Ara..."
        additionalFilters={[
          {
            field: "gender",
            label: "Cinsiyet",
            type: "select",
            options: [
              { value: "", label: "Tümü" },
              { value: "Erkek", label: "Erkek" },
              { value: "Kadın", label: "Kadın" },
              { value: "Diğer", label: "Diğer" },
            ],
          },
          {
            field: "jobId",
            label: "Meslek",
            type: "select",
            options: [
              { value: "", label: "Tümü" },
              ...jobs.map((job) => ({
                value: job.id.toString(),
                label: job.description,
              })),
            ],
          },
          {
            field: "remindMe",
            label: "Hatırlatma",
            type: "select",
            options: [
              { value: "all", label: "Tümü" },
              { value: "true", label: "Evet" },
              { value: "false", label: "Hayır" },
            ],
          },
        ]}
      />

      {isLoading ? (
        <Box sx={{ display: "flex", justifyContent: "center", p: 3 }}>
          <CircularProgress />
        </Box>
      ) : filteredCustomers.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: "center" }}>
          <Typography variant="h6" color="text.secondary">
            Müşteri bulunamadı.
          </Typography>
        </Paper>
      ) : (
        <>
          <Box
            sx={{
              mb: 2,
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Typography variant="body2" color="text.secondary">
              Toplam {customers.length} müşteri içinden{" "}
              {filteredCustomers.length} müşteri gösteriliyor.
            </Typography>
          </Box>
          <TableContainer component={Paper}>
            <Table stickyHeader aria-label="sticky table">
              <TableHead>
                <TableRow
                  sx={{
                    "& .MuiTableCell-head": {
                      fontWeight: "bold",
                      backgroundColor: theme.palette.grey[100],
                    },
                  }}
                >
                  {headCells.map((headCell) => (
                    <TableCell
                      key={headCell.id}
                      align={headCell.numeric ? "right" : "left"}
                      sortDirection={orderBy === headCell.id ? order : false}
                    >
                      {headCell.sortable ? (
                        <TableSortLabel
                          active={orderBy === headCell.id}
                          direction={orderBy === headCell.id ? order : "asc"}
                          onClick={() =>
                            handleRequestSort(headCell.id as keyof Customer)
                          }
                        >
                          {headCell.label}
                        </TableSortLabel>
                      ) : (
                        headCell.label
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {conditionalMask(filteredCustomers, maskCustomersArray).map((customer) => (
                  <TableRow
                    key={customer.id}
                    sx={{
                      backgroundColor: getStatusBackgroundColor(
                        customer.isActive
                      ),
                      "&:hover": {
                        filter: "brightness(0.95)",
                      },
                      "&:last-child td, &:last-child th": {
                        border: 0,
                      },
                    }}
                  >
                    <TableCell align="right">{customer.id}</TableCell>
                    <TableCell>
                      <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                        {customer.name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                        {customer.surname}
                      </Typography>
                    </TableCell>
                    <TableCell>{customer.email}</TableCell>
                    <TableCell>{customer.phoneNumber}</TableCell>
                    <TableCell>{customer.country}</TableCell>
                    <TableCell>{customer.city}</TableCell>
                    <TableCell>{customer.town}</TableCell>
                    <TableCell>{customer.gender}</TableCell>
                    <TableCell>{customer.job?.description || "N/A"}</TableCell>
                    <TableCell>
                      <Switch
                        checked={customer.remindMe}
                        onChange={() => handleToggleRemindMe(customer.id)}
                        color="info"
                      />
                    </TableCell>
                    <TableCell>{formatDate(customer.birthday)}</TableCell>
                    <TableCell>
                      <Switch
                        checked={customer.isActive}
                        onChange={() => handleToggleActive(customer.id)}
                      />
                    </TableCell>
                    <TableCell>{formatDate(customer.createdAt)}</TableCell>
                    <TableCell>{formatDate(customer.updatedAt)}</TableCell>
                    <TableCell>
                      <IconButton onClick={() => handleOpenDialog(customer)}>
                        <EditIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </>
      )}

      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedCustomer ? "Müşteri Düzenle" : "Yeni Müşteri Ekle"}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="Ad"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              sx={{ mb: 2 }}
              required
              error={!!formErrors.name}
              helperText={formErrors.name}
            />
            <TextField
              fullWidth
              label="Soyad"
              value={formData.surname}
              onChange={(e) =>
                setFormData({ ...formData, surname: e.target.value })
              }
              sx={{ mb: 2 }}
              required
              error={!!formErrors.surname}
              helperText={formErrors.surname}
            />
            <TextField
              fullWidth
              label="E-posta"
              type="email"
              value={formData.email}
              onChange={(e) =>
                setFormData({ ...formData, email: e.target.value })
              }
              sx={{ mb: 2 }}
              required
              error={!!formErrors.email}
              helperText={formErrors.email}
              placeholder="<EMAIL>"
            />
            <TextField
              fullWidth
              label="Telefon"
              value={formData.phoneNumber}
              onChange={(e) => {
                const formattedValue = formatPhoneNumber(e.target.value);
                setFormData({ ...formData, phoneNumber: formattedValue });
              }}
              sx={{ mb: 2 }}
              required
              error={!!formErrors.phoneNumber}
              helperText={
                formErrors.phoneNumber ||
                "5XX XXX XX XX formatında 10 haneli numara giriniz"
              }
              placeholder="5XX XXX XX XX"
              inputProps={{ maxLength: 10 }}
            />
            <TextField
              fullWidth
              label="Ülke"
              value={formData.country}
              onChange={(e) =>
                setFormData({ ...formData, country: e.target.value })
              }
              sx={{ mb: 2 }}
              required
              error={!!formErrors.country}
              helperText={formErrors.country}
            />
            <Box sx={{ mb: 2 }}>
              <CityDistrictSelector
                cityValue={formData.city}
                districtValue={formData.town}
                onCityChange={(city) => setFormData({ ...formData, city })}
                onDistrictChange={(district) =>
                  setFormData({ ...formData, town: district })
                }
                cityError={formErrors.city}
                districtError={formErrors.town}
                required
                size="medium"
                gridSize={{ city: 6, district: 6 }}
              />
            </Box>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Cinsiyet</InputLabel>
              <Select
                value={formData.gender}
                label="Cinsiyet"
                onChange={(e) =>
                  setFormData({ ...formData, gender: e.target.value })
                }
              >
                <MenuItem value="Erkek">Erkek</MenuItem>
                <MenuItem value="Kadın">Kadın</MenuItem>
              </Select>
            </FormControl>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Meslek</InputLabel>
              <Select
                value={formData.jobId}
                label="Meslek"
                onChange={(e) =>
                  setFormData({ ...formData, jobId: Number(e.target.value) })
                }
              >
                {jobs.map((job) => (
                  <MenuItem key={job.id} value={job.id}>
                    {job.description}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField
              fullWidth
              label="Doğum Tarihi"
              type="date"
              value={formData.birthday}
              onChange={(e) =>
                setFormData({ ...formData, birthday: e.target.value })
              }
              sx={{ mb: 2 }}
              InputLabelProps={{ shrink: true }}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={formData.remindMe}
                  onChange={(e) =>
                    setFormData({ ...formData, remindMe: e.target.checked })
                  }
                />
              }
              label="Hatırlatma"
            />
            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={(e) =>
                    setFormData({ ...formData, isActive: e.target.checked })
                  }
                />
              }
              label="Aktif"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} disabled={isLoading}>
            İptal
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={isLoading}
            startIcon={isLoading ? <CircularProgress size={20} /> : null}
          >
            {selectedCustomer ? "Güncelle" : "Ekle"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Hata Dialog */}
      <Dialog
        open={errorDialog}
        onClose={() => setErrorDialog(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            background: "transparent",
            boxShadow: "none",
          },
        }}
      >
        <Box
          sx={{
            bgcolor: "#fff",
            borderRadius: 3,
            boxShadow: 6,
            p: 0,
            position: "relative",
          }}
        >
          <Card
            sx={{
              bgcolor: "#ffeaea",
              border: "1px solid #ffb4b4",
              borderRadius: 3,
              boxShadow: 3,
              mx: 3,
              mt: 3,
              mb: 0,
              p: 0,
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography
                variant="h6"
                sx={{ color: "#d32f2f", fontWeight: 700, mb: 1 }}
              >
                Hata
              </Typography>
              <Typography
                variant="body1"
                sx={{ color: "#b71c1c", fontSize: 17, fontWeight: 500 }}
              >
                {errorMessage}
              </Typography>
            </CardContent>
          </Card>
          <DialogActions
            sx={{ justifyContent: "flex-end", px: 3, pb: 2, pt: 1 }}
          >
            <Button
              onClick={() => setErrorDialog(false)}
              variant="contained"
              sx={{
                bgcolor: "#d32f2f",
                color: "#fff",
                fontWeight: 600,
                borderRadius: 2,
                px: 4,
                py: 1.2,
                boxShadow: 2,
                "&:hover": { bgcolor: "#b71c1c" },
              }}
            >
              Tamam
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </AdminLayout>
  );
}
