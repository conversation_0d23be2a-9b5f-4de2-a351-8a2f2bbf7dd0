import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tooltip,
  alpha,
  useTheme,
} from "@mui/material";
import {
  Edit as EditIcon,
  AdminPanelSettings as AdminIcon,
  Person as UserIcon,
  Support as CrmAdminIcon,
} from "@mui/icons-material";
import AdminLayout from "../../components/admin/AdminLayout";
import AdminFilterBar, {
  AdminFilterData,
} from "../../components/admin/AdminFilterBar";
import ExportButton from "../../components/admin/ExportButton";
import { useAuth } from "../../contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import api from "../../utils/api";
import {
  maskUsersArray,
  conditionalMask,
  maskName,
  maskSurname,
  maskEmail,
} from "../../utils/dataMaskingUtils";

interface User {
  id: number;
  name: string;
  surname: string;
  email: string;
  username: string;
  role: "ADMIN" | "USER" | "CRM_ADMIN";
  phoneNumber?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

const UsersPage: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user: currentUser, updateUser } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Dialog states
  const [roleDialogOpen, setRoleDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [newRole, setNewRole] = useState<"ADMIN" | "USER" | "CRM_ADMIN">(
    "USER"
  );
  const [isUpdatingRole, setIsUpdatingRole] = useState(false);

  // Filter states
  const [filterData, setFilterData] = useState<AdminFilterData>({
    searchText: "",
    role: "all",
    isActive: "all",
  });

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [users, filterData]);

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await api.get(
        "/api/Auth_Service/customer"
      );
      const usersData = Array.isArray(response.data) ? response.data : [];
      setUsers(usersData);
    } catch (error: any) {
      setUsers([]);
      setError(
        error.response?.data?.message ||
          error.message ||
          "Kullanıcılar yüklenirken bir hata oluştu."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...users];

    // Search filter
    if (filterData.searchText && filterData.searchText.trim()) {
      const searchTerm = filterData.searchText.toLowerCase().trim();
      filtered = filtered.filter(
        (user) =>
          (user.name && user.name.toLowerCase().includes(searchTerm)) ||
          (user.surname && user.surname.toLowerCase().includes(searchTerm)) ||
          (user.email && user.email.toLowerCase().includes(searchTerm)) ||
          (user.username && user.username.toLowerCase().includes(searchTerm)) ||
          (user.phoneNumber &&
            user.phoneNumber.toLowerCase().includes(searchTerm))
      );
    }

    // Role filter
    if (filterData.role && filterData.role !== "all") {
      filtered = filtered.filter((user) => user.role === filterData.role);
    }

    // Active status filter
    if (filterData.isActive && filterData.isActive !== "all") {
      const isActiveFilter = filterData.isActive === "active";
      filtered = filtered.filter((user) => user.isActive === isActiveFilter);
    }

    setFilteredUsers(filtered);
  };

  const handleOpenRoleDialog = (user: User) => {
    setSelectedUser(user);
    setNewRole(user.role);
    setRoleDialogOpen(true);
  };

  const handleCloseRoleDialog = () => {
    setRoleDialogOpen(false);
    setSelectedUser(null);
    setNewRole("USER");
  };

  const handleRoleChange = async () => {
    if (!selectedUser || !currentUser) return;

    // Kullanıcının kendi rolünü ADMIN'den USER'a değiştirmeye çalışıp çalışmadığını kontrol et
    const isChangingOwnRoleToUser =
      selectedUser.id === currentUser.id &&
      selectedUser.role === "ADMIN" &&
      newRole === "USER";

    try {
      setIsUpdatingRole(true);
      setError(null);
      setSuccess(null);

      await api.put(
        `/api/Auth_Service/customer/change-role/${selectedUser.id}`,
        newRole,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      // Eğer kullanıcı kendi rolünü USER'a değiştirdiyse
      if (isChangingOwnRoleToUser) {
        // Auth context'i güncelle
        updateUser({ role: "USER" });

        // Kısa bir süre sonra ana sayfaya yönlendir
        setTimeout(() => {
          navigate("/");
        }, 1500);

        setSuccess(
          "Rolünüz USER olarak güncellendi. Admin panelinden çıkış yapılıyor..."
        );
      } else {
        setSuccess(
          `${selectedUser.name} ${selectedUser.surname} kullanıcısının rolü başarıyla ${newRole} olarak güncellendi.`
        );
      }

      handleCloseRoleDialog();
      await fetchUsers();
    } catch (error: any) {
      setError(
        error.response?.data?.message ||
          error.message ||
          "Rol güncellenirken bir hata oluştu."
      );
    } finally {
      setIsUpdatingRole(false);
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "ADMIN":
        return "error";
      case "CRM_ADMIN":
        return "warning";
      default:
        return "primary";
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "ADMIN":
        return <AdminIcon />;
      case "CRM_ADMIN":
        return <CrmAdminIcon />;
      default:
        return <UserIcon />;
    }
  };

  const formatDate = (dateString: string | null | undefined): string => {
    if (
      !dateString ||
      typeof dateString !== "string" ||
      dateString.trim() === ""
    ) {
      return "-";
    }
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("tr-TR");
    } catch {
      return "-";
    }
  };

  const renderExportButton = () => {
    return (
      <ExportButton
        data={filteredUsers}
        filename={`Kullanıcılar_${new Date().toISOString().split("T")[0]}`}
        headCells={[
          { id: "id", label: "ID" },
          { id: "name", label: "Ad" },
          { id: "surname", label: "Soyad" },
          { id: "email", label: "E-posta" },
          { id: "username", label: "Kullanıcı Adı" },
          { id: "role", label: "Rol" },
          { id: "phoneNumber", label: "Telefon" },
          { id: "isActive", label: "Aktif" },
          { id: "createdAt", label: "Oluşturulma Tarihi" },
          { id: "updatedAt", label: "Güncellenme Tarihi" },
        ]}
        disabled={filteredUsers.length === 0}
        tooltip="Filtrelenmiş kullanıcıları Excel olarak indir"
      />
    );
  };

  return (
    <AdminLayout>
      <Box sx={{ mb: 2 }}>
        <Typography variant="h5" component="h1" sx={{ mb: 2 }}>
          Kullanıcı Yönetimi
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert
          severity="success"
          sx={{ mb: 2 }}
          onClose={() => setSuccess(null)}
        >
          {success}
        </Alert>
      )}

      <AdminFilterBar
        filterData={filterData}
        setFilterData={setFilterData}
        onFilter={() => {}}
        filteredCount={filteredUsers.length}
        totalCount={users.length}
        searchPlaceholder="Kullanıcı adı veya email ara..."
        renderExportButton={renderExportButton}
      />

      {isLoading ? (
        <Box sx={{ display: "flex", justifyContent: "center", py: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper} sx={{ mt: 2 }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell align="right">ID</TableCell>
                <TableCell>Ad</TableCell>
                <TableCell>Soyad</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Kullanıcı Adı</TableCell>
                <TableCell>Telefon</TableCell>
                <TableCell>Rol</TableCell>
                <TableCell>Durum</TableCell>
                <TableCell>Oluşturulma</TableCell>
                <TableCell>Güncelleme</TableCell>
                <TableCell>İşlemler</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {conditionalMask(filteredUsers, maskUsersArray).map((user) => (
                <TableRow
                  key={user.id}
                  sx={{
                    "&:nth-of-type(odd)": {
                      backgroundColor: alpha(theme.palette.primary.main, 0.02),
                    },
                    "&:hover": {
                      backgroundColor: alpha(theme.palette.primary.main, 0.04),
                    },
                  }}
                >
                  <TableCell align="right">{user.id}</TableCell>
                  <TableCell>
                    <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                      {user.name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                      {user.surname}
                    </Typography>
                  </TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{user.username}</TableCell>
                  <TableCell>{user.phoneNumber || "-"}</TableCell>
                  <TableCell>
                    <Chip
                      icon={getRoleIcon(user.role)}
                      label={user.role}
                      color={getRoleColor(user.role)}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={user.isActive ? "Aktif" : "Pasif"}
                      color={user.isActive ? "success" : "default"}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>{formatDate(user.createdAt)}</TableCell>
                  <TableCell>{formatDate(user.updatedAt)}</TableCell>
                  <TableCell>
                    <Tooltip title="Rol Değiştir">
                      <IconButton
                        onClick={() => handleOpenRoleDialog(user)}
                        color="primary"
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Role Change Dialog */}
      <Dialog
        open={roleDialogOpen}
        onClose={handleCloseRoleDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Kullanıcı Rolü Değiştir</DialogTitle>
        <DialogContent>
          {selectedUser && currentUser && (
            <Box sx={{ pt: 1 }}>
              <Typography variant="body1" sx={{ mb: 2 }}>
                <strong>
                  {conditionalMask(selectedUser.name, maskName)}{" "}
                  {conditionalMask(selectedUser.surname, maskSurname)}
                </strong>{" "}
                kullanıcısının rolünü değiştirmek istediğinizden emin misiniz?
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Email: {conditionalMask(selectedUser.email, maskEmail)}
              </Typography>

              {/* Kendi rolünü değiştirme uyarısı */}
              {selectedUser.id === currentUser.id &&
                selectedUser.role === "ADMIN" &&
                newRole === "USER" && (
                  <Alert severity="warning" sx={{ mb: 3 }}>
                    <Typography variant="body2">
                      <strong>Dikkat!</strong> Kendi rolünüzü USER olarak
                      değiştiriyorsunuz. Bu işlem sonrasında admin paneline
                      erişiminiz kalkacak ve ana sayfaya yönlendirileceksiniz.
                    </Typography>
                  </Alert>
                )}

              <FormControl fullWidth>
                <InputLabel>Yeni Rol</InputLabel>
                <Select
                  value={newRole}
                  label="Yeni Rol"
                  onChange={(e) =>
                    setNewRole(e.target.value as "ADMIN" | "USER" | "CRM_ADMIN")
                  }
                >
                  <MenuItem value="USER">
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <UserIcon />
                      USER
                    </Box>
                  </MenuItem>
                  <MenuItem value="CRM_ADMIN">
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <CrmAdminIcon />
                      CRM_ADMIN
                    </Box>
                  </MenuItem>
                  <MenuItem value="ADMIN">
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <AdminIcon />
                      ADMIN
                    </Box>
                  </MenuItem>
                </Select>
              </FormControl>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseRoleDialog} disabled={isUpdatingRole}>
            İptal
          </Button>
          <Button
            onClick={handleRoleChange}
            variant="contained"
            disabled={isUpdatingRole || newRole === selectedUser?.role}
            startIcon={isUpdatingRole ? <CircularProgress size={16} /> : null}
          >
            {isUpdatingRole ? "Güncelleniyor..." : "Rolü Güncelle"}
          </Button>
        </DialogActions>
      </Dialog>
    </AdminLayout>
  );
};

export default UsersPage;
