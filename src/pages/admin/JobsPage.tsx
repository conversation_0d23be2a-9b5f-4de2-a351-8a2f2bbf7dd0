import React, { useState, useEffect } from 'react';
import api from '../../utils/api';
import { useAuth } from "../../contexts/AuthContext";
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Typography,
  CircularProgress,
  TableSortLabel,
  FormControlLabel,
  Switch,
  Card,
  CardContent,
} from '@mui/material';
import { Edit as EditIcon, Add as AddIcon } from '@mui/icons-material';
import AdminLayout from '../../components/admin/AdminLayout';

interface Job {
  id: number;
  description: string;
  isActive: boolean;
}

// Sorting type definition
type Order = 'asc' | 'desc';

interface HeadCell {
  id: keyof Job;
  label: string;
  numeric: boolean;
  sortable: boolean;
}

export default function JobsPage() {
  const { token } = useAuth();
  const [jobs, setJobs] = useState<Job[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    description: '',
    isActive: true,
  });

  // Form hata durumları için state
  const [formErrors, setFormErrors] = useState({
    description: '',
  });

  // Sorting states
  const [order, setOrder] = useState<Order>('asc');
  const [orderBy, setOrderBy] = useState<keyof Job>('id');

  const [errorDialog, setErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    fetchJobs();
  }, []);

  const fetchJobs = async () => {
    try {
      const response = await api.get('/api/Campaign_Service/job');
      setJobs(response.data);
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
        setErrorDialog(true);
      }
    }
  };

  // Sorting functions
  function descendingComparator<T>(a: T, b: T, orderBy: keyof T) {
    if (b[orderBy] < a[orderBy]) {
      return -1;
    }
    if (b[orderBy] > a[orderBy]) {
      return 1;
    }
    return 0;
  }

  function getComparator<Key extends keyof any>(
    order: Order,
    orderBy: Key,
  ): (a: { [key in Key]: any }, b: { [key in Key]: any }) => number {
    return order === 'desc'
      ? (a, b) => descendingComparator(a, b, orderBy)
      : (a, b) => -descendingComparator(a, b, orderBy);
  }

  function stableSort<T>(array: readonly T[], comparator: (a: T, b: T) => number) {
    const stabilizedThis = array.map((el, index) => [el, index] as [T, number]);
    stabilizedThis.sort((a, b) => {
      const order = comparator(a[0], b[0]);
      if (order !== 0) {
        return order;
      }
      return a[1] - b[1];
    });
    return stabilizedThis.map((el) => el[0]);
  }

  const handleRequestSort = (property: keyof Job) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  // Table header cells configuration
  const headCells: readonly HeadCell[] = [
    { id: 'id', numeric: true, sortable: true, label: 'ID' },
    { id: 'description', numeric: false, sortable: true, label: 'Meslek Adı' },
    { id: 'isActive', numeric: false, sortable: false, label: 'Aktif' },
    { id: 'id', numeric: false, sortable: false, label: 'İşlemler' },
  ];

  const handleOpenDialog = (job?: Job) => {
    setFormErrors({
      description: '',
    });

    if (job) {
      setSelectedJob(job);
      setFormData({
        description: job.description,
        isActive: job.isActive,
      });
    } else {
      setSelectedJob(null);
      setFormData({
        description: '',
        isActive: true,
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedJob(null);
    setFormData({
      description: '',
      isActive: true,
    });
  };

  const validateForm = () => {
    let isValid = true;
    const errors = {
      description: '',
    };

    if (!formData.description.trim()) {
      errors.description = 'Meslek adı boş olamaz';
      isValid = false;
    } else if (formData.description.trim().length < 2) {
      errors.description = 'Meslek adı en az 2 karakter olmalıdır';
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setIsLoading(true);
      if (selectedJob) {
        await api.put(`/api/Campaign_Service/job/${selectedJob.id}`, {
          description: formData.description.trim(),
          isActive: formData.isActive
        });
      } else {
        await api.post('/api/Campaign_Service/job', {
          description: formData.description.trim(),
          isActive: formData.isActive
        });
      }
      fetchJobs();
      handleCloseDialog();
    } catch (error: any) {
      console.error('Meslek kaydedilirken hata oluştu:', error);
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
        setErrorDialog(true);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Bu mesleği silmek istediğinizden emin misiniz?')) {
      try {
        await api.delete(`/api/Campaign_Service/job/${id}`);
        fetchJobs();
      } catch (error: any) {
         if (error.response?.data?.message) {
          setErrorMessage(error.response.data.message);
          setErrorDialog(true);
        }
      }
    }
  };

  const handleToggleActive = async (id: number) => {
    try {
      const job = jobs.find(j => j.id === id);
      if (job) {
        await api.put(`/api/Campaign_Service/job/${id}`, {
          description: job.description,
          isActive: !job.isActive
        });
        fetchJobs();
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
        setErrorDialog(true);
      }
    }
  };

  return (
    <AdminLayout>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5" component="h1">
          Meslekler
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          disabled={isLoading}
        >
          Yeni Meslek Ekle
        </Button>
      </Box>

      {isLoading ? (
        <CircularProgress />
      ) : (
        <TableContainer component={Paper} sx={{ maxHeight: 'calc(100vh - 240px)' }}>
          <Table stickyHeader aria-label="sticky table">
            <TableHead>
              <TableRow>
                {headCells.map((headCell) => (
                  <TableCell
                    key={headCell.id}
                    align={headCell.numeric ? 'right' : 'left'}
                    sortDirection={orderBy === headCell.id ? order : false}
                  >
                    {headCell.sortable ? (
                      <TableSortLabel
                        active={orderBy === headCell.id}
                        direction={orderBy === headCell.id ? order : 'asc'}
                        onClick={() => handleRequestSort(headCell.id as keyof Job)}
                      >
                        {headCell.label}
                      </TableSortLabel>
                    ) : (
                      headCell.label
                    )}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {stableSort(jobs, getComparator(order, orderBy))
                .map((job) => (
                  <TableRow hover key={job.id}>
                    <TableCell>{job.id}</TableCell>
                    <TableCell>{job.description}</TableCell>
                    <TableCell>
                      <Switch
                        checked={job.isActive}
                        onChange={() => handleToggleActive(job.id)}
                      />
                    </TableCell>
                    <TableCell style={{ position: 'sticky', right: 0, background: 'white', zIndex: 1 }}>
                      <IconButton onClick={() => handleOpenDialog(job)} size="small">
                        <EditIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedJob ? 'Meslek Düzenle' : 'Yeni Meslek Ekle'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="Meslek Adı"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              sx={{ mb: 2 }}
              required
              error={!!formErrors.description}
              helperText={formErrors.description}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                />
              }
              label="Aktif"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} disabled={isLoading}>İptal</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={isLoading}
            startIcon={isLoading ? <CircularProgress size={20} /> : null}
          >
            {selectedJob ? 'Güncelle' : 'Ekle'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Hata Dialog */}
      <Dialog
        open={errorDialog}
        onClose={() => setErrorDialog(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            background: 'transparent',
            boxShadow: 'none',
          }
        }}
      >
        <Box
          sx={{
            bgcolor: '#fff',
            borderRadius: 3,
            boxShadow: 6,
            p: 0,
            position: 'relative',
          }}
        >
          <Card
            sx={{
              bgcolor: '#ffeaea',
              border: '1px solid #ffb4b4',
              borderRadius: 3,
              boxShadow: 3,
              mx: 3,
              mt: 3,
              mb: 0,
              p: 0,
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ color: '#d32f2f', fontWeight: 700, mb: 1 }}>
                Hata
              </Typography>
              <Typography variant="body1" sx={{ color: '#b71c1c', fontSize: 17, fontWeight: 500 }}>
                {errorMessage}
              </Typography>
            </CardContent>
          </Card>
          <DialogActions sx={{ justifyContent: 'flex-end', px: 3, pb: 2, pt: 1 }}>
            <Button
              onClick={() => setErrorDialog(false)}
              variant="contained"
              sx={{
                bgcolor: '#d32f2f',
                color: '#fff',
                fontWeight: 600,
                borderRadius: 2,
                px: 4,
                py: 1.2,
                boxShadow: 2,
                '&:hover': { bgcolor: '#b71c1c' }
              }}
            >
              Tamam
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </AdminLayout>
  );
} 