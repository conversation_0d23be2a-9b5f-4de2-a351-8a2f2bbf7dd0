import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Alert,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  People as PeopleIcon,
  Webhook as WebhookIcon,
  Phone as PhoneIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

interface Contact {
  uuid: string;
  name: string;
  surname: string;
  email: string;
  phoneNumber: string;
  countryCode: string;
  status: string;
  campaignName?: string;
  brandName?: string;
  createdAt: string;
  contactPhone?: string;
  contactEmail?: string;
}

interface WebhookEvent {
  uuid: string;
  event: string;
  callId: string;
  number: string;
  duration: number;
  analysis: string;
  endReason: string;
  webhookTimestamp: string;
  createdAt: string;
}

interface CrmStats {
  totalContacts: number;
  totalWebhooks: number;
  activeContacts: number;
  recentWebhooks: number;
  lastSyncTime: string;
}

const CrmDashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { token } = useAuth();
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [webhooks, setWebhooks] = useState<WebhookEvent[]>([]);
  const [stats, setStats] = useState<CrmStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchCrmData();
  }, []);



  const fetchCrmData = async () => {
    try {
      setLoading(true);
      setError(null);

      const headers = {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      };

      const [contactsResponse, webhooksResponse] = await Promise.all([
        axios.get('https://360avantajli.com/api/Campaign_Service/crm-admin/contacts', { headers }),
        axios.get('https://360avantajli.com/api/Campaign_Service/crm-admin/webhook-events', { headers }),
      ]);

      const contactsData = contactsResponse.data || [];
      const webhooksData = webhooksResponse.data || [];

      const contactsArray = Array.isArray(contactsData) ? contactsData : (contactsData.content || contactsData.data || []);
      const webhooksArray = Array.isArray(webhooksData) ? webhooksData : (webhooksData.content || webhooksData.data || []);

      setContacts(contactsArray.slice(0, 10));
      setWebhooks(webhooksArray.slice(0, 10));

      setStats({
        totalContacts: contactsArray.length,
        totalWebhooks: webhooksArray.length,
        activeContacts: contactsArray.filter((c: Contact) => c.status === 'Active').length,
        recentWebhooks: webhooksArray.filter((w: WebhookEvent) => {
          const webhookDate = new Date(w.createdAt);
          const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
          return webhookDate > oneDayAgo;
        }).length,
        lastSyncTime: new Date().toISOString(),
      });
    } catch (err: unknown) {
      let message = 'Bilinmeyen bir hata oluştu.';
      if (axios.isAxiosError(err) && err.response) {
        message = err.response.data?.message || err.message;
      } else if (err instanceof Error) {
        message = err.message;
      }
      setError(
        'CRM verileri alınamadı. Lütfen API bağlantısını ve yetkilendirmeyi kontrol edin. ' + message
      );
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" gutterBottom>
            CRM Admin Dashboard
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            CRM entegrasyonu ve endpoint yönetimi
          </Typography>
        </Box>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchCrmData}
          >
            Yenile
          </Button>

        </Box>
      </Box>

      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {error} (Geliştirme verisi gösteriliyor)
        </Alert>
      )}

      {/* İstatistikler */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ cursor: 'pointer' }} onClick={() => navigate('/crm-admin/contacts')}>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <PeopleIcon color="primary" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{stats.totalContacts}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Toplam Contact
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ cursor: 'pointer' }} onClick={() => navigate('/crm-admin/webhooks')}>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <WebhookIcon color="success" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{stats.totalWebhooks}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Toplam Webhook
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <CheckCircleIcon color="success" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{stats.activeContacts}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Aktif Contact
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <PhoneIcon color="info" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{stats.recentWebhooks}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Son 24s Webhook
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Recent Contacts ve Webhooks */}
      <Grid container spacing={3}>
        {/* Recent Contacts */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">
                Son Contacts
              </Typography>
              <Button
                variant="outlined"
                size="small"
                onClick={() => navigate('/crm-admin/contacts')}
              >
                Tümünü Gör
              </Button>
            </Box>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>İsim</TableCell>
                    <TableCell>Email</TableCell>
                    <TableCell>Telefon</TableCell>
                    <TableCell>Durum</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {contacts.slice(0, 5).map((contact) => (
                    <TableRow key={contact.uuid}>
                      <TableCell>
                        {contact.name} {contact.surname}
                      </TableCell>
                      <TableCell>{contact.email}</TableCell>
                      <TableCell>{contact.phoneNumber}</TableCell>
                      <TableCell>
                        <Chip
                          label={contact.status}
                          size="small"
                          color={contact.status === 'Active' ? 'success' : 'default'}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>

        {/* Recent Webhooks */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">
                Son Webhooks
              </Typography>
              <Button
                variant="outlined"
                size="small"
                onClick={() => navigate('/crm-admin/webhooks')}
              >
                Tümünü Gör
              </Button>
            </Box>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Call ID</TableCell>
                    <TableCell>Telefon</TableCell>
                    <TableCell>Süre</TableCell>
                    <TableCell>Durum</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {webhooks.slice(0, 5).map((webhook) => (
                    <TableRow key={webhook.uuid}>
                      <TableCell>{webhook.callId}</TableCell>
                      <TableCell>{webhook.number}</TableCell>
                      <TableCell>{Math.floor(webhook.duration / 60)}dk</TableCell>
                      <TableCell>
                        <Chip
                          label={webhook.endReason}
                          size="small"
                          color={webhook.endReason === 'completed' ? 'success' : 'warning'}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CrmDashboardPage;
