import React, { useState, useEffect, useMemo } from 'react';
import axios from 'axios';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  Grid,
  Card,
  CardContent,
  Chip,
  Tooltip,
  Button,
  Snackbar,
} from '@mui/material';
import {
  PersonOff as PersonOffIcon,
  People as PeopleIcon,
  PhoneDisabled as PhoneDisabledIcon,
  PhoneForwarded as PhoneForwardedIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import AdminFilterBar, { AdminFilterData } from '../../components/admin/AdminFilterBar';
import ExportButton from '../../components/admin/ExportButton';
import crmService from '../../services/crmService';
import { formatReadableDate, parseToDate } from '../../utils/dateUtils'; // Import the new function
import { useDebouncedCallback } from 'use-debounce';

// <PERSON>yüz Tanımları
interface UnreachableUser {
  id: string; // email veya phone
  name: string;
  surname: string;
  email: string;
  phoneNumber: string;
  source: 'Form' | 'Customer';
  campaignName?: string;
  createdAt: string;
}

interface WebhookEvent {
  number: string;
  // Diğer alanlar
}


// İstatistik kartı komponenti
const StatCard: React.FC<{ title: string; value: string | number; icon: React.ReactNode }> = ({ title, value, icon }) => (
  <Card>
    <CardContent>
      <Box display="flex" alignItems="center" gap={2}>
        {icon}
        <Box>
          <Typography variant="h5">{value}</Typography>
          <Typography color="text.secondary">{title}</Typography>
        </Box>
      </Box>
    </CardContent>
  </Card>
);

const UnreachableUsersPage: React.FC = () => {
  const { token } = useAuth();
  const [unreachableUsers, setUnreachableUsers] = useState<UnreachableUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filterData, setFilterData] = useState<AdminFilterData>({
    searchText: '',
    source: 'all',
    campaignName: 'all',
    dateRange: { startDate: null, endDate: null, startTime: '00:00', endTime: '23:59' },
  });
  const [filteredUsers, setFilteredUsers] = useState<UnreachableUser[]>([]);
  const [callingState, setCallingState] = useState<{ [key: string]: boolean }>({});
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' } | null>(null);

  const debouncedSetFilterData = useDebouncedCallback((data: AdminFilterData) => {
    setFilterData(data);
  }, 300);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const headers = token ? { Authorization: `Bearer ${token}` } : {};

      const [
        formCampaignsRes,
        customerCampaignsRes,
        webhooksRes,
      ] = await Promise.all([
        axios.get('https://360avantajli.com/api/Campaign_Service/form-to-campaign', { headers }),
        axios.get('https://360avantajli.com/api/Campaign_Service/customer-to-campaign', { headers }),
        axios.get('https://360avantajli.com/api/Campaign_Service/crm-admin/webhook-events', { headers }),
      ]);

      const formCampaigns = formCampaignsRes.data || [];
      const customerCampaigns = customerCampaignsRes.data || [];
      const webhookEvents: WebhookEvent[] = webhooksRes.data.content || webhooksRes.data.data || [];
      
      const contactedPhones = new Set(
        webhookEvents.map(w => w.number.startsWith('+90') ? w.number.substring(3) : w.number)
      );
      
      const allLeads: UnreachableUser[] = [];

      // Form verilerinden lead'leri ekle
      formCampaigns.forEach((fc: any) => {
        if (fc.form && (fc.form.phoneNumber || fc.form.email)) {
          allLeads.push({
            id: fc.form.email || fc.form.phoneNumber,
            name: fc.form.name,
            surname: fc.form.surname,
            email: fc.form.email,
            phoneNumber: fc.form.phoneNumber,
            source: 'Form',
            campaignName: fc.campaign?.name,
            createdAt: fc.createdAt,
          });
        }
      });
      
      // Müşteri verilerinden lead'leri ekle
      customerCampaigns.forEach((cc: any) => {
        if (cc.customer && (cc.customer.phoneNumber || cc.customer.email)) {
          allLeads.push({
            id: cc.customer.email || cc.customer.phoneNumber,
            name: cc.customer.name,
            surname: cc.customer.surname,
            email: cc.customer.email,
            phoneNumber: cc.customer.phoneNumber,
            source: 'Customer',
            campaignName: cc.campaign?.name,
            createdAt: cc.createdAt,
          });
        }
      });

      // Ulaşılamayanları filtrele
      const unreachable = allLeads.filter(lead => {
        const normalizedPhone = lead.phoneNumber?.replace(/\s/g, '');
        return !contactedPhones.has(normalizedPhone);
      });
      
      // Tekilleştir
      const uniqueUnreachable = Array.from(new Map(unreachable.map(u => [u.id, u])).values());

      setUnreachableUsers(uniqueUnreachable);

    } catch (err: any) {
      setError('Veri yüklenirken bir hata oluştu: ' + (err.message || 'Bilinmeyen Hata'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (token) {
      fetchData();
    }
  }, [token]);

  useEffect(() => {
    const applyFilters = () => {
      const filtered = unreachableUsers.filter(user => {
        const search = filterData.searchText?.toLowerCase() || '';
        const matchesSearch =
          !search ||
          user.name?.toLowerCase().includes(search) ||
          user.surname?.toLowerCase().includes(search) ||
          user.email?.toLowerCase().includes(search) ||
          user.phoneNumber?.includes(search);

        const matchesSource = filterData.source === 'all' || user.source.toLowerCase() === filterData.source;
        const matchesCampaign = !filterData.campaignName || filterData.campaignName === 'all' || user.campaignName === filterData.campaignName;

        let matchesDate = true;
        const contactDate = parseToDate(user.createdAt);

        if (contactDate) {
            if (filterData.dateRange?.startDate) {
                const dateParts = filterData.dateRange.startDate.split('-').map(Number);
                const timeParts = (filterData.dateRange.startTime || '00:00').split(':').map(Number);
                const startDate = new Date(dateParts[0], dateParts[1] - 1, dateParts[2], timeParts[0], timeParts[1]);
                matchesDate = matchesDate && contactDate >= startDate;
            }
            if (filterData.dateRange?.endDate) {
                const dateParts = filterData.dateRange.endDate.split('-').map(Number);
                const timeParts = (filterData.dateRange.endTime || '23:59').split(':').map(Number);
                const endDate = new Date(dateParts[0], dateParts[1] - 1, dateParts[2], timeParts[0], timeParts[1]);
                matchesDate = matchesDate && contactDate <= endDate;
            }
        } else if (filterData.dateRange?.startDate || filterData.dateRange?.endDate) {
            matchesDate = false;
        }
        
        return matchesSearch && matchesSource && matchesCampaign && matchesDate;
      });
      setFilteredUsers(filtered);
    };

    applyFilters();
  }, [unreachableUsers, filterData]);

  const stats = useMemo(() => ({
    totalLeads: unreachableUsers.length + (/* ananlşan lead sayısı buraya eklenecek */ 0),
    unreachableCount: filteredUsers.length,
    unreachableRate: unreachableUsers.length > 0 ? `${((filteredUsers.length / unreachableUsers.length) * 100).toFixed(1)}%` : '0%',
  }), [unreachableUsers, filteredUsers]);

  const sourceOptions = [
    { value: 'all', label: 'Tümü' },
    { value: 'form', label: 'Form' },
    { value: 'customer', label: 'Müşteri' },
  ];

  const campaignOptions = useMemo(() => {
    const campaigns = unreachableUsers.map(u => u.campaignName);
    const uniqueCampaigns = [...new Set(campaigns.filter(Boolean as any as (x: string | undefined) => x is string))];
    
    return [
      { value: 'all', label: 'Tüm Kampanyalar' },
      ...uniqueCampaigns.map(c => ({ value: c, label: c }))
    ];
  }, [unreachableUsers]);

  const handleCallUser = async (user: UnreachableUser) => {
    if (!user.phoneNumber) {
      setSnackbar({ open: true, message: 'Bu kullanıcı için telefon numarası bulunmuyor.', severity: 'error' });
      return;
    }
    setCallingState(prev => ({ ...prev, [user.id]: true }));
    
    const result = await crmService.triggerCallForUser(user.phoneNumber, user.campaignName);
    
    setSnackbar({
      open: true,
      message: result.message,
      severity: result.success ? 'success' : 'error',
    });
    
    if (result.success) {
      // Başarılı olursa, kullanıcıyı listeden kaldır
      setUnreachableUsers(prev => prev.filter(u => u.id !== user.id));
    }

    setCallingState(prev => ({ ...prev, [user.id]: false }));
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Ulaşılamayan Müşteriler
      </Typography>

      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={4} md={4}>
          <StatCard title="Toplam Potansiyel Müşteri" value={stats.totalLeads} icon={<PeopleIcon color="primary" fontSize="large" />} />
        </Grid>
        <Grid item xs={12} sm={4} md={4}>
          <StatCard title="Ulaşılamayan Müşteri Sayısı" value={stats.unreachableCount} icon={<PersonOffIcon color="error" fontSize="large" />} />
        </Grid>
        <Grid item xs={12} sm={4} md={4}>
          <StatCard title="Ulaşılamama Oranı" value={stats.unreachableRate} icon={<PhoneDisabledIcon color="warning" fontSize="large" />} />
        </Grid>
      </Grid>
      
      <AdminFilterBar
        filterData={filterData}
        setFilterData={debouncedSetFilterData}
        showDateFilter={true}
        additionalFilters={[
          { field: 'source', label: 'Kaynak', type: 'select', options: sourceOptions },
          { field: 'campaignName', label: 'Kampanya', type: 'select', options: campaignOptions },
        ]}
        renderExportButton={() => (
          <ExportButton
            data={filteredUsers}
            filename="Ulasilamayan_Musteriler"
            headCells={[
              { id: 'name', label: 'Ad' },
              { id: 'surname', label: 'Soyad' },
              { id: 'email', label: 'Email' },
              { id: 'phoneNumber', label: 'Telefon' },
              { id: 'source', label: 'Kaynak' },
              { id: 'campaignName', label: 'Kampanya' },
              { id: 'createdAt', label: 'Oluşturulma Tarihi' },
            ]}
          />
        )}
      />
      
      {loading ? (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error">{error}</Alert>
      ) : (
        <Paper>
          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Ad Soyad</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell>Telefon</TableCell>
                  <TableCell>Kaynak</TableCell>
                  <TableCell>Kampanya</TableCell>
                  <TableCell>Kayıt Tarihi</TableCell>
                  <TableCell align="center">İşlem</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>{user.name} {user.surname}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>{user.phoneNumber}</TableCell>
                    <TableCell>
                      <Tooltip title={`Kaynak: ${user.source}`}>
                        <Chip 
                          label={user.source}
                          size="small"
                          color={user.source === 'Form' ? 'info' : 'secondary'}
                          variant="outlined"
                        />
                      </Tooltip>
                    </TableCell>
                    <TableCell>{user.campaignName || '-'}</TableCell>
                    <TableCell>{formatReadableDate(user.createdAt)}</TableCell>
                    <TableCell align="center">
                      <Button
                        variant="contained"
                        size="small"
                        startIcon={callingState[user.id] ? <CircularProgress size={16} color="inherit" /> : <PhoneForwardedIcon />}
                        onClick={() => handleCallUser(user)}
                        disabled={callingState[user.id] || !user.phoneNumber}
                      >
                        {callingState[user.id] ? 'Aranıyor...' : 'Tekrar Ara'}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {snackbar && (
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar(null)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert onClose={() => setSnackbar(null)} severity={snackbar.severity} sx={{ width: '100%' }}>
            {snackbar.message}
          </Alert>
        </Snackbar>
      )}
    </Box>
  );
};

export default UnreachableUsersPage; 