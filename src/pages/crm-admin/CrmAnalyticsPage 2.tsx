import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../../contexts/AuthContext';
import {
  Box,
  Grid,
  Typography,
  CircularProgress,
  Al<PERSON>,
  Card,
  CardContent,
  useTheme,
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
} from 'recharts';

// Veri Modelleri
interface WebhookEvent {
  uuid: string;
  event: string;
  endReason: string;
  webhookTimestamp: string;
  duration: number;
  pipeline?: string;
}

interface AnalyticsData {
  eventsOverTime: { time: string; count: number }[];
  endReasonDistribution: { name: string; value: number }[];
  pipelineStageDistribution: { name: string; count: number }[];
  totalEvents: number;
  averageDuration: number;
}

const CrmAnalyticsPage: React.FC = () => {
  const theme = useTheme();
  const { token } = useAuth();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const END_REASON_COLORS: { [key: string]: string } = {
    completed: theme.palette.success.main,
    customer_hangup: theme.palette.warning.main,
    system_error: theme.palette.error.main,
    default: theme.palette.grey[500],
  };

  useEffect(() => {
    const fetchWebhookData = async () => {
      if (!token) {
        setError("Yetkilendirme gerekli. Lütfen giriş yapın.");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        
        const headers = { 'Authorization': `Bearer ${token}` };
        const response = await axios.get('https://360avantajli.com/api/Campaign_Service/crm-admin/webhook-events', { headers });
        const webhooks: WebhookEvent[] = (Array.isArray(response.data) ? response.data : response.data.data) || [];

        // Veriyi Analiz Et
        // 1. Zamana Göre Olaylar (son 24 saat, saatlik)
        const now = new Date();
        const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        const hourlyCounts: { [key: string]: number } = {};
        for (let i = 0; i < 24; i++) {
            const hour = new Date(last24Hours.getTime() + i * 60 * 60 * 1000);
            hourlyCounts[hour.getHours().toString().padStart(2, '0') + ':00'] = 0;
        }
        
        webhooks
            .filter(w => new Date(w.webhookTimestamp) > last24Hours)
            .forEach(w => {
                const hour = new Date(w.webhookTimestamp).getHours().toString().padStart(2, '0') + ':00';
                if(hourlyCounts[hour] !== undefined) hourlyCounts[hour]++;
            });
        
        const eventsOverTime = Object.entries(hourlyCounts).map(([time, count]) => ({ time, count }));

        // 2. Bitiş Nedeni Dağılımı
        const endReasonCounts = webhooks.reduce((acc, curr) => {
          acc[curr.endReason] = (acc[curr.endReason] || 0) + 1;
          return acc;
        }, {} as { [key: string]: number });
        const endReasonDistribution = Object.entries(endReasonCounts).map(([name, value]) => ({ name, value }));
        
        // 3. Pipeline Aşaması Dağılımı
        const pipelineStageCounts = webhooks.reduce((acc, curr) => {
            if (curr.pipeline) {
                try {
                    const stage = JSON.parse(curr.pipeline).stage;
                    if(stage) acc[stage] = (acc[stage] || 0) + 1;
                } catch (e) { /* ignore */ }
            }
            return acc;
        }, {} as { [key: string]: number });
        const pipelineStageDistribution = Object.entries(pipelineStageCounts).map(([name, count]) => ({ name, count }));

        // 4. Özet İstatistikler
        const totalEvents = webhooks.length;
        const totalDuration = webhooks.reduce((sum, w) => sum + w.duration, 0);
        const averageDuration = totalEvents > 0 ? totalDuration / totalEvents : 0;

        setAnalyticsData({
          eventsOverTime,
          endReasonDistribution,
          pipelineStageDistribution,
          totalEvents,
          averageDuration,
        });

      } catch (err) {
        console.error('Webhook analiz verileri yüklenirken hata oluştu:', err);
        setError('Veriler yüklenirken bir sorun oluştu.');
      } finally {
        setLoading(false);
      }
    };

    fetchWebhookData();
  }, [token]);

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}d ${remainingSeconds}sn`;
  };

  if (loading) {
    return <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px"><CircularProgress /></Box>;
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  if (!analyticsData) {
    return <Alert severity="info">Analiz edilecek webhook verisi bulunamadı.</Alert>;
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>CRM Webhook Analizi</Typography>
      
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card><CardContent><Typography variant="h5">{analyticsData.totalEvents}</Typography><Typography variant="body2" color="text.secondary">Toplam Webhook Olayı</Typography></CardContent></Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card><CardContent><Typography variant="h5">{formatDuration(analyticsData.averageDuration)}</Typography><Typography variant="body2" color="text.secondary">Ortalama Görüşme Süresi</Typography></CardContent></Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        <Grid item xs={12} lg={8}>
          <Card><CardContent>
            <Typography variant="h6" gutterBottom>Webhook Olayları (Son 24 Saat)</Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={analyticsData.eventsOverTime}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis allowDecimals={false} />
                <Tooltip />
                <Legend />
                <Line type="monotone" dataKey="count" name="Olay Sayısı" stroke={theme.palette.primary.main} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent></Card>
        </Grid>
        
        <Grid item xs={12} lg={4}>
          <Card><CardContent>
            <Typography variant="h6" gutterBottom>Görüşme Sonuçları</Typography>
            <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie data={analyticsData.endReasonDistribution} dataKey="value" nameKey="name" cx="50%" cy="50%" outerRadius={100} label>
                    {analyticsData.endReasonDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={END_REASON_COLORS[entry.name] || END_REASON_COLORS.default} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
            </ResponsiveContainer>
          </CardContent></Card>
        </Grid>

        <Grid item xs={12}>
          <Card><CardContent>
            <Typography variant="h6" gutterBottom>Pipeline Aşamalarına Göre Dağılım</Typography>
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={analyticsData.pipelineStageDistribution} layout="vertical">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis type="category" dataKey="name" width={150} />
                <Tooltip />
                <Legend />
                <Bar dataKey="count" name="Olay Sayısı" fill={theme.palette.secondary.main} />
              </BarChart>
            </ResponsiveContainer>
          </CardContent></Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CrmAnalyticsPage;
