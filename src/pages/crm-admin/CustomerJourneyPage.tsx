import React, { useState, useEffect, useMemo, useCallback, memo } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import analyticsService, { FunnelData as FunnelDataFromService } from '../../services/analyticsService';
import {
  Box,
  Typography,
  CircularProgress,
  Alert,
  Paper,
  Grid,
  Card,
  CardContent,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
} from '@mui/material';
import {
    CheckCircle as CheckCircleIcon,
    Cancel as CancelIcon,
    PhoneInTalk as PhoneInTalkIcon,
    People as PeopleIcon,
    RateReview as RateReviewIcon,
} from '@mui/icons-material';
import AdminFilterBar, { AdminFilterData } from '../../components/admin/AdminFilterBar';
import { formatReadableDate, parseToDate } from '../../utils/dateUtils';
import { useDebouncedCallback } from 'use-debounce';

type FunnelData = FunnelDataFromService & { uniqueId: string };

// Stat Card
const StatCard: React.FC<{ title: string; value: string | number; icon?: React.ReactNode }> = ({ title, value, icon }) => (
    <Card>
        <CardContent>
            <Box display="flex" alignItems="center" gap={2}>
                {icon}
                <Box>
                    <Typography variant="h5">{value}</Typography>
                    <Typography variant="body2" color="text.secondary">{title}</Typography>
                </Box>
            </Box>
        </CardContent>
    </Card>
);

// Detail Modal
const DetailModal: React.FC<{ open: boolean; onClose: () => void; selectedRow: FunnelData | null; }> = memo(({ open, onClose, selectedRow }) => {
    if (!open || !selectedRow) return null;

    return (
        <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
            <DialogTitle>Müşteri Yolculuğu Detayı</DialogTitle>
            <DialogContent dividers>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" color="primary">Müşteri Bilgileri</Typography>
                        <Typography><strong>Ad Soyad:</strong> {selectedRow.customerName}</Typography>
                        <Typography><strong>Marka:</strong> {selectedRow.sourceBrand}</Typography>
                        <Typography><strong>Kampanya:</strong> {selectedRow.sourceCampaign}</Typography>
                        <Typography><strong>Oluşturulma:</strong> {formatReadableDate(selectedRow.leadCreatedAt)}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" color="primary">Arama Bilgileri</Typography>
                        <Typography><strong>Arandı mı:</strong> {selectedRow.hasBeenCalled ? <Chip icon={<CheckCircleIcon />} label="Evet" color="success" size="small" /> : <Chip icon={<CancelIcon />} label="Hayır" color="error" size="small" />}</Typography>
                        {selectedRow.callTimestamp && <Typography><strong>Arama Zamanı:</strong> {formatReadableDate(selectedRow.callTimestamp)}</Typography>}
                        {selectedRow.callDuration && <Typography><strong>Görüşme Süresi:</strong> {selectedRow.callDuration} sn</Typography>}
                        {selectedRow.callOutcome && <Typography><strong>Görüşme Sonucu:</strong> {selectedRow.callOutcome}</Typography>}
                    </Grid>
                </Grid>
            </DialogContent>
            <DialogActions>
                <Button onClick={onClose} variant="contained">Kapat</Button>
            </DialogActions>
        </Dialog>
    );
});

const CustomerJourneyPage: React.FC = () => {
  const { token } = useAuth();
  const [allData, setAllData] = useState<FunnelData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedRow, setSelectedRow] = useState<FunnelData | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [filterData, setFilterData] = useState<AdminFilterData>({ 
      searchText: '', 
      dateRange: { startDate: '', endDate: '', startTime: '00:00', endTime: '23:59' },
      campaignName: '',
      brand: '',
  });

  const debouncedSetFilterData = useDebouncedCallback((data: AdminFilterData) => setFilterData(data), 300);

  useEffect(() => {
    const fetchData = async () => {
      if (!token) {
        setError("Yetkilendirme gerekli.");
        setLoading(false);
        return;
      }
      try {
        setLoading(true);
        setError(null);
        const data = await analyticsService.getComprehensiveDataFromJoin(token);
        const dataWithUniqueIds = data.map((row, index) => ({ ...row, uniqueId: `${row.id || 'no-id'}-${index}` }));
        setAllData(dataWithUniqueIds);
      } catch {
        setError("Müşteri yolculuğu verileri yüklenirken bir hata oluştu.");
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [token]);

  const campaignOptions = useMemo(() => {
        const campaigns = [...new Set(allData.map(item => item.sourceCampaign).filter((c): c is string => !!c))];
        return [{ value: 'all', label: 'Tüm Kampanyalar' }, ...campaigns.map(c => ({ value: c, label: c }))];
    }, [allData]);
  const brandOptions = useMemo(() => {
        const brands = [...new Set(allData.map(item => item.sourceBrand).filter((b): b is string => !!b))];
        return [{ value: 'all', label: 'Tüm Markalar' }, ...brands.map(b => ({ value: b, label: b }))];
    }, [allData]);

  const filteredData = useMemo(() => {
    return allData.filter(row => {
        const search = filterData.searchText.toLowerCase();
        const matchesSearch = !search || 
            row.customerName?.toLowerCase().includes(search) ||
            row.sourceCampaign?.toLowerCase().includes(search) ||
            row.sourceBrand?.toLowerCase().includes(search) ||
            row.callOutcome?.toLowerCase().includes(search);

        const matchesCampaign = !filterData.campaignName || row.sourceCampaign === filterData.campaignName;
        const matchesBrand = !filterData.brand || row.sourceBrand === filterData.brand;

        let matchesDate = true;
        const contactDate = parseToDate(row.leadCreatedAt);
        if (contactDate) {
            if (filterData.dateRange?.startDate) {
                const startDate = new Date(`${filterData.dateRange.startDate}T${filterData.dateRange.startTime || '00:00'}`);
                matchesDate = matchesDate && contactDate >= startDate;
            }
            if (filterData.dateRange?.endDate) {
                const endDate = new Date(`${filterData.dateRange.endDate}T${filterData.dateRange.endTime || '23:59'}`);
                matchesDate = matchesDate && contactDate <= endDate;
            }
        } else if (filterData.dateRange?.startDate || filterData.dateRange?.endDate) {
            matchesDate = false;
        }

        return matchesSearch && matchesCampaign && matchesBrand && matchesDate;
    });
  }, [allData, filterData]);

  const stats = useMemo(() => {
    const totalLeads = filteredData.length;
    if (totalLeads === 0) return { totalLeads: 0, calledLeads: 0, callRate: '0%', successRate: '0%' };
    const calledLeads = filteredData.filter(d => d.hasBeenCalled).length;
    const successfulCalls = filteredData.filter(d => d.callOutcome === 'completed').length;
    const callRate = ((calledLeads / totalLeads) * 100).toFixed(1) + '%';
    const successRate = (calledLeads > 0 ? (successfulCalls / calledLeads) * 100 : 0).toFixed(1) + '%';
    return { totalLeads, calledLeads, callRate, successRate };
  }, [filteredData]);

  const handleOpenModal = useCallback((row: FunnelData) => {
    setSelectedRow(row);
    setModalOpen(true);
  }, []);

  const handleCloseModal = useCallback(() => setModalOpen(false), []);

  if (loading) return <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px"><CircularProgress /></Box>;
  if (error) return <Alert severity="error">{error}</Alert>;

  return (
    <Box sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Typography variant="h4" gutterBottom>Müşteri Yolculuğu Analizi</Typography>
        
                <AdminFilterBar 
            filterData={filterData}
            setFilterData={debouncedSetFilterData} 
            additionalFilters={[
                { field: 'campaignName', label: 'Kampanya', type: 'select', options: campaignOptions },
                { field: 'brand', label: 'Marka', type: 'select', options: brandOptions },
            ]}
            showDateFilter={true}
            showActiveFilter={false}
        />

        <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={3}><StatCard title="Toplam Potansiyel Müşteri" value={stats.totalLeads} icon={<PeopleIcon color="primary" />} /></Grid>
            <Grid item xs={12} sm={6} md={3}><StatCard title="Aranan Müşteri Sayısı" value={stats.calledLeads} icon={<PhoneInTalkIcon color="success" />} /></Grid>
            <Grid item xs={12} sm={6} md={3}><StatCard title="Aranma Oranı" value={stats.callRate} icon={<RateReviewIcon color="action" />} /></Grid>
            <Grid item xs={12} sm={6} md={3}><StatCard title="Başarılı Görüşme Oranı" value={stats.successRate} icon={<CheckCircleIcon color="success" />} /></Grid>
        </Grid>

        <Paper sx={{ flex: 1, minHeight: 0, width: '100%', overflow: 'hidden' }}>
            <TableContainer sx={{ height: '100%' }}>
                <Table stickyHeader size="small">
                    <TableHead>
                        <TableRow>
                            <TableCell>Müşteri Adı</TableCell>
                            <TableCell>Kampanya</TableCell>
                            <TableCell>Marka</TableCell>
                            <TableCell>Oluşturulma T.</TableCell>
                            <TableCell align="center">Arandı mı?</TableCell>
                            <TableCell>Arama T.</TableCell>
                            <TableCell>Görüşme Sonucu</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {filteredData.map((row) => (
                            <TableRow key={row.uniqueId} hover onClick={() => handleOpenModal(row)} sx={{ cursor: 'pointer' }}>
                                <TableCell>{row.customerName}</TableCell>
                                <TableCell>{row.sourceCampaign}</TableCell>
                                <TableCell>{row.sourceBrand}</TableCell>
                                <TableCell>{formatReadableDate(row.leadCreatedAt)}</TableCell>
                                <TableCell align="center">{row.hasBeenCalled ? <CheckCircleIcon color="success" /> : <CancelIcon color="error" />}</TableCell>
                                <TableCell>{formatReadableDate(row.callTimestamp)}</TableCell>
                                <TableCell>{row.callOutcome}</TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
        </Paper>
        <DetailModal open={modalOpen} onClose={handleCloseModal} selectedRow={selectedRow} />
    </Box>
  );
};

export default CustomerJourneyPage;