import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  CircularProgress,
  Alert,
  Button,
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Person as PersonIcon,
  Campaign as CampaignIcon,
} from '@mui/icons-material';
import axios from 'axios';
import ExportButton from '../../components/admin/ExportButton';
import AdminFilterBar, { AdminFilterData, SelectOption } from '../../components/admin/AdminFilterBar';
import { formatReadableDate, parseToDate } from '../../utils/dateUtils'; // parseToDate import et

interface Contact {
  uuid: string;
  name: string;
  surname: string;
  email: string;
  phoneNumber: string;
  countryCode: string;
  status: string;
  campaignName?: string;
  brandName?: string;
  createdAt: string;
  contactPhone?: string;
  contactEmail?: string;
  campaignId?: number;
  formToCampaignCreatedAt?: string;
  customerToCampaignCreatedAt?: string;
}

const CrmContactsPage: React.FC = () => {
  const { token } = useAuth();
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [campaignOptions, setCampaignOptions] = useState<SelectOption[]>([]);
  const [brandOptions, setBrandOptions] = useState<SelectOption[]>([]);
  const [filterData, setFilterData] = useState<AdminFilterData>({
    searchText: '',
    isActive: 'all',
    campaignName: '',
    brandName: '',
    dateRange: { startDate: '', endDate: '', startTime: '00:00', endTime: '23:59' },
  });

  const handleFilter = () => {
    setPage(0);
  };

  useEffect(() => {
    fetchContacts();
  }, []);

  const fetchContacts = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!token) {
        setError('Authentication required. Please login first.');
        return;
      }

      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      };

      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/crm-admin/contacts', { headers });

      // Response format kontrolü
      const contactsData = response.data || [];
      const contactsArray = Array.isArray(contactsData) ? contactsData : (contactsData.content || contactsData.data || []);

      setContacts(contactsArray);

      const uniqueCampaigns = [...new Set(contactsArray.map((c: Contact) => c.campaignName).filter(Boolean))] as string[];
      const uniqueBrands = [...new Set(contactsArray.map((c: Contact) => c.brandName).filter(Boolean))] as string[];

      setCampaignOptions([
        { value: '', label: 'Tüm Kampanyalar' },
        ...uniqueCampaigns.map(name => ({ value: name, label: name }))
      ]);
      setBrandOptions([
        { value: '', label: 'Tüm Markalar' },
        ...uniqueBrands.map(name => ({ value: name, label: name }))
      ]);
    } catch (err: any) {
      setError('Contact verileri yüklenirken bir hata oluştu.');
      
      // Mock data for development
      setContacts([
        {
          uuid: '1',
          name: 'Ahmet',
          surname: 'Yılmaz',
          email: '<EMAIL>',
          phoneNumber: '5551234567',
          countryCode: '+90',
          status: 'Active',
          campaignName: 'Yaz Kampanyası',
          brandName: 'Test Marka',
          createdAt: '2024-01-20T10:30:00Z',
          contactPhone: '5551234567',
          contactEmail: '<EMAIL>',
          campaignId: 1,
        },
        {
          uuid: '2',
          name: 'Fatma',
          surname: 'Demir',
          email: '<EMAIL>',
          phoneNumber: '5559876543',
          countryCode: '+90',
          status: 'Active',
          campaignName: 'Kış Kampanyası',
          brandName: 'Test Marka 2',
          createdAt: '2024-01-19T15:20:00Z',
          contactPhone: '5559876543',
          contactEmail: '<EMAIL>',
          campaignId: 2,
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const filteredContacts = contacts.filter(contact => {
    // Arama metni
    const search = filterData.searchText.toLowerCase();
    const matchesSearch =
      (!search ||
        (contact.name && contact.name.toLowerCase().includes(search)) ||
        (contact.surname && contact.surname.toLowerCase().includes(search)) ||
        (contact.email && contact.email.toLowerCase().includes(search)) ||
        (contact.phoneNumber && contact.phoneNumber.includes(search)));
    // Durum
    const matchesStatus =
      filterData.isActive === 'all' ||
      (filterData.isActive === 'active' && contact.status === 'Active') ||
      (filterData.isActive === 'inactive' && contact.status !== 'Active');
    // Kampanya adı
    const matchesCampaign =
      !filterData.campaignName || contact.campaignName === filterData.campaignName;
    // Marka adı
    const matchesBrand =
      !filterData.brandName || contact.brandName === filterData.brandName;
    // Tarih aralığı
    let matchesDate = true;
    const contactDate = parseToDate(contact.customerToCampaignCreatedAt || contact.formToCampaignCreatedAt || contact.createdAt);

    if (contactDate) {
      if (filterData.dateRange?.startDate) {
        const dateParts = filterData.dateRange.startDate.split('-').map(Number);
        const timeParts = (filterData.dateRange.startTime || '00:00').split(':').map(Number);
        const startDate = new Date(dateParts[0], dateParts[1] - 1, dateParts[2], timeParts[0], timeParts[1]);
        matchesDate = matchesDate && contactDate >= startDate;
      }
      if (filterData.dateRange?.endDate) {
        const dateParts = filterData.dateRange.endDate.split('-').map(Number);
        const timeParts = (filterData.dateRange.endTime || '23:59').split(':').map(Number);
        const endDate = new Date(dateParts[0], dateParts[1] - 1, dateParts[2], timeParts[0], timeParts[1]);
        matchesDate = matchesDate && contactDate <= endDate;
      }
    } else if (filterData.dateRange?.startDate || filterData.dateRange?.endDate) {
      matchesDate = false; // Tarih yoksa ve filtre varsa eşleşme
    }

    return matchesSearch && matchesStatus && matchesCampaign && matchesBrand && matchesDate;
  });

  const paginatedContacts = filteredContacts.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleViewDetails = async (contact: Contact) => {
    // setSelectedContact(contact); // Removed
    // setDetailDialog(true); // Removed

    // Contact'ın telefon numarasına göre webhook'ları getir
    try {
      const response = await axios.get(`https://360avantajli.com/api/Campaign_Service/crm-admin/webhook-events/by-phone/${contact.phoneNumber}`);
      // setContactWebhooks(response.data || []); // Removed
    } catch (error) {
      console.error('Webhook fetch error:', error);
      // setContactWebhooks([]); // Removed
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <AdminFilterBar
        filterData={filterData}
        setFilterData={setFilterData}
        onFilter={handleFilter}
        showActiveFilter={true}
        showDateFilter={true}
        additionalFilters={[
          { field: 'campaignName', label: 'Kampanya Adı', type: 'select', options: campaignOptions },
          { field: 'brandName', label: 'Marka Adı', type: 'select', options: brandOptions },
        ]}
        renderExportButton={() => (
          <ExportButton
            data={filteredContacts}
            filename="CRM_Contacts"
            headCells={[
              { id: 'name', label: 'Ad' },
              { id: 'surname', label: 'Soyad' },
              { id: 'email', label: 'Email' },
              { id: 'phoneNumber', label: 'Telefon' },
              { id: 'countryCode', label: 'Ülke Kodu' },
              { id: 'status', label: 'Durum' },
              { id: 'campaignName', label: 'Kampanya' },
              { id: 'brandName', label: 'Marka' },
              { id: 'createdAt', label: 'Oluşturulma' },
            ]}
          />
        )}
      />

      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {error} (Geliştirme verisi gösteriliyor)
        </Alert>
      )}

      {/* Contacts Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>İsim Soyisim</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Telefon</TableCell>
                <TableCell>Kampanya</TableCell>
                <TableCell>Marka</TableCell>
                <TableCell>Durum</TableCell>
                <TableCell>Oluşturulma</TableCell>
                <TableCell>İşlemler</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedContacts.map((contact) => (
                <TableRow key={contact.uuid}>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      <PersonIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      {contact.name} {contact.surname}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      {contact.email}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      <PhoneIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      {contact.countryCode} {contact.phoneNumber}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      <CampaignIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      {contact.campaignName || '-'}
                    </Box>
                  </TableCell>
                  <TableCell>{contact.brandName || '-'}</TableCell>
                  <TableCell>
                    <Chip
                      label={contact.status}
                      size="small"
                      color={contact.status === 'Active' ? 'success' : 'default'}
                    />
                  </TableCell>
                  <TableCell>{formatReadableDate(contact.customerToCampaignCreatedAt || contact.formToCampaignCreatedAt || contact.createdAt)}</TableCell>
                  <TableCell>
                    <Button
                      size="small"
                      startIcon={<VisibilityIcon />}
                      onClick={() => handleViewDetails(contact)}
                    >
                      Detay
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[10, 25, 50, 100]}
          component="div"
          count={filteredContacts.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Sayfa başına satır:"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} / ${count}`}
        />
      </Paper>

      {/* Contact Detail Dialog */}
      {/* Removed Contact Detail Dialog */}
    </Box>
  );
};

export default CrmContactsPage;
