import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  CircularProgress,
  Alert,
  LinearProgress,
} from '@mui/material';
import {
  Storage as StorageIcon,
  TableChart as TableIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import crmService from '../../services/crmService';
import { formatReadableDate } from '../../utils/dateUtils';

interface TableInfo {
  name: string;
  recordCount: number;
  lastUpdated: string;
  size: string;
}

interface DatabaseInfo {
  tables: TableInfo[];
  totalSize: string;
  connectionStatus: 'connected' | 'disconnected' | 'error';
}

const CrmDatabasePage: React.FC = () => {
  const [databaseInfo, setDatabaseInfo] = useState<DatabaseInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDatabaseInfo();
  }, []);

  const fetchDatabaseInfo = async () => {
    try {
      setLoading(true);
      setError(null);
      const info = await crmService.getDatabaseInfo();
      setDatabaseInfo(info);
    } catch (err: any) {
      console.error('Error fetching database info:', err);
      setError('Database bilgileri yüklenirken bir hata oluştu.');
      
      // Mock data for development
      setDatabaseInfo({
        tables: [
          {
            name: 'customers',
            recordCount: 1250,
            lastUpdated: '2024-01-20T10:30:00Z',
            size: '2.5 MB',
          },
          {
            name: 'campaigns',
            recordCount: 45,
            lastUpdated: '2024-01-20T09:15:00Z',
            size: '512 KB',
          },
          {
            name: 'contacts',
            recordCount: 3200,
            lastUpdated: '2024-01-20T08:45:00Z',
            size: '5.1 MB',
          },
          {
            name: 'calls',
            recordCount: 890,
            lastUpdated: '2024-01-20T07:30:00Z',
            size: '1.8 MB',
          },
          {
            name: 'call_logs',
            recordCount: 2150,
            lastUpdated: '2024-01-20T06:15:00Z',
            size: '4.3 MB',
          },
        ],
        totalSize: '14.2 MB',
        connectionStatus: 'connected',
      });
    } finally {
      setLoading(false);
    }
  };

  const getConnectionStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'success';
      case 'disconnected':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const getConnectionStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircleIcon color="success" fontSize="small" />;
      case 'disconnected':
        return <WarningIcon color="warning" fontSize="small" />;
      case 'error':
        return <ErrorIcon color="error" fontSize="small" />;
      default:
        return <StorageIcon fontSize="small" />;
    }
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('tr-TR').format(num);
  };

  const calculatePercentage = (tableSize: string, totalSize: string) => {
    const tableSizeNum = parseFloat(tableSize.replace(/[^\d.]/g, ''));
    const totalSizeNum = parseFloat(totalSize.replace(/[^\d.]/g, ''));
    return Math.round((tableSizeNum / totalSizeNum) * 100);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        CRM Database
      </Typography>
      <Typography variant="subtitle1" color="text.secondary" gutterBottom>
        Database tabloları ve veri istatistikleri
      </Typography>

      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {error} (Geliştirme verisi gösteriliyor)
        </Alert>
      )}

      {databaseInfo && (
        <>
          {/* Database Özeti */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center">
                    <StorageIcon color="primary" sx={{ mr: 2, fontSize: 40 }} />
                    <Box>
                      <Typography variant="h4">{databaseInfo.totalSize}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Toplam Boyut
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center">
                    <TableIcon color="info" sx={{ mr: 2, fontSize: 40 }} />
                    <Box>
                      <Typography variant="h4">{databaseInfo.tables.length}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Toplam Tablo
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center">
                    {getConnectionStatusIcon(databaseInfo.connectionStatus)}
                    <Box sx={{ ml: 2 }}>
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography variant="h6">Bağlantı Durumu</Typography>
                        <Chip
                          label={databaseInfo.connectionStatus}
                          size="small"
                          color={getConnectionStatusColor(databaseInfo.connectionStatus) as any}
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        Database Bağlantısı
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Tablo Detayları */}
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Tablo Detayları
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Tablo Adı</TableCell>
                    <TableCell align="right">Kayıt Sayısı</TableCell>
                    <TableCell align="right">Boyut</TableCell>
                    <TableCell align="center">Boyut Dağılımı</TableCell>
                    <TableCell>Son Güncelleme</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {databaseInfo.tables.map((table) => (
                    <TableRow key={table.name}>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          <TableIcon color="action" sx={{ mr: 1 }} />
                          <Typography variant="subtitle2">
                            {table.name}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2">
                          {formatNumber(table.recordCount)}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" fontWeight="medium">
                          {table.size}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Box sx={{ width: '100%', maxWidth: 200 }}>
                          <LinearProgress
                            variant="determinate"
                            value={calculatePercentage(table.size, databaseInfo.totalSize)}
                            sx={{ height: 8, borderRadius: 4 }}
                          />
                          <Typography variant="caption" color="text.secondary">
                            {calculatePercentage(table.size, databaseInfo.totalSize)}%
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="caption" color="text.secondary">
                          {formatReadableDate(table.lastUpdated)}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>

          {/* Toplam İstatistikler */}
          <Grid container spacing={3} sx={{ mt: 2 }}>
            <Grid item xs={12}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Toplam İstatistikler
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box textAlign="center">
                      <Typography variant="h4" color="primary">
                        {formatNumber(databaseInfo.tables.reduce((sum, table) => sum + table.recordCount, 0))}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Toplam Kayıt
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={3}>
                    <Box textAlign="center">
                      <Typography variant="h4" color="success.main">
                        {Math.max(...databaseInfo.tables.map(t => t.recordCount)).toLocaleString('tr-TR')}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        En Büyük Tablo
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={3}>
                    <Box textAlign="center">
                      <Typography variant="h4" color="info.main">
                        {Math.round(databaseInfo.tables.reduce((sum, table) => sum + table.recordCount, 0) / databaseInfo.tables.length).toLocaleString('tr-TR')}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Ortalama Kayıt/Tablo
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={3}>
                    <Box textAlign="center">
                      <Typography variant="h4" color="warning.main">
                        {databaseInfo.totalSize}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Toplam Veri Boyutu
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Paper>
            </Grid>
          </Grid>
        </>
      )}
    </Box>
  );
};

export default CrmDatabasePage;
