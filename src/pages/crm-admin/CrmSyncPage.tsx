import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
} from '@mui/material';
import {
  Sync as SyncIcon,
  PlayArrow as PlayIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Schedule as ScheduleIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import crmService from '../../services/crmService';

interface SyncLog {
  id: string;
  endpointId: string;
  timestamp: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  dataCount?: number;
  duration?: number;
}

const CrmSyncPage: React.FC = () => {
  const [syncLogs, setSyncLogs] = useState<SyncLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [syncDialog, setSyncDialog] = useState<{
    open: boolean;
    endpointId?: string;
    endpointName?: string;
  }>({ open: false });

  useEffect(() => {
    fetchSyncLogs();
  }, []);

  const fetchSyncLogs = async () => {
    try {
      setLoading(true);
      setError(null);
      const logs = await crmService.getSyncLogs(50);
      setSyncLogs(logs);
    } catch (err: any) {
      console.error('Error fetching sync logs:', err);
      setError('Sync logları yüklenirken bir hata oluştu.');
      
      // Mock data for development
      setSyncLogs([
        {
          id: '1',
          endpointId: '1',
          timestamp: '2024-01-20T10:30:00Z',
          status: 'success',
          message: 'Customer data synchronized successfully',
          dataCount: 125,
          duration: 2500,
        },
        {
          id: '2',
          endpointId: '2',
          timestamp: '2024-01-20T09:15:00Z',
          status: 'success',
          message: 'Campaign data retrieved successfully',
          dataCount: 45,
          duration: 1800,
        },
        {
          id: '3',
          endpointId: '3',
          timestamp: '2024-01-20T08:45:00Z',
          status: 'error',
          message: 'Connection timeout while updating leads',
          dataCount: 0,
          duration: 30000,
        },
        {
          id: '4',
          endpointId: '1',
          timestamp: '2024-01-20T07:30:00Z',
          status: 'warning',
          message: 'Partial sync completed - some records skipped',
          dataCount: 98,
          duration: 3200,
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleManualSync = async (endpointId?: string) => {
    try {
      setSyncing(endpointId || 'all');
      const result = await crmService.triggerSync(endpointId);
      
      if (result.success) {
        alert('Senkronizasyon başarıyla başlatıldı!');
        // Refresh logs after a short delay
        setTimeout(() => {
          fetchSyncLogs();
        }, 2000);
      } else {
        alert(`Senkronizasyon başlatılamadı: ${result.message}`);
      }
    } catch (err: any) {
      alert('Senkronizasyon başlatılırken bir hata oluştu.');
    } finally {
      setSyncing(null);
      setSyncDialog({ open: false });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon color="success" fontSize="small" />;
      case 'error':
        return <ErrorIcon color="error" fontSize="small" />;
      case 'warning':
        return <WarningIcon color="warning" fontSize="small" />;
      default:
        return <ScheduleIcon fontSize="small" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('tr-TR');
  };

  const formatDuration = (duration: number) => {
    if (duration < 1000) return `${duration}ms`;
    return `${(duration / 1000).toFixed(1)}s`;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          CRM Senkronizasyon
        </Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchSyncLogs}
          >
            Yenile
          </Button>
          <Button
            variant="contained"
            startIcon={syncing === 'all' ? <CircularProgress size={16} /> : <SyncIcon />}
            onClick={() => setSyncDialog({ open: true })}
            disabled={syncing !== null}
          >
            Manuel Sync
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {error} (Geliştirme verisi gösteriliyor)
        </Alert>
      )}

      {/* Sync Controls */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h6">Customer Sync</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Müşteri verilerini senkronize et
                  </Typography>
                </Box>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={syncing === '1' ? <CircularProgress size={16} /> : <PlayIcon />}
                  onClick={() => setSyncDialog({ open: true, endpointId: '1', endpointName: 'Customer Sync' })}
                  disabled={syncing !== null}
                >
                  Başlat
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h6">Campaign Sync</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Kampanya verilerini senkronize et
                  </Typography>
                </Box>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={syncing === '2' ? <CircularProgress size={16} /> : <PlayIcon />}
                  onClick={() => setSyncDialog({ open: true, endpointId: '2', endpointName: 'Campaign Sync' })}
                  disabled={syncing !== null}
                >
                  Başlat
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h6">Lead Sync</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Lead verilerini senkronize et
                  </Typography>
                </Box>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={syncing === '3' ? <CircularProgress size={16} /> : <PlayIcon />}
                  onClick={() => setSyncDialog({ open: true, endpointId: '3', endpointName: 'Lead Sync' })}
                  disabled={syncing !== null}
                >
                  Başlat
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Sync Logs */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Senkronizasyon Logları
        </Typography>
        <List>
          {syncLogs.map((log, index) => (
            <React.Fragment key={log.id}>
              <ListItem>
                <ListItemIcon>
                  {getStatusIcon(log.status)}
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box display="flex" alignItems="center" gap={2}>
                      <Typography variant="subtitle2">
                        {log.message}
                      </Typography>
                      <Chip
                        label={log.status}
                        size="small"
                        color={getStatusColor(log.status) as any}
                      />
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        {formatDate(log.timestamp)}
                      </Typography>
                      {log.dataCount !== undefined && (
                        <Typography variant="caption" color="text.secondary" sx={{ ml: 2 }}>
                          {log.dataCount} kayıt
                        </Typography>
                      )}
                      {log.duration && (
                        <Typography variant="caption" color="text.secondary" sx={{ ml: 2 }}>
                          {formatDuration(log.duration)}
                        </Typography>
                      )}
                    </Box>
                  }
                />
              </ListItem>
              {index < syncLogs.length - 1 && <Box sx={{ borderBottom: 1, borderColor: 'divider' }} />}
            </React.Fragment>
          ))}
        </List>
      </Paper>

      {/* Sync Confirmation Dialog */}
      <Dialog open={syncDialog.open} onClose={() => setSyncDialog({ open: false })}>
        <DialogTitle>
          Senkronizasyon Başlat
        </DialogTitle>
        <DialogContent>
          <Typography>
            {syncDialog.endpointName 
              ? `${syncDialog.endpointName} senkronizasyonunu başlatmak istediğinizden emin misiniz?`
              : 'Tüm endpoint\'ler için senkronizasyonu başlatmak istediğinizden emin misiniz?'
            }
          </Typography>
          {syncing && (
            <Box sx={{ mt: 2 }}>
              <LinearProgress />
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                Senkronizasyon başlatılıyor...
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSyncDialog({ open: false })} disabled={syncing !== null}>
            İptal
          </Button>
          <Button 
            onClick={() => handleManualSync(syncDialog.endpointId)} 
            variant="contained"
            disabled={syncing !== null}
          >
            Başlat
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CrmSyncPage;
