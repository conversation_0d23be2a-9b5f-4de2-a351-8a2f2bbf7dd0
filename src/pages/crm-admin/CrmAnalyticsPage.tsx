import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../../contexts/AuthContext';
import {
  Box,
  Grid,
  Typography,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  colors,
} from '@mui/material';
import {

  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
} from 'recharts';

// Veri Modelleri
interface WebhookEvent {
  uuid: string;
  event: string;
  endReason: string;
  webhookTimestamp: string;
  duration: number;
  pipeline?: string;
}

interface AnalyticsData {
  eventsOverTime: { time: string; count: number }[];
  endReasonDistribution: { name: string; value: number }[];
  pipelineStageDistribution: { name: string; count: number }[];
  totalEvents: number;
  averageDuration: number; // saniye cinsinden
  totalDuration: number; // saniye cinsinden
}

// Renk paleti
const PIE_CHART_COLORS = [
  colors.blue[500],
  colors.green[500],
  colors.red[500],
  colors.orange[500],
  colors.purple[500],
  colors.yellow[700],
  colors.teal[500],
  colors.pink[500],
  colors.indigo[500],
  colors.cyan[500],
];

const CrmAnalyticsPage: React.FC = () => {
  const { token } = useAuth();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchWebhookData = async () => {
      if (!token) {
        setError("Yetkilendirme gerekli. Lütfen giriş yapın.");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        
        const headers = { 'Authorization': `Bearer ${token}` };
        const response = await axios.get('https://360avantajli.com/api/Campaign_Service/crm-admin/webhook-events', { headers });
        
                const webhooks: WebhookEvent[] = Array.isArray(response.data) ? response.data : [];

        // 1. Zamana Göre Olaylar (son 24 saat, saatlik)
        const now = new Date();
        const last24Hours: { time: string; count: number }[] = [];
        const hourlyCountsMap = new Map<string, number>();

        for (let i = 23; i >= 0; i--) {
            const date = new Date(now.getTime() - i * 60 * 60 * 1000);
            const hourKey = `${date.getUTCHours().toString().padStart(2, '0')}:00`;
            last24Hours.push({ time: hourKey, count: 0 });
            hourlyCountsMap.set(hourKey, 0);
        }

        webhooks.forEach(w => {
            if (typeof w.webhookTimestamp === 'string') {
                const eventDate = new Date(w.webhookTimestamp.replace(' ', 'T') + 'Z');
                if (!isNaN(eventDate.getTime()) && eventDate.getTime() > now.getTime() - 24 * 60 * 60 * 1000) {
                    const hourKey = `${eventDate.getUTCHours().toString().padStart(2, '0')}:00`;
                    if (hourlyCountsMap.has(hourKey)) {
                        hourlyCountsMap.set(hourKey, hourlyCountsMap.get(hourKey)! + 1);
                    }
                }
            }
        });

        const eventsOverTime = last24Hours.map(hourData => ({
            ...hourData,
            count: hourlyCountsMap.get(hourData.time) || 0,
        }));

        // 2. Bitiş Nedeni Dağılımı
        const endReasonCounts = webhooks.reduce((acc, curr) => {
          let reason = curr.endReason || 'unknown';
          if (reason.startsWith('pipeline-error')) {
            reason = 'pipeline-error';
          }
          acc[reason] = (acc[reason] || 0) + 1;
          return acc;
        }, {} as { [key: string]: number });
        const endReasonDistribution = Object.entries(endReasonCounts).map(([name, value]) => ({ name, value }));
        
        // 3. Pipeline Aşaması Dağılımı
        const pipelineStageCounts = webhooks.reduce((acc, curr) => {
            if (curr.pipeline) {
                try {
                    const stage = JSON.parse(curr.pipeline).stage;
                    if(stage) acc[stage] = (acc[stage] || 0) + 1;
                                } catch {
                    /* Hatalı JSON'u yoksay */
                }
            }
            return acc;
        }, {} as { [key: string]: number });
        const pipelineStageDistribution = Object.entries(pipelineStageCounts).map(([name, count]) => ({ name, count }));

        // 4. Özet İstatistikler
        const totalEvents = webhooks.length;
        const totalDurationInMs = webhooks.reduce((sum, w) => sum + (w.duration || 0), 0);
        const averageDurationInMs = totalEvents > 0 ? totalDurationInMs / totalEvents : 0;

        const finalAnalyticsData = {
          eventsOverTime,
          endReasonDistribution,
          pipelineStageDistribution,
          totalEvents,
          averageDuration: averageDurationInMs / 1000, // saniyeye çevir
          totalDuration: totalDurationInMs / 1000, // saniyeye çevir
        };

        

        setAnalyticsData(finalAnalyticsData);

      } catch {
        setError('Veriler yüklenirken bir sorun oluştu.');
      } finally {
        setLoading(false);
      }
    };

    fetchWebhookData();
  }, [token]);

  const formatDuration = (seconds: number) => {
    if (isNaN(seconds) || seconds < 0) return "0sn";

    const days = Math.floor(seconds / (3600 * 24));
    seconds %= 3600 * 24;
    const hours = Math.floor(seconds / 3600);
    seconds %= 3600;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);

    let result = "";
    if (days > 0) result += `${days}g `;
    if (hours > 0) result += `${hours}s `;
    if (minutes > 0) result += `${minutes}d `;
    if (remainingSeconds > 0 || result === "") result += `${remainingSeconds}sn`;
    
    return result.trim();
  };

  if (loading) {
    return <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px"><CircularProgress /></Box>;
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  if (!analyticsData) {
    return <Alert severity="info">Analiz edilecek webhook verisi bulunamadı.</Alert>;
  }
  
    const CustomPieLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: { cx: number; cy: number; midAngle: number; innerRadius: number; outerRadius: number; percent: number; }) => {
      const RADIAN = Math.PI / 180;
      const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
      const x = cx + radius * Math.cos(-midAngle * RADIAN);
      const y = cy + radius * Math.sin(-midAngle * RADIAN);

      return (
        <text x={x} y={y} fill="white" textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central">
          {`${(percent * 100).toFixed(0)}%`}
        </text>
      );
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>CRM Webhook Analizi</Typography>
      
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card><CardContent><Typography variant="h5">{analyticsData.totalEvents}</Typography><Typography variant="body2" color="text.secondary">Toplam Webhook Olayı</Typography></CardContent></Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card><CardContent><Typography variant="h5">{formatDuration(analyticsData.totalDuration)}</Typography><Typography variant="body2" color="text.secondary">Toplam Görüşme Süresi</Typography></CardContent></Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card><CardContent><Typography variant="h5">{formatDuration(analyticsData.averageDuration)}</Typography><Typography variant="body2" color="text.secondary">Ortalama Görüşme Süresi</Typography></CardContent></Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        <Grid item xs={12} lg={8}>
          <Card><CardContent>
            <Typography variant="h6" gutterBottom>Çağrı Sayısı (Son 24 Saat)</Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={analyticsData.eventsOverTime}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis allowDecimals={false} />
                <Tooltip />
                <Legend />
                <Line type="monotone" dataKey="count" name="Çağrı Sayısı" stroke={colors.blue[500]} strokeWidth={2} dot={{ r: 4 }} activeDot={{ r: 8 }} />          </LineChart>
            </ResponsiveContainer>
          </CardContent></Card>
        </Grid>
        
        <Grid item xs={12} lg={4}>
          <Card><CardContent>
            <Typography variant="h6" gutterBottom>Görüşme Sonuçları</Typography>
            <ResponsiveContainer width="100%" height={400}>
              <PieChart margin={{ top: 0, right: 20, bottom: 100, left: 20 }}>
                <Pie
                  data={analyticsData.endReasonDistribution}
                  dataKey="value"
                  nameKey="name"
                  cx="50%"
                  cy="45%"
                  outerRadius={80}
                  labelLine={false}
                  label={CustomPieLabel}
                >
                  {analyticsData.endReasonDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={PIE_CHART_COLORS[index % PIE_CHART_COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value: number, name: string) => [value, name]} />
                <Legend
                  verticalAlign="bottom"
                  wrapperStyle={{ bottom: 30, left: 20, right: 20 }}
                />
              </PieChart>
            </ResponsiveContainer>
          </CardContent></Card>
        </Grid>


      </Grid>
    </Box>
  );
};

export default CrmAnalyticsPage;
