import React, { useState, useEffect, useMemo } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import {
  Box,
  Typography,
  Paper,
  Chip,
  CircularProgress,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  CheckCircleOutline as CheckCircleOutlineIcon,
  ErrorOutline as ErrorOutlineIcon,
  HourglassEmpty as HourglassEmptyIcon,
  ExpandMore as ExpandMoreIcon,
} from '@mui/icons-material';
import axios from 'axios';
import { useLocation } from 'react-router-dom';
import ExportButton from '../../components/admin/ExportButton';
import AdminFilterBar, { AdminFilterData } from '../../components/admin/AdminFilterBar';
import { formatReadableDate } from '../../utils/dateUtils';
import { useDebouncedCallback } from 'use-debounce';

interface WebhookEventFromService {
  uuid: string;
  event: string;
  callId: string;
  number: string;
  duration: number;
  analysis: string;
  conversation: string;
  endReason: string;
  webhookTimestamp: string;
  createdAt: string;
  pipeline?: string;
  fields?: string;
}

type WebhookEvent = WebhookEventFromService & { uniqueId: string };

// Stat Card
const StatCard: React.FC<{ title: string; value: string | number; icon?: React.ReactNode }> = ({ title, value, icon }) => (
    <Card>
        <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                {icon}
                <Box>
                    <Typography variant="h6">{value}</Typography>
                    <Typography color="text.secondary">{title}</Typography>
                </Box>
            </Box>
        </CardContent>
    </Card>
);

const CrmWebhooksPage: React.FC = () => {
  const { token } = useAuth();
  const location = useLocation();
  const [allWebhooks, setAllWebhooks] = useState<WebhookEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedWebhook, setSelectedWebhook] = useState<WebhookEvent | null>(null);
  const [detailDialog, setDetailDialog] = useState(false);
  const [filterData, setFilterData] = useState<AdminFilterData>({
    searchText: '',
    event: 'all',
    endReason: 'all',
    pipeline: 'all',
    dateRange: { startDate: null, endDate: null, startTime: null, endTime: null },
  });

  const debouncedSetFilterData = useDebouncedCallback(setFilterData, 300);

  useEffect(() => {
    fetchWebhooks();
  }, [token]);

  useEffect(() => {
    if (allWebhooks.length > 0) {
      const params = new URLSearchParams(location.search);
      const webhookId = params.get('id');
      if (webhookId) {
        const webhook = allWebhooks.find(w => w.uuid === webhookId);
        if (webhook) {
          handleViewDetails(webhook);
        }
      }
    }
  }, [allWebhooks, location.search]);

  const fetchWebhooks = async () => {
    setLoading(true);
    setError(null);
    if (!token) {
      setError('Authentication required.');
      setLoading(false);
      return;
    }

    try {
      const headers = { 'Authorization': `Bearer ${token}` };
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/crm-admin/webhook-events', { headers });
      const webhooksData = response.data?.content || response.data || [];
      if (Array.isArray(webhooksData)) {
        setAllWebhooks(webhooksData.map((w: WebhookEventFromService) => ({ ...w, uniqueId: w.uuid })));
      } else {
        setError('Received data is not in the expected format.');
      }
    } catch (err) {
        if (axios.isAxiosError(err)) {
            setError(`Webhook verileri yüklenirken bir hata oluştu: ${err.message}`);
          } else {
            setError('Webhook verileri yüklenirken bilinmeyen bir hata oluştu.');
          }
    } finally {
      setLoading(false);
    }
  };

  const filteredWebhooks = useMemo(() => {
    return allWebhooks.filter(webhook => {
      const search = filterData.searchText.toLowerCase();
      const matchesSearch =
        !search ||
        Object.values(webhook).some(value => 
            String(value).toLowerCase().includes(search)
        );

      const matchesEvent = filterData.event === 'all' || webhook.event === filterData.event;
      const matchesEndReason = filterData.endReason === 'all' || webhook.endReason === filterData.endReason;
      
      const matchesPipeline = filterData.pipeline === 'all' || (
        webhook.pipeline &&
        (() => {
          try {
            return JSON.parse(webhook.pipeline).stage === filterData.pipeline;
          } catch { return false; }
        })()
      );

      let matchesDate = true;
      if (filterData.dateRange?.startDate) {
        matchesDate = matchesDate && new Date(webhook.webhookTimestamp) >= new Date(filterData.dateRange.startDate);
      }
      if (filterData.dateRange?.endDate) {
        matchesDate = matchesDate && new Date(webhook.webhookTimestamp) <= new Date(filterData.dateRange.endDate);
      }

      return matchesSearch && matchesEvent && matchesEndReason && matchesDate && matchesPipeline;
    });
  }, [allWebhooks, filterData]);

  const eventOptions = useMemo(() => {
    const events = [...new Set(allWebhooks.map(w => w.event).filter(Boolean))];
    return [{ value: 'all', label: 'Tüm Eventler' }, ...events.map(e => ({ value: e, label: e }))];
  }, [allWebhooks]);

  const endReasonOptions = useMemo(() => {
    const reasons = [...new Set(allWebhooks.map(w => w.endReason).filter(Boolean))];
    return [{ value: 'all', label: 'Tüm Nedenler' }, ...reasons.map(r => ({ value: r, label: r }))];
  }, [allWebhooks]);

  const pipelineOptions = useMemo(() => {
    const pipelines = new Set<string>();
    allWebhooks.forEach(webhook => {
      if (webhook.pipeline) {
        try {
          const parsed = JSON.parse(webhook.pipeline);
          if (parsed.stage) pipelines.add(parsed.stage);
        } catch {
          // Silently ignore JSON parsing errors
        }
      }
    });
    return [{ value: 'all', label: 'Tüm Aşamalar' }, ...Array.from(pipelines).map(p => ({ value: p, label: p }))];
  }, [allWebhooks]);

  const stats = useMemo(() => {
    const total = allWebhooks.length;
    const completed = allWebhooks.filter(w => ['completed', 'assistant-ended-call', 'customer-ended-call'].includes(w.endReason)).length;
    const errors = allWebhooks.filter(w => w.endReason === 'system_error').length;
    return { total, completed, errors };
  }, [allWebhooks]);

  const handleViewDetails = (webhook: WebhookEvent) => {
    setSelectedWebhook(webhook);
    setDetailDialog(true);
  };

  const formatDuration = (ms: number) => {
    if (isNaN(ms) || ms < 0) return "00:00";
    const totalSeconds = Math.floor(ms / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  type ChipColor = 'success' | 'warning' | 'error' | 'default' | 'info' | 'primary';

  const getEndReasonColor = (endReason: string): ChipColor => {
    switch (endReason) {
      case 'completed':
      case 'assistant-ended-call':
      case 'customer-ended-call': return 'success';
      case 'customer_hangup':
      case 'silence-timed-out': return 'warning';
      case 'system_error': return 'error';
      default: return 'default';
    }
  };

  if (loading) {
    return <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px"><CircularProgress /></Box>;
  }

  return (
    <Box sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column', gap: 2 }}>
      <Typography variant="h4" gutterBottom>Webhook Akışı</Typography>
      
      <AdminFilterBar
        filterData={filterData}
        setFilterData={debouncedSetFilterData}
        showDateFilter={true}
        additionalFilters={[
          { field: 'event', label: 'Event', type: 'select', options: eventOptions },
          { field: 'endReason', label: 'Bitiş Nedeni', type: 'select', options: endReasonOptions },
          { field: 'pipeline', label: 'Pipeline Aşaması', type: 'select', options: pipelineOptions },
        ]}
        renderExportButton={() => (
          <ExportButton
            data={filteredWebhooks}
            filename="CRM_Webhooks"
            headCells={Object.keys(filteredWebhooks[0] || {}).map(k => ({ id: k, label: k }))}
          />
        )}
      />

      {error && <Alert severity="warning" sx={{ mb: 2 }}>{error}</Alert>}

      <Grid container spacing={3} sx={{ mb: 2 }}>
        <Grid item xs={12} sm={4}><StatCard title="Toplam Webhook" value={stats.total} icon={<HourglassEmptyIcon fontSize="large" color="primary" />} /></Grid>
        <Grid item xs={12} sm={4}><StatCard title="Başarılı Olaylar" value={stats.completed} icon={<CheckCircleOutlineIcon fontSize="large" color="success" />} /></Grid>
        <Grid item xs={12} sm={4}><StatCard title="Sistem Hataları" value={stats.errors} icon={<ErrorOutlineIcon fontSize="large" color="error" />} /></Grid>
      </Grid>

      <Paper sx={{ flex: 1, overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>
        <TableContainer sx={{ flex: 1, overflow: 'auto' }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell>Call ID</TableCell>
                <TableCell>Telefon</TableCell>
                <TableCell>Süre</TableCell>
                <TableCell>Bitiş Nedeni</TableCell>
                <TableCell>Analiz</TableCell>
                <TableCell>Webhook Zamanı</TableCell>
                <TableCell>İşlemler</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredWebhooks.map((webhook) => (
                <TableRow key={webhook.uniqueId} hover onClick={() => handleViewDetails(webhook)} sx={{ cursor: 'pointer' }}>
                  <TableCell>{webhook.callId}</TableCell>
                  <TableCell>{webhook.number}</TableCell>
                  <TableCell>{formatDuration(webhook.duration)}</TableCell>
                  <TableCell>
                    <Chip label={webhook.endReason} size="small" color={getEndReasonColor(webhook.endReason)} />
                  </TableCell>
                  <TableCell>
                    <Tooltip title={webhook.analysis} placement="top-start">
                      <Typography noWrap sx={{ maxWidth: 300 }}>{webhook.analysis}</Typography>
                    </Tooltip>
                  </TableCell>
                  <TableCell>{formatReadableDate(webhook.webhookTimestamp)}</TableCell>
                  <TableCell>
                    <Button size="small" startIcon={<VisibilityIcon />} onClick={(e) => { e.stopPropagation(); handleViewDetails(webhook); }}>
                      Detay
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      <Dialog open={detailDialog} onClose={() => setDetailDialog(false)} maxWidth="lg" fullWidth>
        <DialogTitle>Webhook Detayları - {selectedWebhook?.callId}</DialogTitle>
        <DialogContent>
          {selectedWebhook && (
            <Grid container spacing={3} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>Arama Bilgileri</Typography>
                    <Typography><strong>Call ID:</strong> {selectedWebhook.callId}</Typography>
                    <Typography><strong>Telefon:</strong> {selectedWebhook.number}</Typography>
                    <Typography><strong>Süre:</strong> {formatDuration(selectedWebhook.duration)}</Typography>
                    <Typography><strong>Bitiş Nedeni:</strong> {selectedWebhook.endReason}</Typography>
                    <Typography><strong>Event:</strong> {selectedWebhook.event}</Typography>
                    <Typography><strong>Webhook Zamanı:</strong> {formatReadableDate(selectedWebhook.webhookTimestamp)}</Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Analiz
                    </Typography>
                    <Typography variant="body2">
                      {selectedWebhook.analysis}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12}>
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="h6">Konuşma Metni</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap' }}>
                      {selectedWebhook.conversation}
                    </Typography>
                  </AccordionDetails>
                </Accordion>
              </Grid>
              {selectedWebhook.pipeline && (
                <Grid item xs={12} md={6}>
                  <Accordion>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography variant="h6">Pipeline Data</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Typography variant="body2" component="pre">
                        {JSON.stringify(JSON.parse(selectedWebhook.pipeline), null, 2)}
                      </Typography>
                    </AccordionDetails>
                  </Accordion>
                </Grid>
              )}
              {selectedWebhook.fields && (
                <Grid item xs={12} md={6}>
                  <Accordion>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography variant="h6">Custom Fields</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Typography variant="body2" component="pre">
                        {JSON.stringify(JSON.parse(selectedWebhook.fields), null, 2)}
                      </Typography>
                    </AccordionDetails>
                  </Accordion>
                </Grid>
              )}
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailDialog(false)}>
            Kapat
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CrmWebhooksPage;
