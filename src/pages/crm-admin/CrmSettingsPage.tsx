import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Slider,
  InputAdornment,
} from '@mui/material';
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import crmService from '../../services/crmService';

interface CrmConfiguration {
  syncInterval: number;
  retryAttempts: number;
  timeout: number;
  batchSize: number;
  enableLogging: boolean;
  autoSync: boolean;
  maxConcurrentSyncs: number;
  errorThreshold: number;
}

const CrmSettingsPage: React.FC = () => {
  const [config, setConfig] = useState<CrmConfiguration>({
    syncInterval: 3600,
    retryAttempts: 3,
    timeout: 30000,
    batchSize: 100,
    enableLogging: true,
    autoSync: true,
    maxConcurrentSyncs: 5,
    errorThreshold: 10,
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    fetchConfiguration();
  }, []);

  const fetchConfiguration = async () => {
    try {
      setLoading(true);
      setError(null);
      const configuration = await crmService.getConfiguration();
      setConfig({
        syncInterval: configuration.syncInterval || 3600,
        retryAttempts: configuration.retryAttempts || 3,
        timeout: configuration.timeout || 30000,
        batchSize: configuration.batchSize || 100,
        enableLogging: configuration.enableLogging !== false,
        autoSync: configuration.autoSync !== false,
        maxConcurrentSyncs: configuration.maxConcurrentSyncs || 5,
        errorThreshold: configuration.errorThreshold || 10,
      });
    } catch (err: any) {
      console.error('Error fetching configuration:', err);
      setError('Konfigürasyon yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);
      
      await crmService.updateConfiguration(config);
      setSuccess('Konfigürasyon başarıyla kaydedildi!');
      
      // Success mesajını 3 saniye sonra temizle
      setTimeout(() => setSuccess(null), 3000);
    } catch (err: any) {
      console.error('Error saving configuration:', err);
      setError('Konfigürasyon kaydedilirken bir hata oluştu.');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    fetchConfiguration();
    setError(null);
    setSuccess(null);
  };

  const handleConfigChange = (key: keyof CrmConfiguration, value: any) => {
    setConfig(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const formatTime = (seconds: number) => {
    if (seconds < 60) return `${seconds} saniye`;
    if (seconds < 3600) return `${Math.round(seconds / 60)} dakika`;
    return `${Math.round(seconds / 3600)} saat`;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" gutterBottom>
            CRM Ayarları
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            CRM senkronizasyon ve sistem ayarları
          </Typography>
        </Box>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleReset}
            disabled={saving}
          >
            Sıfırla
          </Button>
          <Button
            variant="contained"
            startIcon={saving ? <CircularProgress size={16} /> : <SaveIcon />}
            onClick={handleSave}
            disabled={saving}
          >
            Kaydet
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Senkronizasyon Ayarları */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <SettingsIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">
                  Senkronizasyon Ayarları
                </Typography>
              </Box>
              
              <Box mb={3}>
                <Typography gutterBottom>
                  Senkronizasyon Aralığı: {formatTime(config.syncInterval)}
                </Typography>
                <Slider
                  value={config.syncInterval}
                  onChange={(_, value) => handleConfigChange('syncInterval', value)}
                  min={300}
                  max={86400}
                  step={300}
                  marks={[
                    { value: 300, label: '5dk' },
                    { value: 3600, label: '1sa' },
                    { value: 21600, label: '6sa' },
                    { value: 86400, label: '24sa' },
                  ]}
                />
              </Box>

              <Box mb={3}>
                <TextField
                  fullWidth
                  label="Yeniden Deneme Sayısı"
                  type="number"
                  value={config.retryAttempts}
                  onChange={(e) => handleConfigChange('retryAttempts', parseInt(e.target.value))}
                  inputProps={{ min: 1, max: 10 }}
                />
              </Box>

              <Box mb={3}>
                <TextField
                  fullWidth
                  label="Timeout"
                  type="number"
                  value={config.timeout}
                  onChange={(e) => handleConfigChange('timeout', parseInt(e.target.value))}
                  InputProps={{
                    endAdornment: <InputAdornment position="end">ms</InputAdornment>,
                  }}
                  inputProps={{ min: 5000, max: 120000, step: 1000 }}
                />
              </Box>

              <Box mb={2}>
                <TextField
                  fullWidth
                  label="Batch Boyutu"
                  type="number"
                  value={config.batchSize}
                  onChange={(e) => handleConfigChange('batchSize', parseInt(e.target.value))}
                  inputProps={{ min: 10, max: 1000, step: 10 }}
                  helperText="Tek seferde işlenecek kayıt sayısı"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Sistem Ayarları */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Sistem Ayarları
              </Typography>
              
              <Box mb={3}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={config.enableLogging}
                      onChange={(e) => handleConfigChange('enableLogging', e.target.checked)}
                    />
                  }
                  label="Detaylı Loglama"
                />
              </Box>

              <Box mb={3}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={config.autoSync}
                      onChange={(e) => handleConfigChange('autoSync', e.target.checked)}
                    />
                  }
                  label="Otomatik Senkronizasyon"
                />
              </Box>

              <Box mb={3}>
                <TextField
                  fullWidth
                  label="Maksimum Eşzamanlı Sync"
                  type="number"
                  value={config.maxConcurrentSyncs}
                  onChange={(e) => handleConfigChange('maxConcurrentSyncs', parseInt(e.target.value))}
                  inputProps={{ min: 1, max: 20 }}
                  helperText="Aynı anda çalışabilecek sync işlemi sayısı"
                />
              </Box>

              <Box mb={2}>
                <TextField
                  fullWidth
                  label="Hata Eşiği"
                  type="number"
                  value={config.errorThreshold}
                  onChange={(e) => handleConfigChange('errorThreshold', parseInt(e.target.value))}
                  inputProps={{ min: 1, max: 100 }}
                  InputProps={{
                    endAdornment: <InputAdornment position="end">%</InputAdornment>,
                  }}
                  helperText="Bu eşik aşıldığında uyarı gönderilir"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Konfigürasyon Özeti */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Mevcut Konfigürasyon Özeti
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Senkronizasyon Aralığı
                  </Typography>
                  <Typography variant="h6">
                    {formatTime(config.syncInterval)}
                  </Typography>
                </Box>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Batch Boyutu
                  </Typography>
                  <Typography variant="h6">
                    {config.batchSize} kayıt
                  </Typography>
                </Box>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Timeout
                  </Typography>
                  <Typography variant="h6">
                    {config.timeout / 1000}s
                  </Typography>
                </Box>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Otomatik Sync
                  </Typography>
                  <Typography variant="h6">
                    {config.autoSync ? 'Aktif' : 'Pasif'}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CrmSettingsPage;
