import React, { useEffect, useState, useMemo, useCallback, memo } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip,
  Grid,
  Card,
  CardContent,
  Chip,
  Collapse,
  IconButton,
} from '@mui/material';
import { ExpandMore, ExpandLess } from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import ExportButton from '../../components/admin/ExportButton';
import AdminFilterBar, { AdminFilterData } from '../../components/admin/AdminFilterBar';
import { formatReadableDate, parseToDate } from '../../utils/dateUtils';
import { useDebouncedCallback } from 'use-debounce';
import {
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Phone as PhoneIcon,
  People as PeopleIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';
import axios from 'axios';


interface Contact {
  name: string;
  surname: string;
  email: string;
  phoneNumber: string;
  campaignName?: string;
  status: string;
  brandName?: string;
  createdAt: string;
  customerToCampaignCreatedAt?: string;
  formToCampaignCreatedAt?: string;
}

interface WebhookEvent {
  callId: string;
  event: string;
  conversation: string;
  analysis: string;
  createdAt: string;
  number: string;
  endReason?: string;
  pipeline?: string;
}

interface JoinedRow {
  name: string;
  surname: string;
  email: string;
  phone_number: string;
  campaign_name?: string;
  status: string;
  brand: string;
  call_id: string;
  event: string;
  conversation: string;
  analysis: string;
  created_at: string;
  endReason?: string;
  pipeline?: string;
  pipeline_stage?: string;
  callAcceptanceStatus: 'accepted' | 'rejected' | 'no_answer' | 'unknown';
}

// İstatistik kartı komponenti
const StatCard: React.FC<{ title: string; value: string | number; color?: 'primary' | 'success' | 'warning' | 'error'; icon?: React.ReactNode }> = ({ 
  title, 
  value, 
  color = 'primary',
  icon 
}) => (
  <Card>
    <CardContent>
      <Box display="flex" alignItems="center" gap={1}>
        {icon}
        <Box>
          <Typography color="text.secondary" gutterBottom variant="body2">
            {title}
          </Typography>
          <Typography variant="h5" component="div" color={color}>
            {value}
          </Typography>
        </Box>
      </Box>
    </CardContent>
  </Card>
);


// Arama kabul durumunu belirleyen fonksiyon
const determineCallAcceptanceStatus = (endReason?: string, analysis?: string, conversation?: string): 'accepted' | 'rejected' | 'no_answer' | 'unknown' => {
  const lowerEndReason = endReason?.toLowerCase() || '';
  const lowerAnalysis = analysis?.toLowerCase() || '';
  const lowerConversation = conversation?.toLowerCase() || '';
  
  // Kabul edildi
  if (
    lowerEndReason.includes('completed') ||
    lowerEndReason.includes('answered') ||
    lowerAnalysis.includes('answered') ||
    lowerAnalysis.includes('responded') ||
    lowerConversation.length > 50 // Uzun konuşma = kabul edildi
  ) {
    return 'accepted';
  }
  
  // Reddedildi
  if (
    lowerEndReason.includes('busy') ||
    lowerEndReason.includes('declined') ||
    lowerEndReason.includes('rejected') ||
    lowerAnalysis.includes('busy') ||
    lowerAnalysis.includes('declined')
  ) {
    return 'rejected';
  }
  
  // Açmadı
  if (
    lowerEndReason.includes('no-answer') ||
    lowerEndReason.includes('no_answer') ||
    lowerEndReason.includes('unanswered') ||
    lowerEndReason.includes('timeout') ||
    lowerAnalysis.includes('no answer') ||
    lowerAnalysis.includes('unanswered')
  ) {
    return 'no_answer';
  }
  
  return 'unknown';
};

const OptimizedDetailModal: React.FC<{ open: boolean; onClose: () => void; selectedRow: JoinedRow | null; }> = memo(({ open, onClose, selectedRow }) => {
  const [expandedSections, setExpandedSections] = useState<{ conversation: boolean; analysis: boolean; }>({ conversation: false, analysis: false });

  const toggleSection = useCallback((section: 'conversation' | 'analysis') => {
    setExpandedSections(prev => ({ ...prev, [section]: !prev[section] }));
  }, []);

  useEffect(() => {
    if (!open) {
      setExpandedSections({ conversation: false, analysis: false });
    }
  }, [open]);

  const getCallAcceptanceChip = useCallback((status: 'accepted' | 'rejected' | 'no_answer' | 'unknown') => {
    const chipProps = {
      accepted: { label: 'Kabul Edildi', color: 'success' as const },
      rejected: { label: 'Reddedildi', color: 'error' as const },
      no_answer: { label: 'Cevapsız', color: 'warning' as const },
      unknown: { label: 'Bilinmiyor', color: 'default' as const },
    };
    const props = chipProps[status] || chipProps.unknown;
    return <Chip {...props} size="small" />;
  }, []);

  if (!open || !selectedRow) {
    return null;
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth PaperProps={{ sx: { maxHeight: '90vh' } }}>
      <DialogTitle>Detay Bilgileri</DialogTitle>
      <DialogContent dividers>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="primary">Kişi Bilgileri</Typography>
              <Box sx={{ pl: 1, mt: 1 }}>
                <Typography variant="body2"><strong>Ad Soyad:</strong> {selectedRow.name} {selectedRow.surname}</Typography>
                <Typography variant="body2"><strong>Email:</strong> {selectedRow.email}</Typography>
                <Typography variant="body2"><strong>Telefon:</strong> {selectedRow.phone_number}</Typography>
              </Box>
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="primary">Kampanya Bilgileri</Typography>
              <Box sx={{ pl: 1, mt: 1 }}>
                <Typography variant="body2"><strong>Kampanya:</strong> {selectedRow.campaign_name}</Typography>
                <Typography variant="body2"><strong>Marka:</strong> {selectedRow.brand}</Typography>
                <Typography variant="body2"><strong>Durum:</strong> {selectedRow.status}</Typography>
              </Box>
            </Box>
          </Grid>
          <Grid item xs={12}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="primary">Arama Bilgileri</Typography>
              <Box sx={{ pl: 1, mt: 1 }}>
                <Typography variant="body2"><strong>Call ID:</strong> {selectedRow.call_id}</Typography>
                <Typography variant="body2"><strong>Event:</strong> {selectedRow.event}</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                  <Typography variant="body2"><strong>Arama Kabul Durumu:</strong></Typography>
                  {getCallAcceptanceChip(selectedRow.callAcceptanceStatus)}
                </Box>
                <Typography variant="body2"><strong>Oluşturulma:</strong> {formatReadableDate(selectedRow.created_at)}</Typography>
              </Box>
            </Box>
          </Grid>
          <Grid item xs={12}>
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }} onClick={() => toggleSection('conversation')}>
                <IconButton size="small">{expandedSections.conversation ? <ExpandLess /> : <ExpandMore />}</IconButton>
                <Typography variant="subtitle2" color="primary">Görüşme Detayı</Typography>
              </Box>
              <Collapse in={expandedSections.conversation}>
                <Box sx={{ mt: 1, p: 2, bgcolor: '#f5f5f5', borderRadius: 1, maxHeight: '200px', overflow: 'auto' }}>
                  <Typography variant="body2" sx={{ whiteSpace: 'pre-line', wordBreak: 'break-word' }}>
                    {selectedRow.conversation || 'Görüşme bilgisi bulunmuyor.'}
                  </Typography>
                </Box>
              </Collapse>
            </Box>
          </Grid>
          <Grid item xs={12}>
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }} onClick={() => toggleSection('analysis')}>
                <IconButton size="small">{expandedSections.analysis ? <ExpandLess /> : <ExpandMore />}</IconButton>
                <Typography variant="subtitle2" color="primary">Analiz Detayı</Typography>
              </Box>
              <Collapse in={expandedSections.analysis}>
                <Box sx={{ mt: 1, p: 2, bgcolor: '#f5f5f5', borderRadius: 1, maxHeight: '200px', overflow: 'auto' }}>
                  <Typography variant="body2" sx={{ whiteSpace: 'pre-line', wordBreak: 'break-word' }}>
                    {selectedRow.analysis || 'Analiz bilgisi bulunmuyor.'}
                  </Typography>
                </Box>
              </Collapse>
            </Box>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} variant="contained">Kapat</Button>
      </DialogActions>
    </Dialog>
  );
});

const CrmContactsWebhookJoinPage: React.FC = () => {
  const { token } = useAuth();
  const [joinedRows, setJoinedRows] = useState<JoinedRow[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedRow, setSelectedRow] = useState<JoinedRow | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [filterData, setFilterData] = useState<AdminFilterData>({
    searchText: '',
    campaignName: '',
    brand: '',
    user: '',
    event: '',
    endReason: '',
    pipeline: '',
    dateRange: { startDate: '', endDate: '', startTime: '00:00', endTime: '23:59' },
  });

  const debouncedSetFilterData = useDebouncedCallback((data: AdminFilterData) => {
    setFilterData(data);
  }, 300);

  const handleOpenModal = useCallback((row: JoinedRow) => {
    setSelectedRow(row);
    setModalOpen(true);
  }, []);
  
  const handleCloseModal = useCallback(() => {
    setModalOpen(false);
    // Delay clearing selectedRow to allow smooth close animation
    setTimeout(() => setSelectedRow(null), 200);
  }, []);

  // Dinamik event ve endReason seçenekleri
  const eventOptions = useMemo(() => Array.from(new Set(joinedRows.map(r => r.event))).filter((ev): ev is string => !!ev).map(ev => ({ value: ev, label: ev })), [joinedRows]);
  const endReasonOptions = useMemo(() => Array.from(new Set(joinedRows.map(r => r.endReason))).filter((ev): ev is string => !!ev).map(ev => ({ value: ev, label: ev })), [joinedRows]);
  const pipelineOptions = useMemo(() => {
    const pipelines = new Set<string>();
    joinedRows.forEach(row => {
      if (row.pipeline_stage) {
        pipelines.add(row.pipeline_stage);
      }
    });
    return Array.from(pipelines).map(p => ({ value: p, label: p }));
  }, [joinedRows]);

  const campaignNameOptions = useMemo(() => Array.from(new Set(joinedRows.map(r => r.campaign_name))).filter((c): c is string => !!c).map(c => ({ value: c, label: c })), [joinedRows]);
  const brandOptions = useMemo(() => Array.from(new Set(joinedRows.map(r => r.brand))).filter((b): b is string => !!b).map(b => ({ value: b, label: b })), [joinedRows]);

  const filteredRows = useMemo(() => joinedRows.filter(row => {
    const search = filterData.searchText.toLowerCase();
    const matchesSearch =
      (!search ||
        (row.name && row.name.toLowerCase().includes(search)) ||
        (row.surname && row.surname.toLowerCase().includes(search)) ||
        (row.email && row.email.toLowerCase().includes(search)) ||
        (row.phone_number && row.phone_number.includes(search)) ||
        (row.campaign_name && row.campaign_name.toLowerCase().includes(search)) ||
        (row.brand && row.brand.toLowerCase().includes(search)) ||
        (row.event && row.event.toLowerCase().includes(search)) ||
        (row.analysis && row.analysis.toLowerCase().includes(search)) ||
        (row.conversation && row.conversation.toLowerCase().includes(search)));
    const matchesCampaign = !filterData.campaignName || row.campaign_name === filterData.campaignName;
    const matchesBrand = !filterData.brand || row.brand === filterData.brand;
    const matchesUser = !filterData.user ||
      (row.name && row.name.toLowerCase().includes(filterData.user.toLowerCase())) ||
      (row.surname && row.surname.toLowerCase().includes(filterData.user.toLowerCase())) ||
      (row.email && row.email.toLowerCase().includes(filterData.user.toLowerCase()));
    const matchesEvent = !filterData.event || row.event === filterData.event;
    const matchesEndReason = !filterData.endReason || (row.endReason || '') === filterData.endReason;
    const matchesPipeline = !filterData.pipeline || row.pipeline_stage === filterData.pipeline;

    let matchesDate = true;
    const contactDate = parseToDate(row.created_at);
    if (contactDate) {
      if (filterData.dateRange?.startDate) {
        const dateParts = filterData.dateRange.startDate.split('-').map(Number);
        const timeParts = (filterData.dateRange.startTime || '00:00').split(':').map(Number);
        const startDate = new Date(dateParts[0], dateParts[1] - 1, dateParts[2], timeParts[0], timeParts[1]);
        matchesDate = matchesDate && contactDate >= startDate;
      }
      if (filterData.dateRange?.endDate) {
        const dateParts = filterData.dateRange.endDate.split('-').map(Number);
        const timeParts = (filterData.dateRange.endTime || '23:59').split(':').map(Number);
        const endDate = new Date(dateParts[0], dateParts[1] - 1, dateParts[2], timeParts[0], timeParts[1]);
        matchesDate = matchesDate && contactDate <= endDate;
      }
    } else if (filterData.dateRange?.startDate || filterData.dateRange?.endDate) {
      matchesDate = false;
    }

    return matchesSearch && matchesCampaign && matchesBrand && matchesUser && matchesEvent && matchesEndReason && matchesDate && matchesPipeline;
  }), [joinedRows, filterData]);

  // İstatistikleri hesapla
  const stats = useMemo(() => {
    const totalContacts = joinedRows.length;
    if (totalContacts === 0) return {
      totalContacts: 0,
      calledContacts: 0,
      callRate: '0%',
      acceptedCalls: 0,
      rejectedCalls: 0,
      noAnswerCalls: 0,
      acceptanceRate: '0%'
    };

    const calledContacts = joinedRows.length; // Tüm joinedRows aranmış olanlar
    const acceptedCalls = joinedRows.filter(r => r.callAcceptanceStatus === 'accepted').length;
    const rejectedCalls = joinedRows.filter(r => r.callAcceptanceStatus === 'rejected').length;
    const noAnswerCalls = joinedRows.filter(r => r.callAcceptanceStatus === 'no_answer').length;

    const callRate = '100%'; // Tüm joined veriler aranmış
    const acceptanceRate = calledContacts > 0 ? ((acceptedCalls / calledContacts) * 100).toFixed(1) + '%' : '0%';

    return {
      totalContacts,
      calledContacts,
      callRate,
      acceptedCalls,
      rejectedCalls,
      noAnswerCalls,
      acceptanceRate
    };
  }, [joinedRows]);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const headers = token ? { Authorization: `Bearer ${token}` } : {};
      const [contactsRes, webhooksRes] = await Promise.all([
        axios.get('https://360avantajli.com/api/Campaign_Service/crm-admin/contacts', { headers }),
        axios.get('https://360avantajli.com/api/Campaign_Service/crm-admin/webhook-events', { headers }),
      ]);
      const contactsData = Array.isArray(contactsRes.data)
        ? contactsRes.data
        : contactsRes.data.content || contactsRes.data.data || [];
      const webhooksData: WebhookEvent[] = (Array.isArray(webhooksRes.data)
        ? webhooksRes.data
        : webhooksRes.data.content || webhooksRes.data.data || []
      ).filter((w: WebhookEvent) => w.event?.trim().toLowerCase() !== 'voice_evaluation');
      
      const webhookMap = new Map<string, WebhookEvent[]>();
      webhooksData.forEach((w) => {
        const existing = webhookMap.get(w.number) || [];
        webhookMap.set(w.number, [...existing, w]);
      });

      const joined: JoinedRow[] = [];
      contactsData.forEach((c: Contact) => {
        const phoneWithCode = `+90${c.phoneNumber}`;
        const matchingWebhooks = webhookMap.get(phoneWithCode);

        if (matchingWebhooks) {
          matchingWebhooks.forEach((w) => {
            const callAcceptanceStatus = determineCallAcceptanceStatus(w.endReason, w.analysis, w.conversation);
            let pipeline_stage: string | undefined;
            if (w.pipeline) {
              try {
                const parsed = JSON.parse(w.pipeline);
                if (parsed.stage) {
                  pipeline_stage = parsed.stage;
                }
              } catch { /* ignore */ }
            }
            
            joined.push({
              name: c.name,
              surname: c.surname,
              email: c.email,
              phone_number: c.phoneNumber,
              campaign_name: c.campaignName,
              status: c.status,
              brand: c.brandName || '',
              call_id: w.callId,
              event: w.event,
              conversation: w.conversation,
              analysis: w.analysis,
              created_at: c.customerToCampaignCreatedAt || c.formToCampaignCreatedAt || c.createdAt,
              endReason: w.endReason,
              pipeline: w.pipeline,
              pipeline_stage,
              callAcceptanceStatus,
            });
          });
        }
      });

      setJoinedRows(joined);
    } catch (err) {

      if (axios.isAxiosError(err)) {
        setError(`Veri yüklenirken bir hata oluştu: ${err.message}`);
      } else {
        setError('Veri yüklenirken bilinmeyen bir hata oluştu.');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // Call acceptance status için chip rengi
  const getCallAcceptanceChip = (status: string) => {
    switch (status) {
      case 'accepted':
        return <Chip icon={<CheckCircleIcon />} label="Kabul Etti" color="success" size="small" />;
      case 'rejected':
        return <Chip icon={<CancelIcon />} label="Meşgul/Red" color="error" size="small" />;
      case 'no_answer':
        return <Chip icon={<PhoneIcon />} label="Açmadı" color="warning" size="small" />;
      default:
        return <Chip label="Bilinmiyor" color="default" size="small" />;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* İstatistik Kartları */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard 
            title="Toplam Aranmış Müşteri" 
            value={stats.totalContacts} 
            icon={<PeopleIcon color="primary" />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard 
            title="Aramayı Kabul Eden" 
            value={stats.acceptedCalls} 
            color="success"
            icon={<CheckCircleIcon color="success" />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard 
            title="Aramayı Açmayan" 
            value={stats.noAnswerCalls} 
            color="warning"
            icon={<PhoneIcon color="warning" />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard 
            title="Meşgul/Reddeden" 
            value={stats.rejectedCalls} 
            color="error"
            icon={<CancelIcon color="error" />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard 
            title="Kabul Etme Oranı" 
            value={stats.acceptanceRate} 
            color="success"
            icon={<AssessmentIcon color="success" />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <StatCard 
            title="Aranma Oranı" 
            value={stats.callRate} 
            color="primary"
            icon={<PhoneIcon color="primary" />}
          />
        </Grid>
      </Grid>

      <AdminFilterBar
        filterData={filterData}
        setFilterData={debouncedSetFilterData}
        onFilter={() => { /* Refetch or client-side filter logic could be triggered here */ }}
        showDateFilter={true}
        additionalFilters={[
          { field: 'campaignName', label: 'Kampanya Adı', type: 'select', options: campaignNameOptions },
          { field: 'brand', label: 'Marka Adı', type: 'select', options: brandOptions },
          { field: 'user', label: 'Kullanıcı Adı/E-posta', type: 'text' },
          { field: 'event', label: 'Event Tipi', type: 'select', options: eventOptions },
          { field: 'endReason', label: 'Bitiş Nedeni', type: 'select', options: endReasonOptions },
          { field: 'pipeline', label: 'Pipeline Aşaması', type: 'select', options: pipelineOptions },
        ]}
        renderExportButton={() => (
          <ExportButton
            data={filteredRows}
            filename="Contacts_Webhook_Join"
            headCells={[
              { id: 'name', label: 'Ad' },
              { id: 'surname', label: 'Soyad' },
              { id: 'email', label: 'Email' },
              { id: 'phone_number', label: 'Telefon' },
              { id: 'campaign_name', label: 'Kampanya' },
              { id: 'status', label: 'Durum' },
              { id: 'brand', label: 'Marka' },
              { id: 'call_id', label: 'Call ID' },
              { id: 'event', label: 'Event' },
              { id: 'conversation', label: 'Görüşme' },
              { id: 'analysis', label: 'Analiz' },
              { id: 'created_at', label: 'Oluşturulma' },
              { id: 'callAcceptanceStatus', label: 'Arama Kabul Durumu' },
            ]}
          />
        )}
      />
      {error && <Alert severity="error">{error}</Alert>}
      {loading ? (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      ) : (
        <Paper>
          <TableContainer sx={{ maxWidth: '100vw', overflowX: 'auto' }}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Ad</TableCell>
                  <TableCell>Soyad</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell>Telefon</TableCell>
                  <TableCell>Kampanya</TableCell>
                  <TableCell>Durum</TableCell>
                  <TableCell>Marka</TableCell>
                  <TableCell>Call ID</TableCell>
                  <TableCell>Event</TableCell>
                  <TableCell>Arama Kabul Durumu</TableCell>
                  <TableCell>Görüşme</TableCell>
                  <TableCell>Analiz</TableCell>
                  <TableCell>Oluşturulma</TableCell>
                  <TableCell>Detay</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredRows.map((row, idx) => (
                  <TableRow key={`${row.call_id}-${idx}`}>
                    <TableCell>{row.name}</TableCell>
                    <TableCell>{row.surname}</TableCell>
                    <TableCell>{row.email}</TableCell>
                    <TableCell>{row.phone_number}</TableCell>
                    <TableCell>{row.campaign_name}</TableCell>
                    <TableCell>{row.status}</TableCell>
                    <TableCell>{row.brand}</TableCell>
                    <TableCell>{row.call_id}</TableCell>
                    <TableCell>{row.event}</TableCell>
                    <TableCell>{getCallAcceptanceChip(row.callAcceptanceStatus)}</TableCell>
                    <TableCell
                      sx={{
                        minWidth: 300,
                        maxWidth: 400,
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        fontSize: '0.95rem',
                        p: 1,
                      }}
                    >
                      <Tooltip title={row.conversation || ''} placement="top" arrow>
                        <span>{row.conversation}</span>
                      </Tooltip>
                    </TableCell>
                    <TableCell
                      sx={{
                        minWidth: 300,
                        maxWidth: 400,
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        fontSize: '0.95rem',
                        p: 1,
                      }}
                    >
                      <Tooltip title={row.analysis || ''} placement="top" arrow>
                        <span>{row.analysis}</span>
                      </Tooltip>
                    </TableCell>
                    <TableCell>{formatReadableDate(row.created_at)}</TableCell>
                    <TableCell>
                      <Button size="small" variant="text" onClick={() => handleOpenModal(row)} sx={{ minWidth: 0, p: 0.5, fontSize: '0.95rem' }}>
                        Detay
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          {/* Optimized Detay Modalı */}
          <OptimizedDetailModal 
            open={modalOpen} 
            onClose={handleCloseModal} 
            selectedRow={selectedRow}
          />
        </Paper>
      )}
    </Box>
  );
};

export default CrmContactsWebhookJoinPage; 