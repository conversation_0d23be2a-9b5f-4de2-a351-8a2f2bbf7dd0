import React, { useState } from "react";
import { useNavigate, Link as RouterLink } from "react-router-dom";
import axios from "axios";
import {
  Box,
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Alert,
  CircularProgress,
  useTheme,
  Link,
  Grid,
} from "@mui/material";
import { PersonAdd as PersonAddIcon } from "@mui/icons-material";
import { useIntl } from "react-intl";
import { useLanguage } from "../i18n/LanguageContext";

const RegisterPage: React.FC = () => {
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const role = "USER";
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const navigate = useNavigate();
  const theme = useTheme();
  const intl = useIntl();
  const { locale } = useLanguage();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (
      !firstName ||
      !lastName ||
      !email ||
      !username ||
      !password ||
      !confirmPassword
    ) {
      setError(intl.formatMessage({ id: "register.fillAllFields" }));
      return;
    }

    if (password !== confirmPassword) {
      setError(intl.formatMessage({ id: "register.passwordsDoNotMatch" }));
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError(intl.formatMessage({ id: "form.invalidEmail" }));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await axios.post(
        "https://360avantajli.com/api/Auth_Service/auth/create-user",
        {
          name: firstName,
          surname: lastName,
          email,
          username,
          password,
          role,
        },
        {
          headers: {
            "Content-Type": "application/json",
          },
          withCredentials: false,
        }
      );

      setSuccess(true);

      // Redirect to login page after 2 seconds
      setTimeout(() => {
        navigate("/login");
      }, 2000);
    } catch (err) {
      // More detailed error handling
      let errorMessage = intl.formatMessage({ id: "register.genericError" });

      if (err && typeof err === "object") {
        if ("response" in err) {
          const errorResponse = err as {
            response?: {
              status?: number;
              data?: any;
            };
          };

          // Check for specific error messages from the server
          if (errorResponse.response?.data?.message) {
            errorMessage = errorResponse.response.data.message;
          } else if (errorResponse.response?.status === 409) {
            errorMessage = intl.formatMessage({ id: "register.userExists" });
          }
        } else if ("message" in err) {
          errorMessage = (err as Error).message;
        }
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Container component="main" maxWidth="xs" sx={{ px: { xs: 2, sm: 3 } }}>
      <Box
        sx={{
          marginTop: { xs: 4, sm: 6, md: 8 },
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          mb: { xs: 4, sm: 6, md: 8 },
          px: { xs: 1, sm: 0 },
        }}
      >
        <Paper
          elevation={3}
          sx={{
            p: { xs: 2, sm: 3, md: 4 },
            width: "100%",
            borderRadius: 2,
            boxShadow: "0 8px 24px rgba(149, 157, 165, 0.2)",
          }}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            <Box
              sx={{
                width: 56,
                height: 56,
                borderRadius: "50%",
                bgcolor: theme.palette.primary.main,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                mb: 2,
              }}
            >
              <PersonAddIcon sx={{ color: "white", fontSize: 28 }} />
            </Box>

            <Typography component="h1" variant="h5" fontWeight={600} mb={3}>
              {intl.formatMessage({ id: "register.title" })}
            </Typography>

            {success && (
              <Alert severity="success" sx={{ width: "100%", mb: 2 }}>
                {intl.formatMessage({ id: "register.success" })}
              </Alert>
            )}

            {error && !success && (
              <Alert severity="error" sx={{ width: "100%", mb: 2 }}>
                {error}
              </Alert>
            )}

            <Box
              component="form"
              onSubmit={handleSubmit}
              sx={{ mt: 1, width: "100%" }}
            >
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    required
                    fullWidth
                    id="firstName"
                    label={intl.formatMessage({ id: "form.firstName" })}
                    name="firstName"
                    autoComplete="given-name"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    disabled={isLoading || success}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    required
                    fullWidth
                    id="lastName"
                    label={intl.formatMessage({ id: "form.lastName" })}
                    name="lastName"
                    autoComplete="family-name"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    disabled={isLoading || success}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    required
                    fullWidth
                    id="email"
                    label={intl.formatMessage({ id: "form.email" })}
                    name="email"
                    autoComplete="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={isLoading || success}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    required
                    fullWidth
                    id="username"
                    label={intl.formatMessage({ id: "profile.username" })}
                    name="username"
                    autoComplete="username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    disabled={isLoading || success}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    required
                    fullWidth
                    name="password"
                    label={intl.formatMessage({ id: "login.password" })}
                    type="password"
                    id="password"
                    autoComplete="new-password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    disabled={isLoading || success}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    required
                    fullWidth
                    name="confirmPassword"
                    label={intl.formatMessage({
                      id: "register.confirmPassword",
                    })}
                    type="password"
                    id="confirmPassword"
                    autoComplete="new-password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    disabled={isLoading || success}
                  />
                </Grid>
              </Grid>

              <Button
                type="submit"
                fullWidth
                variant="contained"
                sx={{
                  mt: 3,
                  mb: 2,
                  py: 1.5,
                  borderRadius: "12px",
                  textTransform: "none",
                  fontWeight: 600,
                  fontSize: "1rem",
                  boxShadow:
                    "0 8px 16px rgba(0, 0, 0, 0.1), inset 0 -2px 4px rgba(0, 0, 0, 0.05)",
                  background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                  backdropFilter: "blur(8px)",
                  color: "#fff",
                  border: "none",
                  transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                  "&:hover": {
                    boxShadow:
                      "0 12px 24px rgba(0, 0, 0, 0.15), inset 0 -2px 6px rgba(0, 0, 0, 0.08)",
                    transform: "translateY(-2px)",
                    background: `linear-gradient(135deg, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`,
                    border: "none",
                  },
                  "&:active": {
                    transform: "translateY(0)",
                    boxShadow:
                      "0 4px 8px rgba(0, 0, 0, 0.1), inset 0 -1px 2px rgba(0, 0, 0, 0.05)",
                  },
                }}
                disabled={isLoading || success}
              >
                {isLoading ? (
                  <CircularProgress size={24} color="inherit" />
                ) : (
                  intl.formatMessage({ id: "register.submit" })
                )}
              </Button>

              <Box sx={{ display: "flex", justifyContent: "center", mt: 2 }}>
                <Link
                  component={RouterLink}
                  to="/login"
                  variant="body2"
                  color="primary.main"
                >
                  {intl.formatMessage({ id: "register.haveAccount" })}
                </Link>
              </Box>
            </Box>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default RegisterPage;
