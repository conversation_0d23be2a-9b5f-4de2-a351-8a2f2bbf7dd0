import { useState, useEffect, RefObject } from 'react';

/**
 * Bir elementin görünüm alanında olup olmadığını izleyen hook
 * Bu hook, ekranda görünmeyen bileşenlerin gereksiz render edilmesini önler
 */
export function useInView<T extends HTMLElement>(options?: IntersectionObserverInit): [
  RefObject<T>, 
  boolean
] {
  // useRef yerine useState kullanıldı çünkü ref değerinin değişimini izlememiz gerekiyor
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [ref, _setRef] = useState<T | null>(null);
  const [isInView, setIsInView] = useState(false);

  useEffect(() => {
    if (!ref) return;

    const observer = new IntersectionObserver(([entry]) => {
      // Element görünüm alanına girdiğinde veya çıktığında isInView değerini güncelle
      setIsInView(entry.isIntersecting);
    }, {
      root: null, // Viewport baz alınır
      rootMargin: '100px', // Görünüm alanından 100px önce yüklemeye başla (preload etkisi)
      threshold: 0.01, // Elementin %1'i göründüğünde tetiklen
      ...options
    });

    observer.observe(ref);

    return () => {
      observer.disconnect();
    };
  }, [ref, options]);

  return [{ current: ref } as RefObject<T>, isInView];
}
