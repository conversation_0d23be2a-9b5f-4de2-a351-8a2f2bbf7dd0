import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * ScrollToTop bileşeni, sayfa geçişlerinde kaydırma pozisyonunu 
 * sayfanın en üstüne taşır.
 */
export const ScrollToTop = () => {
  const { pathname } = useLocation();

  // Rota değiştiğinde sayfayı en üste kaydır
  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth' // Yumuşak kaydırma efekti
    });
  }, [pathname]);

  return null;
};

export default ScrollToTop;
