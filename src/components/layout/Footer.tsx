import React from "react";
import {
  <PERSON>,
  Container,
  <PERSON>rid,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Link,
  Icon<PERSON>utton,
  <PERSON><PERSON>,
} from "@mui/material";
import { Link as RouterLink } from "react-router-dom";
import { useIntl } from "react-intl";
import { useTheme } from "../../store/ThemeContext";
import FacebookIcon from "@mui/icons-material/Facebook";
import LinkedInIcon from "@mui/icons-material/LinkedIn";
import InstagramIcon from "@mui/icons-material/Instagram";
import LocalOfferIcon from "@mui/icons-material/LocalOffer";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import SupportAgentIcon from "@mui/icons-material/SupportAgent";

const Footer: React.FC = () => {
  const intl = useIntl();
  const { darkMode } = useTheme();

  const features = [
    {
      icon: <LocalOfferIcon sx={{ fontSize: 40 }} />,
      title: intl.formatMessage({ id: "home.features.bestPrice.title" }),
      description: intl.formatMessage({
        id: "home.features.bestPrice.description",
      }),
    },
    {
      icon: <AccountBalanceIcon sx={{ fontSize: 40 }} />,
      title: intl.formatMessage({ id: "home.features.financing.title" }),
      description: intl.formatMessage({
        id: "home.features.financing.description",
      }),
    },
    {
      icon: <SupportAgentIcon sx={{ fontSize: 40 }} />,
      title: intl.formatMessage({ id: "home.features.support.title" }),
      description: intl.formatMessage({
        id: "home.features.support.description",
      }),
    },
  ];

  const footerLinks = {
    company: [
      { name: intl.formatMessage({ id: "footer.about" }), href: "/about" },
      { name: intl.formatMessage({ id: "footer.contact" }), href: "/contact" },
      { name: intl.formatMessage({ id: "footer.careers" }), href: "/careers" },
    ],
    legal: [
      { name: intl.formatMessage({ id: "footer.privacy" }), href: "/privacy" },
      { name: intl.formatMessage({ id: "footer.terms" }), href: "/terms" },
      { name: intl.formatMessage({ id: "footer.kvkk" }), href: "/kvkk" },
      {
        name: intl.formatMessage({ id: "cookie.policy.link" }),
        href: "/cookie-policy",
      },
    ],
    categories: [
      { name: "Market", href: "/category/market" },
      { name: "Elektronik", href: "/category/electronics" },
      { name: "Giyim", href: "/category/clothing" },
      { name: "Kozmetik", href: "/category/cosmetics" },
    ],
    social: [
      {
        name: "Facebook",
        href: "https://www.facebook.com/digi360reklam",
        icon: <FacebookIcon />,
      },
      {
        name: "LinkedIn",
        href: "https://www.linkedin.com/company/digi360medya",
        icon: <LinkedInIcon />,
      },
      {
        name: "Instagram",
        href: "https://www.instagram.com/digi360medya",
        icon: <InstagramIcon />,
      },
    ],
  };

  return (
    <Box
      component="footer"
      sx={{
        bgcolor: darkMode ? "background.paper" : "#fff",
        color: darkMode ? "common.white" : "text.primary",
        py: 6,
        mt: "auto",
        borderTop: 1,
        borderColor: darkMode ? "rgba(255, 255, 255, 0.1)" : "divider",
        boxShadow: darkMode ? "none" : "0 -4px 20px rgba(0,0,0,0.05)",
      }}
    >
      <Container maxWidth="lg" sx={{ px: { xs: 1, sm: 2, md: 3 } }}>
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", sm: "row" },
            justifyContent: "center",
            alignItems: "center",
            gap: 3,
            mb: 6,
            px: { xs: 2, sm: 0 },
            "& > *": {
              flex: "1 1 0%",
              maxWidth: { xs: "100%", sm: "30%" },
              minWidth: { xs: "100%", sm: "200px" },
            },
            "@media (max-width: 600px)": {
              gap: 2,
              "& > *": {
                maxWidth: "100%",
                width: "100%",
              },
            },
          }}
        >
          {features.map((feature, index) => (
            <Box
              key={index}
              sx={{
                display: "flex",
                flexDirection: { xs: "row", sm: "column" },
                alignItems: { xs: "flex-start", sm: "center" },
                textAlign: { xs: "left", sm: "center" },
                p: { xs: 2, sm: 3 },
                width: "100%",
                borderRadius: 2,
                bgcolor: darkMode
                  ? "rgba(255, 255, 255, 0.05)"
                  : "background.paper",
                boxShadow: darkMode
                  ? "0 4px 20px rgba(0, 0, 0, 0.3)"
                  : "0 4px 20px rgba(0, 0, 0, 0.05)",
                border: darkMode
                  ? "1px solid rgba(255, 255, 255, 0.1)"
                  : "1px solid rgba(0, 0, 0, 0.05)",
                transition: "all 0.3s ease",
                "&:hover": {
                  bgcolor: darkMode
                    ? "rgba(255, 255, 255, 0.08)"
                    : "rgba(0, 0, 0, 0.02)",
                  transform: { xs: "none", sm: "translateY(-4px)" },
                  boxShadow: darkMode
                    ? "0 8px 30px rgba(0, 0, 0, 0.5)"
                    : "0 8px 30px rgba(0, 0, 0, 0.1)",
                },
              }}
            >
              <Box
                sx={{
                  color: "primary.main",
                  mb: { xs: 0, sm: 2 },
                  mr: { xs: 2, sm: 0 },
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  p: 1.5,
                  borderRadius: "50%",
                  bgcolor: darkMode
                    ? "rgba(255, 255, 255, 0.08)"
                    : "rgba(0, 0, 0, 0.04)",
                  transition: "all 0.3s ease",
                  flexShrink: 0,
                  "&:hover": {
                    transform: "scale(1.1) rotate(5deg)",
                    bgcolor: darkMode
                      ? "rgba(255, 255, 255, 0.12)"
                      : "rgba(0, 0, 0, 0.08)",
                  },
                }}
              >
                {React.cloneElement(feature.icon, {
                  sx: {
                    fontSize: { xs: 28, sm: 36, md: 40 },
                    color: darkMode ? "primary.main" : "primary.main",
                  },
                })}
              </Box>
              <Box
                sx={{
                  flex: 1,
                  minWidth: 0, // Prevents flex item from overflowing
                }}
              >
                <Typography
                  component="div"
                  sx={{
                    mb: { xs: 0.25, sm: 1 },
                    color: darkMode ? "common.white" : "text.primary",
                    fontWeight: 700,
                    fontSize: { xs: "0.95rem", sm: "1.05rem", md: "1.1rem" },
                    lineHeight: 1.2,
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                  }}
                >
                  {feature.title}
                </Typography>
                <Typography
                  component="div"
                  variant="body2"
                  sx={{
                    color: darkMode ? "grey.300" : "text.secondary",
                    lineHeight: 1.4,
                    fontWeight: 400,
                    fontSize: { xs: "0.8125rem", sm: "0.875rem" },
                    display: "-webkit-box",
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: "vertical",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                  }}
                >
                  {feature.description}
                </Typography>
              </Box>
            </Box>
          ))}
        </Box>

        <Divider
          sx={{
            mb: 6,
            borderColor: darkMode ? "rgba(255, 255, 255, 0.1)" : "divider",
            opacity: darkMode ? 0.2 : 1,
          }}
        />

        <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>
          <Grid item xs={12} sm={4}>
            <Typography
              variant="h6"
              sx={{
                color: darkMode ? "primary.light" : "text.primary",
                fontWeight: 600,
                mb: 2,
              }}
            >
              360Avantajlı
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: darkMode ? "grey.300" : "text.secondary",
                mb: 2,
                lineHeight: 1.6,
              }}
            >
              {intl.formatMessage({ id: "footer.slogan" })}
            </Typography>
            <Stack direction="row" spacing={1}>
              {footerLinks.social.map((link) => (
                <IconButton
                  key={link.name}
                  component="a"
                  href={link.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  sx={{
                    color: darkMode ? "grey.400" : "text.secondary",
                    transition: "all 0.3s ease",
                    "&:hover": {
                      color: darkMode ? "primary.main" : "primary.main",
                      transform: "translateY(-2px)",
                      bgcolor: darkMode
                        ? "rgba(255, 255, 255, 0.05)"
                        : "rgba(0, 0, 0, 0.05)",
                    },
                  }}
                  aria-label={link.name}
                >
                  {link.icon}
                </IconButton>
              ))}
            </Stack>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Typography
              variant="h6"
              sx={{
                color: darkMode ? "primary.light" : "text.primary",
                fontWeight: 600,
                mb: 2,
              }}
            >
              {intl.formatMessage({ id: "footer.about" })}
            </Typography>
            {footerLinks.company.map((link) => (
              <Link
                key={link.href}
                component={RouterLink}
                to={link.href}
                sx={{
                  display: "block",
                  mb: 1,
                  textDecoration: "none",
                  color: darkMode ? "grey.300" : "text.secondary",
                  transition: "all 0.3s ease",
                  "&:hover": {
                    color: darkMode ? "primary.main" : "primary.main",
                    transform: "translateX(4px)",
                  },
                }}
              >
                {link.name}
              </Link>
            ))}
          </Grid>
          <Grid item xs={12} sm={4}>
            <Typography
              variant="h6"
              sx={{
                color: darkMode ? "primary.light" : "text.primary",
                fontWeight: 600,
                mb: 2,
              }}
            >
              {intl.formatMessage({ id: "footer.terms" })}
            </Typography>
            {footerLinks.legal.map((link) => (
              <Link
                key={link.href}
                component={RouterLink}
                to={link.href}
                sx={{
                  display: "block",
                  mb: 1,
                  textDecoration: "none",
                  color: darkMode ? "grey.300" : "text.secondary",
                  transition: "all 0.3s ease",
                  "&:hover": {
                    color: darkMode ? "primary.main" : "primary.main",
                    transform: "translateX(4px)",
                  },
                }}
              >
                {link.name}
              </Link>
            ))}
          </Grid>
        </Grid>
        <Divider
          sx={{
            my: 4,
            borderColor: darkMode ? "rgba(255, 255, 255, 0.1)" : "divider",
            opacity: darkMode ? 0.2 : 1,
          }}
        />
        <Typography
          variant="body2"
          align="center"
          sx={{
            color: darkMode ? "grey.400" : "text.secondary",
            opacity: darkMode ? 0.8 : 1,
          }}
        >
          © {new Date().getFullYear()} 360Avantajlı.{" "}
          {intl.formatMessage({ id: "footer.rights" })}
        </Typography>
      </Container>
    </Box>
  );
};

export default Footer;
