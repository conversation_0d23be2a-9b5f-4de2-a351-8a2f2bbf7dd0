import React, { Suspense, lazy } from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import Box from "@mui/material/Box";
import { CircularProgress } from "@mui/material";
import Navbar from "./Navbar";
import Footer from "./Footer";
import ScrollToTop from "./ScrollToTop";

import CampaignsBanner from "../common/CampaignsBanner";
// Yükleme bileşeni
const LoadingComponent = () => (
  <Box
    sx={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      minHeight: "50vh",
    }}
  >
    <CircularProgress color="primary" />
  </Box>
);

// Ana uygulama sayfaları - lazy loading ile yükleme
const Home = lazy(() => import("../../pages/Home"));
const Categories = lazy(() => import("../../pages/Categories"));
const CategoryPage = lazy(() => import("../../pages/CategoryPage"));
const SearchResults = lazy(() => import("../../pages/SearchResults"));
const CampaignDetail = lazy(() => import("../../pages/CampaignDetail"));
const BrandPage = lazy(() => import("../../pages/BrandPage"));
const BrandsPage = lazy(() => import("../../pages/BrandsPage"));
const LoginPage = lazy(() => import("../../pages/LoginPage"));
const RegisterPage = lazy(() => import("../../pages/RegisterPage"));
const ProfilePage = lazy(() => import("../../pages/ProfilePage"));
const IlgilendigimKampanyalar = lazy(
  () => import("../../pages/IlgilendigimKampanyalar")
);

// Ana sayfa yönlendiricisi
const Layout: React.FC = () => {
  // Router kontrolleri

  return (
    <Box sx={{ display: "flex", flexDirection: "column", minHeight: "100vh" }}>
      <ScrollToTop />
      <Navbar />
      <CampaignsBanner />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          pt: { xs: 1, sm: 2, md: 3 },
          pb: { xs: 2, sm: 3, md: 4 },
          px: { xs: 1, sm: 2, md: 3 },
          width: "100%",
          maxWidth: "100%",
          mx: "auto",
        }}
      >
        <Suspense fallback={<LoadingComponent />}>
          <Routes>
            {/* Ana URL - Artık Home sayfasını göster */}
            <Route path="/" element={<Home />} />

            {/* Arama sonuçları */}
            <Route path="/search" element={<SearchResults />} />

            {/* Login ve Register sayfaları */}
            <Route path="/login" element={<LoginPage />} />
            <Route path="/register" element={<RegisterPage />} />
            <Route path="/profile" element={<ProfilePage />} />
            <Route path="/my-campaigns" element={<IlgilendigimKampanyalar />} />

            {/* Kategori hiyerarşisi */}
            <Route path="/kategoriler" element={<Categories />} />
            <Route
              path="/kategoriler/:categoryId(\d+)"
              element={<Navigate to="/kategoriler" replace />}
            />
            <Route
              path="/kategoriler/:categoryName"
              element={<CategoryPage />}
            />

            {/* Marka hiyerarşisi */}
            <Route path="/marka" element={<BrandsPage />} />
            <Route path="/marka/:brandName" element={<BrandPage />} />
            <Route
              path="/marka/:brandName/:categorySlug"
              element={<BrandPage />}
            />
            <Route
              path="/marka/:brandName/kampanyalar/:campaignName"
              element={<CampaignDetail />}
            />

            {/* Kampanya hiyerarşisi */}
            <Route
              path="/kampanyalar/:campaignName"
              element={<CampaignDetail />}
            />

            {/* Eski campaigns URL'lerinden yeni kampanyalar URL'lerine yönlendirme */}
            <Route
              path="/campaigns/:campaignName"
              element={<Navigate to="/kampanyalar/:campaignName" replace />}
            />
            <Route
              path="/campaigns/:id/:campaignName"
              element={<Navigate to="/kampanyalar/:campaignName" replace />}
            />
            <Route
              path="/campaign/:id"
              element={<Navigate to="/kampanyalar/:id" replace />}
            />
            <Route
              path="/campaign/:id/:campaignName"
              element={<Navigate to="/kampanyalar/:campaignName" replace />}
            />

            {/* Eski brands URL'lerinden yeni marka URL'lerine yönlendirme */}
            <Route path="/brands" element={<Navigate to="/marka" replace />} />
            <Route
              path="/brands/:brandName"
              element={<Navigate to="/marka/:brandName" replace />}
            />
            <Route
              path="/brands/:brandName/:categorySlug"
              element={
                <Navigate to="/marka/:brandName/:categorySlug" replace />
              }
            />
            <Route
              path="/brands/:brandName/campaigns/:campaignName"
              element={
                <Navigate
                  to="/marka/:brandName/kampanyalar/:campaignName"
                  replace
                />
              }
            />

            {/* Marka Yönlendirmeleri */}
            <Route
              path="/brand/:brandId"
              element={<Navigate to="/marka/:brandId" replace />}
            />
            <Route
              path="/brand/:brandId/:brandName"
              element={<Navigate to="/marka/:brandName" replace />}
            />

            {/* Eski categories URL'lerinden yeni kategoriler URL'lerine yönlendirme */}
            <Route
              path="/categories"
              element={<Navigate to="/kategoriler" replace />}
            />
            <Route
              path="/categories/:categoryName"
              element={<Navigate to="/kategoriler/:categoryName" replace />}
            />
            <Route
              path="/categories/:categoryId(\d+)"
              element={<Navigate to="/kategoriler" replace />}
            />

            {/* Kategori Yönlendirmeleri */}
            <Route
              path="/category/:categoryId"
              element={<Navigate to="/kategoriler/:categoryId" replace />}
            />
            <Route
              path="/category/:categoryId/:categoryName"
              element={<Navigate to="/kategoriler/:categoryName" replace />}
            />
            <Route
              path="/category-list/:groupId"
              element={<Navigate to="/kategoriler/group/:groupId" replace />}
            />

            {/* Tanımlanmamış rotaları ana sayfaya yönlendir */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Suspense>
      </Box>
      <Footer />
    </Box>
  );
};

export default Layout;
