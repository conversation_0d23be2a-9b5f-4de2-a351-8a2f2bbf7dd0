import React, { useState } from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  useTheme,
} from '@mui/material';
import { useIntl } from 'react-intl';
import { Send as SendIcon } from '@mui/icons-material';
import { alpha } from '@mui/material/styles';
import { useAuth } from '../../../contexts/AuthContext';
import RegisterPromptModal from '../../common/RegisterPromptModal';

export const NewsletterSection: React.FC = () => {
  const intl = useIntl();
  const [email, setEmail] = useState('');
  const [showRegisterPrompt, setShowRegisterPrompt] = useState(false);
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  const { isAuthenticated } = useAuth();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (email) {
      // Email servisi gelecekte burada entegre edilebilir
      setEmail('');

      // Eğer kullanıcı giriş <PERSON>ş<PERSON>, kayıt olmaya teşvik et
      if (!isAuthenticated) {
        setShowRegisterPrompt(true);
      }
    }
  };

  return (
    <Box
      sx={{
        py: 8,
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: isDarkMode
            ? `linear-gradient(135deg, ${alpha(theme.palette.primary.dark, 0.4)} 0%, ${alpha(theme.palette.primary.main, 0.1)} 100%)`
            : `linear-gradient(135deg, ${alpha(theme.palette.primary.light, 0.2)} 0%, ${alpha(theme.palette.primary.main, 0.05)} 100%)`,
          zIndex: 0,
        },
      }}
    >
      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
        <Paper
          elevation={0}
          sx={{
            p: { xs: 3, md: 6 },
            borderRadius: 4,
            background: isDarkMode
              ? `linear-gradient(145deg, ${alpha('#fff', 0.08)} 0%, ${alpha('#fff', 0.04)} 100%)`
              : `linear-gradient(145deg, ${alpha('#fff', 0.9)} 0%, ${alpha('#fff', 0.95)} 100%)`,
            backdropFilter: 'blur(10px)',
            border: `1px solid ${isDarkMode ? alpha('#fff', 0.1) : alpha('#000', 0.05)}`,
            boxShadow: isDarkMode
              ? '0 8px 32px rgba(0, 0, 0, 0.3)'
              : '0 8px 32px rgba(149, 157, 165, 0.2)',
          }}
        >
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={6}>
              <Typography
                variant="h4"
                component="h2"
                gutterBottom
                sx={{
                  fontWeight: 700,
                  color: isDarkMode ? 'grey.100' : 'grey.900',
                  textShadow: isDarkMode ? '0 2px 4px rgba(0,0,0,0.2)' : 'none',
                }}
              >
                {intl.formatMessage({ id: 'home.newsletter.title' })}
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  mb: 3,
                  opacity: 0.9,
                  color: isDarkMode ? 'grey.300' : 'text.secondary',
                }}
              >
                {intl.formatMessage({ id: 'home.newsletter.description' })}
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <form onSubmit={handleSubmit}>
                <Box
                  sx={{
                    display: 'flex',
                    gap: 2,
                    flexDirection: { xs: 'column', sm: 'row' },
                  }}
                >
                  <TextField
                    fullWidth
                    variant="outlined"
                    placeholder={intl.formatMessage({
                      id: 'home.newsletter.emailPlaceholder',
                    })}
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: isDarkMode ? alpha('#fff', 0.05) : '#fff',
                        borderRadius: 2,
                        '& fieldset': {
                          borderColor: isDarkMode ? alpha('#fff', 0.1) : alpha('#000', 0.1),
                        },
                        '&:hover fieldset': {
                          borderColor: isDarkMode ? alpha('#fff', 0.2) : alpha('#000', 0.2),
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: theme.palette.primary.main,
                        },
                      },
                      '& .MuiInputBase-input': {
                        color: isDarkMode ? 'grey.100' : 'text.primary',
                        '&::placeholder': {
                          color: isDarkMode ? 'grey.400' : 'text.secondary',
                          opacity: 0.8,
                        },
                      },
                    }}
                  />
                  <Button
                    type="submit"
                    variant="contained"
                    size="large"
                    endIcon={<SendIcon />}
                    sx={{
                      minWidth: { sm: '180px' },
                      height: '56px',
                      borderRadius: 2,
                      textTransform: 'none',
                      fontSize: '1rem',
                      fontWeight: 600,
                      boxShadow: 'none',
                      background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.4)}`,
                        transform: 'translateY(-2px)',
                      },
                    }}
                  >
                    {intl.formatMessage({ id: 'home.newsletter.subscribe' })}
                  </Button>
                </Box>
              </form>
            </Grid>
          </Grid>
        </Paper>
      </Container>

      {/* Register Prompt Modal */}
      <RegisterPromptModal
        open={showRegisterPrompt}
        onClose={() => setShowRegisterPrompt(false)}
        userEmail={email}
      />
    </Box>
  );
};