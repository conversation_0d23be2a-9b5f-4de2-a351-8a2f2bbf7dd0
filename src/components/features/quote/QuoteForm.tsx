import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  Grid,
  IconButton,
  FormControlLabel,
  Checkbox,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  SelectChangeEvent,
  Stepper,
  Step,
  StepLabel,
  Link,
  useTheme,
  useMediaQuery,
  CircularProgress,
  CardContent,
  Card,
} from "@mui/material";
import CityDistrictSelector from "../../common/CityDistrictSelector";
import {
  Close as CloseIcon,
  ErrorOutline as ErrorOutlineIcon,
  NavigateNext as NavigateNextIcon,
  NavigateBefore as NavigateBeforeIcon,
} from "@mui/icons-material";
import { useIntl } from "react-intl";
import { useCategories } from "../../../contexts/CategoryContext";
import { useAuth } from "../../../contexts/AuthContext";
import { alpha } from "@mui/material/styles";
import axios from "axios";
import api from "../../../utils/api";
import { formService } from "../../../services/formService";
import RegisterPromptModal from "../../common/RegisterPromptModal";
import { useLocation } from "react-router-dom";

// KVKK Dialog Component
const KVKKDialog: React.FC<{ open: boolean; onClose: () => void }> = ({
  open,
  onClose,
}) => {
  const theme = useTheme();
  const intl = useIntl();
  const isDarkMode = theme.palette.mode === "dark";

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          bgcolor: isDarkMode ? "background.paper" : "#fff",
          color: isDarkMode ? "grey.100" : "text.primary",
        },
      }}
    >
      <DialogTitle
        sx={{
          bgcolor: isDarkMode ? "background.paper" : "#fff",
          borderBottom: `1px solid ${
            isDarkMode ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.1)"
          }`,
        }}
      >
        <Typography
          variant="h6"
          sx={{
            color: isDarkMode ? "primary.light" : "primary.main",
            fontWeight: 600,
            fontSize: "1.25rem",
          }}
        >
          {intl.formatMessage({ id: "form.kvkkTitle" })}
        </Typography>
        <IconButton
          onClick={onClose}
          sx={{
            position: "absolute",
            right: 8,
            top: 8,
            color: isDarkMode ? "grey.400" : "grey.500",
            "&:hover": {
              bgcolor: isDarkMode
                ? "rgba(255, 255, 255, 0.04)"
                : "rgba(0, 0, 0, 0.04)",
            },
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        <Box sx={{ p: 2 }}>
          <Typography
            variant="body1"
            paragraph
            sx={{ color: isDarkMode ? "grey.300" : "text.primary" }}
          >
            {intl.formatMessage({ id: "form.kvkkIntro" })}
          </Typography>

          <Typography
            variant="h6"
            gutterBottom
            sx={{
              mt: 2,
              color: isDarkMode ? "primary.light" : "primary.main",
              fontWeight: 600,
              fontSize: "1.1rem",
            }}
          >
            {intl.formatMessage({ id: "form.kvkkSection1Title" })}
          </Typography>
          <Typography
            variant="body1"
            paragraph
            sx={{ color: isDarkMode ? "grey.300" : "text.primary" }}
          >
            {intl.formatMessage({ id: "form.kvkkSection1Text" })}
          </Typography>

          <Typography
            variant="h6"
            gutterBottom
            sx={{
              mt: 2,
              color: isDarkMode ? "primary.light" : "primary.main",
              fontWeight: 600,
              fontSize: "1.1rem",
            }}
          >
            {intl.formatMessage({ id: "form.kvkkSection2Title" })}
          </Typography>
          <Typography
            variant="body1"
            paragraph
            sx={{ color: isDarkMode ? "grey.300" : "text.primary" }}
          >
            {intl.formatMessage({ id: "form.kvkkSection2Text" })}
          </Typography>

          <Typography
            variant="h6"
            gutterBottom
            sx={{
              mt: 2,
              color: isDarkMode ? "primary.light" : "primary.main",
              fontWeight: 600,
              fontSize: "1.1rem",
            }}
          >
            {intl.formatMessage({ id: "form.kvkkSection3Title" })}
          </Typography>
          <Typography
            variant="body1"
            paragraph
            sx={{ color: isDarkMode ? "grey.300" : "text.primary" }}
          >
            {intl.formatMessage({ id: "form.kvkkSection3Text" })}
          </Typography>

          <Typography
            variant="h6"
            gutterBottom
            sx={{
              mt: 2,
              color: isDarkMode ? "primary.light" : "primary.main",
              fontWeight: 600,
              fontSize: "1.1rem",
            }}
          >
            {intl.formatMessage({ id: "form.kvkkSection4Title" })}
          </Typography>
          <Typography
            variant="body1"
            paragraph
            sx={{ color: isDarkMode ? "grey.300" : "text.primary" }}
          >
            {intl.formatMessage({ id: "form.kvkkSection4Text" })}
          </Typography>

          <Typography
            variant="body1"
            paragraph
            sx={{
              mt: 2,
              color: isDarkMode ? "grey.300" : "text.primary",
              "& a": {
                color: isDarkMode ? "primary.light" : "primary.main",
                textDecoration: "none",
                "&:hover": {
                  textDecoration: "underline",
                },
              },
            }}
          >
            {intl
              .formatMessage({ id: "form.kvkkContact" })
              .split("<EMAIL>")
              .map((part, index, array) => (
                <React.Fragment key={index}>
                  {part}
                  {index < array.length - 1 && (
                    <Link
                      href="mailto:<EMAIL>"
                      sx={{ ml: 0.5, mr: 0.5 }}
                    >
                      <EMAIL>
                    </Link>
                  )}
                </React.Fragment>
              ))}
          </Typography>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

interface CampaignDetailField {
  type: "text" | "radio" | "checkbox";
  label: string;
  value: string | boolean | string[];
  options?: string[];
  required?: boolean;
}

interface QuoteFormProps {
  open: boolean;
  onClose: () => void;
  campaignDetails?: {
    id: string;
    title: string;
    category: string;
    brand: string;
    imageUrl: string;
    description?: string;
    descriptionTitle?: string;
    title2?: string;
    description2?: string;
    title3?: string;
    description3?: string;
    endDate?: string;
    discount?: string;
    conditions?: string[];
    features?: Record<string, unknown>;
    stats?: Record<string, unknown>;
    details?: Record<string, CampaignDetailField>;
    campaignUrl?: string;
    logoUrl?: string;
    brandUrl?: string;
    brandId?: string;
  };
  isNavbar?: boolean;
  isOverlay?: boolean;
}

const QuoteForm: React.FC<QuoteFormProps> = ({
  open,
  onClose,
  campaignDetails,
  isNavbar = false,
  isOverlay = false,
}) => {
  const intl = useIntl();
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === "dark";
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const { user, isAuthenticated } = useAuth();
  const [activeStep, setActiveStep] = useState(0);

  // Ref for scrolling to form fields on mobile
  const formFieldsRef = React.useRef<HTMLDivElement>(null);
  const [showRegisterPrompt, setShowRegisterPrompt] = useState(false);
  const [formData, setFormData] = useState({
    mainCategory: campaignDetails?.category || "",
    subCategory: "",
    selectedCampaign: campaignDetails?.id || "",
    firstName: "",
    lastName: "",
    email: "",
    phone: "",

    consent: false,
    city: "",
    district: "",
  });
  const [kvkkDialogOpen, setKvkkDialogOpen] = useState(false);
  const [campaigns, setCampaigns] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [formErrors, setFormErrors] = useState<{
    email?: string;
    general?: string;
  }>({});
  const [errorDialogOpen, setErrorDialogOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [selectedSubCategories, setSelectedSubCategories] = useState<string[]>(
    []
  );
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const location = useLocation();

  // Add state for campaign images and navigation
  const [campaignImages, setCampaignImages] = useState<
    { url: string; isShowcase: boolean }[]
  >([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Add state for selected campaign details in navbar mode
  const [selectedCampaignDetails, setSelectedCampaignDetails] =
    useState<any>(null);

  // Function to fetch selected campaign details
  const fetchSelectedCampaignDetails = async (campaignId: string) => {
    try {
      if (!campaignId || !campaigns.length) return;

      const campaign = campaigns.find((c) => c.id.toString() === campaignId);
      if (!campaign) return;

      // Get category info
      const categoryName =
        flattenedCategories.find((cat) => cat.id === campaign.category?.id)
          ?.name ||
        campaign.category?.name ||
        "";

      setSelectedCampaignDetails({
        id: campaign.id.toString(),
        title: campaign.name || campaign.title,
        category: categoryName,
        brand:
          campaign.brand?.name ||
          campaign.brandName ||
          campaign.brand ||
          "Bilinmeyen Marka",
        imageUrl: campaign.imageUrl || "/placeholder-image.jpg",
        description: campaign.description || "",
        descriptionTitle: campaign.descriptionTitle,
        title2: campaign.title2,
        description2: campaign.description2,
        title3: campaign.title3,
        description3: campaign.description3,
        endDate: campaign.endDate,
        discount: campaign.discount ? `%${campaign.discount}` : undefined,
        conditions: campaign.conditions,
        details: campaign.details,
        stats: campaign.stats,
        campaignUrl: campaign.campaignUrl,
        logoUrl: campaign.logoUrl,
        brandUrl: campaign.brandUrl,
        brandId: campaign.brandId?.toString(),
      });
    } catch (error) {
      console.error("Error fetching campaign details:", error);
    }
  };

  // Context'ten kategorileri al
  const { categoryGroups, flattenedCategories } = useCategories();

  // Ana kategorileri filtrele (parentCategoryId = 0 olanlar)
  const mainCategories = flattenedCategories.filter(
    (cat) => cat.parentCategoryId === 0
  );

  // Seçilen kategorinin alt kategorilerini bul
  const selectedCategory = formData.mainCategory
    ? flattenedCategories.find(
        (cat) => cat.id.toString() === formData.mainCategory
      )
    : null;

  // Zincirli alt kategori mantığı
  const getSubCategories = (parentId: string | number) =>
    flattenedCategories.filter(
      (cat) => cat.parentCategoryId === Number(parentId)
    );

  const subCategories = selectedCategory
    ? getSubCategories(selectedCategory.id)
    : [];

  // Adımları dinamik olarak oluştur
  const getSteps = () => {
    const steps = [intl.formatMessage({ id: "steps.categorySelection" })];
    if (subCategories.length > 0) {
      steps.push(intl.formatMessage({ id: "steps.subCategorySelection" }));
    }
    steps.push(
      intl.formatMessage({ id: "steps.campaignSelection" }),
      intl.formatMessage({ id: "steps.contactInfo" })
    );
    return steps;
  };

  // steps her render'da güncel olmalı
  const steps = isNavbar
    ? getSteps()
    : [intl.formatMessage({ id: "steps.contactInfo" })];

  // Kampanyaları getir
  const fetchCampaigns = async (categoryId: string) => {
    try {
      setLoading(true);

      // Kampanyaları ve marka ilişkilerini paralel olarak çek
      const [campaignResponse, brandCampaignResponse] = await Promise.all([
        axios.get("https://360avantajli.com/api/Campaign_Service/campaign", { timeout: 15000 }),
        axios.get(
          "https://360avantajli.com/api/Campaign_Service/brand-to-campaign",
          { timeout: 15000 }
        ),
      ]);

      if (campaignResponse.data && Array.isArray(campaignResponse.data)) {
        const filteredCampaigns = campaignResponse.data.filter(
          (c: any) =>
            c.category?.id?.toString() === categoryId && c.isActive === true
        );

        // Her kampanya için marka bilgisini ekle
        const campaignsWithBrands = filteredCampaigns.map((campaign: any) => {
          // Marka-kampanya ilişkisini bul
          const brandCampaign = brandCampaignResponse.data?.find(
            (bc: any) => bc.campaign?.id === campaign.id && bc.isActive === true
          );

          // Marka bilgisini ekle
          const brandName =
            brandCampaign?.brand?.name || campaign.brand || "Bilinmeyen Marka";

          return {
            ...campaign,
            brand: brandName,
            brandId: brandCampaign?.brand?.id?.toString() || campaign.brandId,
            logoUrl: brandCampaign?.brand?.logoUrl || campaign.logoUrl,
            brandUrl: brandCampaign?.brand?.brandUrl || campaign.brandUrl,
          };
        });

        setCampaigns(campaignsWithBrands);
      }
    } catch (error) {
      console.error(
        intl.formatMessage({ id: "form.error.campaignFetchFailed" }),
        error
      );
    } finally {
      setLoading(false);
    }
  };

  // Form verilerini sıfırlama fonksiyonu
  const resetForm = () => {
    setFormErrors({});
    setFormData({
      mainCategory: "",
      subCategory: "",
      selectedCampaign: "",
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      consent: false,
      city: "",
      district: "",
    });
    setActiveStep(0);
    setCampaigns([]);
    setIsSubmitted(false);
    setSelectedSubCategories([]);
    setSelectedCampaignDetails(null);
  };

  // Modal açıldığında formu sıfırla ve scroll'u kilitle
  React.useEffect(() => {
    if (open) {
      resetForm();
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [open]);

  // Kullanıcı giriş yapmış ve profil bilgileri varsa form açıldığında otomatik doldur
  useEffect(() => {
    const fillProfileData = async () => {
      if (open && isAuthenticated && user?.username) {
        try {
          const response = await api.get(
            "/api/Auth_Service/customer/profile",
            { timeout: 10000 }
          );
          if (response.data) {
            const customerData = response.data;
            setFormData((prev) => ({
              ...prev,
              firstName: customerData.name || "",
              lastName: customerData.surname || "",
              email: customerData.email || "",
              phone: customerData.phoneNumber || "",
              city: customerData.city || "",
              district: customerData.town || "",
            }));
          }
        } catch (error) {
          // Profil bilgisi alınamazsa sessizce devam et
        }
      }
    };
    fillProfileData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, isAuthenticated, user?.username]);

  useEffect(() => {
    setErrorDialogOpen(false);
    setShowSuccessDialog(false);
    setShowRegisterPrompt(false);
    setFormData({
      mainCategory: "",
      subCategory: "",
      selectedCampaign: "",
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      consent: false,
      city: "",
      district: "",
    });
    setActiveStep(0);
    setIsSubmitted(false);
    setCampaigns([]);
    setSelectedSubCategories([]);
  }, [location.pathname, intl.locale]);

  const handleClose = () => {
    // Loading sırasında form kapatılmasını engelle
    if (loading) return;

    resetForm();
    setShowSuccessDialog(false);
    setErrorDialogOpen(false);
    setShowRegisterPrompt(false);
    document.body.style.overflow = "auto";
    onClose();
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsSubmitted(false);
    const { name, value, checked } = event.target;
    if (name === "phone") {
      let numericValue = value.replace(/[^0-9]/g, "");

      // Eğer alan boşsa ve kullanıcı herhangi bir rakam yazdıysa, direkt 5 ile başlat
      if (formData.phone === "" && numericValue.length > 0) {
        numericValue = "5";
      }
      // Eğer alan zaten dolu ve 5 ile başlıyorsa, normal şekilde devam et
      else if (numericValue.startsWith("5")) {
        numericValue = numericValue.slice(0, 10);
      }
      // Eğer 5 ile başlamıyorsa ve alan doluysa, mevcut değeri koru
      else if (formData.phone.length > 0) {
        numericValue = formData.phone;
      }

      setFormData({
        ...formData,
        phone: numericValue,
      });
    } else {
      setFormData({
        ...formData,
        [name]: name === "consent" ? checked : value,
      });
    }
  };

  // Kategori değiştiğinde alt adımları sıfırla
  const handleCategoryChange = (event: SelectChangeEvent) => {
    const newCategory = event.target.value;
    setFormData({
      ...formData,
      mainCategory: newCategory,
      subCategory: "",
      selectedCampaign: "",
    });
    setSelectedSubCategories([]);
    setActiveStep(1); // Her zaman bir sonraki adıma geç
  };

  // Zincirli alt kategori değişimi
  const handleSubCategoryChange = (level: number, value: string) => {
    // Seçili alt kategorileri güncelle
    const updated = [...selectedSubCategories];
    updated[level] = value;
    // Sonraki seviyeleri temizle
    updated.length = level + 1;
    setSelectedSubCategories(updated);

    const newFormData = {
      ...formData,
      subCategory: value,
      selectedCampaign: "",
    };

    setFormData(newFormData);

    // Otomatik olarak bir sonraki adıma geç (kampanya seçim ekranına)
    const nextStepIndex = steps.findIndex(
      (step) => step === intl.formatMessage({ id: "steps.campaignSelection" })
    );
    if (nextStepIndex !== -1) {
      // Alt kategorinin alt kategorisi yoksa veya son seviyedeysek kampanya seçim ekranına geç
      const nextSubCategories = getSubCategories(value);
      if (nextSubCategories.length === 0 || level > 0) {
        setActiveStep(nextStepIndex);
        // Seçili alt kategori için kampanyaları getir
        fetchCampaigns(value);
      }
    }
  };

  // Kampanya seçildiğinde iletişim adımına geç
  const handleCampaignChange = (event: SelectChangeEvent) => {
    const newCampaign = event.target.value;
    setFormData({
      ...formData,
      selectedCampaign: newCampaign,
    });

    // Fetch campaign details for navbar mode
    if (isNavbar && newCampaign) {
      fetchSelectedCampaignDetails(newCampaign);
    }

    setActiveStep(steps.length - 1); // Son adıma geç
  };

  // Devam Et butonu
  const handleNext = () => {
    // Önce mevcut adımın geçerli olup olmadığını kontrol et
    if (!isStepValid()) {
      // Geçersizse form alanlarında hata göster
      setIsSubmitted(true);
      return;
    }

    if (activeStep === steps.length - 1) {
      handleSubmit();
    } else {
      setActiveStep((prev) => prev + 1);
    }
  };

  // Geri butonu
  const handleBack = () => {
    if (activeStep > 0) {
      setActiveStep((prev) => prev - 1);
    }
  };

  const validateForm = () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^[0-9]{10}$/;

    // Basit boolean kontrolü - popup yerine form alanlarında hata göstereceğiz
    const isValid =
      formData.firstName.trim() &&
      formData.lastName.trim() &&
      formData.email.trim() &&
      emailRegex.test(formData.email) &&
      formData.phone.trim() &&
      phoneRegex.test(formData.phone) &&
      formData.city.trim() &&
      formData.district.trim() &&
      formData.consent &&
      (!isNavbar || formData.selectedCampaign);

    return isValid;
  };

  const handleSubmit = async () => {
    setIsSubmitted(true);
    // Form hatalarını temizle
    setFormErrors({});

    // Form validasyonunu kontrol et - artık sadece boolean döndürüyor
    if (!validateForm()) {
      // Form geçersiz - hata mesajları zaten form alanlarında gösteriliyor
      return;
    }

    // Eğer form valid değilse, gönderme
    if (!isStepValid()) return;

    try {
      setLoading(true);

      // Form verilerini hazırla
      const formPayload = {
        name: formData.firstName,
        surname: formData.lastName,
        email: formData.email,
        phoneNumber: formData.phone,
        country: "Türkiye", // Varsayılan değer
        city: formData.city,
        town: formData.district,
        gender: "", // Eğer cinsiyet alanı varsa buraya eklenebilir
        birthday: "", // Eğer doğum tarihi alanı varsa buraya eklenebilir
        isActive: true,
      };

      // Kampanya ID'sini al
      const campaignId = campaignDetails?.id || formData.selectedCampaign;

      if (!campaignId) {
        throw new Error(
          intl.formatMessage({ id: "form.error.campaignIdMissing" })
        );
      }

      // Duplicate kontrolü yap
      const isDuplicate = await formService.checkDuplicateSubmission(
        formData.email,
        parseInt(campaignId)
      );

      if (isDuplicate) {
        setErrorMessage(
          intl.formatMessage({ id: "form.error.duplicateSubmission" }) || 
          "Bu kampanya için daha önce teklif aldınız. Başka bir kampanya seçebilirsiniz."
        );
        setErrorDialogOpen(true);
        return;
      }

      // Form ve kampanya ilişkisini oluştur
      await formService.submitFormAndCreateCampaignRelationV2(
        formPayload,
        parseInt(campaignId),
        isAuthenticated,
        user?.username
      );

      // Başarılı işlem sonrası
      setShowSuccessDialog(true);
      setTimeout(() => {
        onClose();
        resetForm();
      }, 2000);
      setIsSubmitted(false);

      // Eğer kullanıcı giriş yapmamışsa veya navbar'dan geliyorsa, kayıt olmaya teşvik et
      if (isNavbar || !isAuthenticated) {
        setShowRegisterPrompt(true);
      }
    } catch (error: any) {
      // Hata mesajını daha spesifik hale getir
      let errorMsg = error.message || intl.formatMessage({ id: "form.error.submitFailed" });
      
      // Backend'den gelen hata mesajlarını kontrol et
      if (error.response?.data?.error) {
        errorMsg = error.response.data.error;
      } else if (error.response?.status === 500) {
        errorMsg = intl.formatMessage({ id: "form.error.serverError" }) || 
                  "Sunucu hatası oluştu. Lütfen daha sonra tekrar deneyiniz.";
      }
      
      setErrorMessage(errorMsg);
      setErrorDialogOpen(true);
    } finally {
      setLoading(false);
    }
  };

  const isStepValid = () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^[0-9]{10}$/;

    switch (activeStep) {
      case 0:
        // Ana kategori seçimi zorunlu - boş ise sonraki adıma geçemez
        return isNavbar
          ? !!formData.mainCategory
          : !!formData.firstName &&
              !!formData.lastName &&
              emailRegex.test(formData.email) &&
              phoneRegex.test(formData.phone) &&
              formData.consent &&
              !!formData.city &&
              !!formData.district;
      case 1:
        // Alt kategori adımı sadece alt kategori varsa gösterilir
        // Alt kategori varsa seçilmiş olmalı, yoksa kampanya seçimi gerekli
        return subCategories.length > 0
          ? !!formData.subCategory
          : !!formData.selectedCampaign;
      case 2:
        // Alt kategori yoksa kampanya seçimi, varsa iletişim bilgileri
        // Kampanya seçimi zorunlu
        return subCategories.length > 0
          ? !!formData.selectedCampaign
          : !!formData.firstName &&
              !!formData.lastName &&
              emailRegex.test(formData.email) &&
              phoneRegex.test(formData.phone) &&
              formData.consent &&
              !!formData.city &&
              !!formData.district;
      case 3:
        // İletişim bilgileri validasyonu
        return (
          !!formData.firstName &&
          !!formData.lastName &&
          emailRegex.test(formData.email) &&
          phoneRegex.test(formData.phone) &&
          formData.consent &&
          !!formData.city &&
          !!formData.district
        );
      default:
        return false;
    }
  };

  // Alt kategori zinciri değiştiğinde kampanyaları getir - sadece form açıkken
  useEffect(() => {
    if (!open) return; // Form açık değilse API çağrısı yapma
    
    // Zincirin en sonundaki alt kategoriye göre kampanya getir
    const lastSub = selectedSubCategories[selectedSubCategories.length - 1];
    if (lastSub) {
      fetchCampaigns(lastSub);
    } else if (formData.mainCategory && subCategories.length === 0) {
      fetchCampaigns(formData.mainCategory);
    }
  }, [selectedSubCategories, formData.mainCategory, subCategories.length, open]);

  // Add function to fetch campaign images
  const fetchCampaignImages = async (campaignId: string) => {
    try {
      if (!campaignId) return;

      // Add showcase image
      const showcase = {
        isShowcase: true,
        url: `https://360avantajli.com/api/Campaign_Service/campaign-image/showcase/${campaignId}`,
      };

      // Fetch detail images with timeout
      const detailUrls = await Promise.race([
        getDetailImageUrls(campaignId),
        new Promise<string[]>((resolve) => 
          setTimeout(() => resolve([]), 5000) // 5 saniye timeout
        )
      ]);
      
      const details = detailUrls.slice(0, 5).map((url) => ({ isShowcase: false, url }));

      setCampaignImages([showcase, ...details]);
      setCurrentImageIndex(0); // Reset to first image
    } catch (error) {
      console.error("Error fetching campaign images:", error);
      // If error fetching images, use the default imageUrl from current campaign
      const fallbackImageUrl = currentCampaign?.imageUrl;
      if (fallbackImageUrl) {
        setCampaignImages([
          {
            isShowcase: true,
            url: fallbackImageUrl,
          },
        ]);
      }
    }
  };

  // Function to get detail images
  const getDetailImageUrls = async (campaignId: string): Promise<string[]> => {
    if (!campaignId) return [];
    try {
      const response = await axios.get(
        `https://360avantajli.com/api/Campaign_Service/campaign-image/details/${campaignId}`,
        { timeout: 10000 }
      );
      if (typeof response.data === "string") {
        const parser = new DOMParser();
        const doc = parser.parseFromString(response.data, "text/html");
        const images = Array.from(doc.getElementsByTagName("img"));
        return images.map((img) =>
          img.src.replace(
            "https://360avantajli.com/Image_Service",
            "https://360avantajli.com/api/Image_Service"
          )
        );
      }
    } catch (e) {
      console.error("Error fetching detail images:", e);
    }
    return [];
  };

  // Navigation functions for images
  const handlePreviousImage = () => {
    setCurrentImageIndex((prev) =>
      prev === 0 ? campaignImages.length - 1 : prev - 1
    );
  };

  const handleNextImage = () => {
    setCurrentImageIndex((prev) =>
      prev === campaignImages.length - 1 ? 0 : prev + 1
    );
  };

  const handleThumbnailClick = (index: number) => {
    setCurrentImageIndex(index);
  };

  // Get current campaign (from props or selected)
  const currentCampaign =
    campaignDetails || (isNavbar ? selectedCampaignDetails : null);

  // State for dynamic campaign URL
  const [dynamicCampaignUrl, setDynamicCampaignUrl] = useState<string>("");
  const [urlLoading, setUrlLoading] = useState<boolean>(false);

  // Fetch campaign URL dynamically (with separate loading state) - sadece form açıkken
  useEffect(() => {
    if (!open) return; // Form açık değilse API çağrısı yapma
    
    const fetchCampaignUrl = async () => {
      if (!currentCampaign?.id) return;

      try {
        setUrlLoading(true);
        const campaignUrlsResponse = await axios.get(
          "https://360avantajli.com/api/Campaign_Service/campaign-url",
          { timeout: 15000 }
        );

        if (
          campaignUrlsResponse.data &&
          Array.isArray(campaignUrlsResponse.data)
        ) {
          const activeCampaignUrlEntry = campaignUrlsResponse.data.find(
            (urlEntry: any) =>
              urlEntry.campaign?.id?.toString() ===
                currentCampaign.id.toString() && urlEntry.isActive === true
          );

          if (activeCampaignUrlEntry && activeCampaignUrlEntry.url) {
            setDynamicCampaignUrl(activeCampaignUrlEntry.url);
          } else {
            setDynamicCampaignUrl("");
          }
        }
      } catch (error) {
        console.error("Error fetching campaign URL:", error);
        setDynamicCampaignUrl("");
      } finally {
        setUrlLoading(false);
      }
    };

    fetchCampaignUrl();
  }, [currentCampaign?.id, open]);

  // Fetch campaign images when campaign details change - sadece form açıkken
  useEffect(() => {
    if (!open) return; // Form açık değilse API çağrısı yapma
    
    if (currentCampaign?.id) {
      fetchCampaignImages(currentCampaign.id);
    }
  }, [currentCampaign?.id, open]);

  const getStepContent = (step: number) => {
    if (!isNavbar) {
      return (
        <Box sx={{ mt: { xs: 1, md: 2 } }}>
          {" "}
          {/* Reduced top margin on mobile */}
          <Grid container spacing={{ xs: 1.5, md: 2 }} ref={formFieldsRef}>
            {" "}
            {/* Smaller spacing on mobile */}
            {campaignDetails && (
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  {campaignDetails.title}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {campaignDetails.brand} - {campaignDetails.category}
                </Typography>
              </Grid>
            )}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: "form.firstName" })}
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                required
                error={isSubmitted && !formData.firstName}
                helperText={
                  isSubmitted && !formData.firstName
                    ? intl.formatMessage({
                        id: "form.validation.firstNameRequired",
                      })
                    : ""
                }
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: "form.lastName" })}
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                required
                error={isSubmitted && !formData.lastName}
                helperText={
                  isSubmitted && !formData.lastName
                    ? intl.formatMessage({
                        id: "form.validation.lastNameRequired",
                      })
                    : ""
                }
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: "form.email" })}
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                placeholder="<EMAIL>"
                error={
                  isSubmitted &&
                  (!formData.email ||
                    !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email))
                }
                helperText={
                  isSubmitted && !formData.email
                    ? intl.formatMessage({
                        id: "form.validation.emailRequired",
                      })
                    : isSubmitted &&
                      !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)
                    ? intl.formatMessage({ id: "form.invalidEmail" })
                    : ""
                }
                disabled={isAuthenticated}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: "form.phone" })}
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                required
                placeholder="5xxxxxxxxx"
                inputProps={{
                  maxLength: 10,
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                }}
                error={
                  isSubmitted &&
                  (!formData.phone || !/^[0-9]{10}$/.test(formData.phone))
                }
                helperText={
                  isSubmitted && !formData.phone
                    ? intl.formatMessage({
                        id: "form.validation.phoneRequired",
                      })
                    : isSubmitted && !/^[0-9]{10}$/.test(formData.phone)
                    ? intl.formatMessage({ id: "form.invalidPhone" })
                    : ""
                }
              />
            </Grid>
            <Grid item xs={12}>
              <CityDistrictSelector
                cityValue={formData.city}
                districtValue={formData.district}
                onCityChange={(city) => setFormData({ ...formData, city })}
                onDistrictChange={(district) =>
                  setFormData({ ...formData, district })
                }
                cityError={
                  isSubmitted && !formData.city
                    ? intl.formatMessage({ id: "form.validation.cityRequired" })
                    : ""
                }
                districtError={
                  isSubmitted && !formData.district
                    ? intl.formatMessage({
                        id: "form.validation.districtRequired",
                      })
                    : ""
                }
                required
                size="medium"
                gridSize={{ city: 6, district: 6 }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.consent}
                    onChange={handleInputChange}
                    name="consent"
                    required
                  />
                }
                label={
                  <Typography
                    variant="body2"
                    sx={{ display: "inline", lineHeight: 1.5 }}
                  >
                    {intl.formatMessage({ id: "form.kvkkConsentPrefix" })}&nbsp;
                    <Link
                      component="button"
                      variant="body2"
                      onClick={handleKVKKClick}
                      sx={{
                        display: "inline",
                        p: 0,
                        m: 0,
                        verticalAlign: "baseline",
                      }}
                    >
                      {intl.formatMessage({ id: "form.kvkkConsentLink" })}
                    </Link>
                    &nbsp;{intl.formatMessage({ id: "form.kvkkConsentSuffix" })}
                  </Typography>
                }
              />
              {isSubmitted && !formData.consent && (
                <Typography variant="caption" color="error">
                  {intl.formatMessage({ id: "form.validation.kvkkRequired" })}
                </Typography>
              )}
            </Grid>
          </Grid>
        </Box>
      );
    }

    // Adımları dinamik olarak göster
    const currentStep = steps[step];
    if (currentStep === intl.formatMessage({ id: "steps.categorySelection" })) {
      return (
        <Box sx={{ mt: { xs: 1, md: 2 }, px: { xs: 2, md: 0 } }}>
          {/* Desktop: Select dropdown */}
          <Box sx={{ display: { xs: "none", md: "block" } }}>
            <FormControl
              fullWidth
              sx={{
                "& .MuiInputLabel-root": {
                  fontSize: "14px",
                  fontWeight: 400,
                  color: "rgba(0, 0, 0, 0.6)",
                },
                "& .MuiOutlinedInput-root": {
                  borderRadius: "4px",
                  backgroundColor: "transparent",
                },
                "& .MuiSelect-select": {
                  padding: "14px",
                  fontSize: "14px",
                  fontWeight: 400,
                },
              }}
            >
              <InputLabel>
                {intl.formatMessage({ id: "form.selectCategory" })}
              </InputLabel>
              <Select
                value={formData.mainCategory}
                onChange={handleCategoryChange}
                label={intl.formatMessage({ id: "form.selectCategory" })}
              >
                {mainCategories.map((category) => (
                  <MenuItem key={category.id} value={category.id.toString()}>
                    {intl.formatMessage({
                      id: `category.${category.name
                        .toLowerCase()
                        .replace(/\s+/g, "")}`,
                      defaultMessage: category.name,
                    })}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>

          {/* Mobile: Card-based selection */}
          <Box sx={{ display: { xs: "block", md: "none" } }}>
            <Typography
              variant="h6"
              sx={{
                mb: 2,
                fontSize: "18px",
                fontWeight: 600,
                color: "#1976d2",
                textAlign: "center",
              }}
            >
              {intl.formatMessage({ id: "form.selectCategory" })}
            </Typography>

            {/* Scrollable container for many categories */}
            <Box
              sx={{
                maxHeight: "60vh", // Limit height to 60% of viewport
                overflowY: "auto",
                overflowX: "hidden",
                pr: 1, // Padding for scrollbar
                "&::-webkit-scrollbar": {
                  width: "6px",
                },
                "&::-webkit-scrollbar-track": {
                  backgroundColor: "#f1f1f1",
                  borderRadius: "3px",
                },
                "&::-webkit-scrollbar-thumb": {
                  backgroundColor: "#c1c1c1",
                  borderRadius: "3px",
                  "&:hover": {
                    backgroundColor: "#a8a8a8",
                  },
                },
              }}
            >
              <Grid container spacing={2}>
                {mainCategories.map((category) => {
                  const isSelected =
                    formData.mainCategory === category.id.toString();
                  return (
                    <Grid item xs={6} key={category.id}>
                      <Card
                        onClick={() => {
                          const event = {
                            target: { value: category.id.toString() },
                          } as SelectChangeEvent;
                          handleCategoryChange(event);
                        }}
                        sx={{
                          minHeight: 80, // Reduced from 100px
                          maxHeight: 120, // Max height to prevent huge cards
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          cursor: "pointer",
                          borderRadius: 3,
                          border: isSelected
                            ? "3px solid #1976d2"
                            : "2px solid #e0e0e0", // Daha belirgin border
                          backgroundColor: isSelected ? "#e3f2fd" : "#fafafa",
                          boxShadow: isSelected
                            ? "0 6px 24px rgba(25, 118, 210, 0.2)"
                            : "0 2px 8px rgba(0, 0, 0, 0.08)", // Varsayılan shadow
                          transition: "all 0.3s ease",
                          "&:hover": {
                            transform: "translateY(-2px)",
                            boxShadow: isSelected
                              ? "0 8px 32px rgba(25, 118, 210, 0.25)"
                              : "0 4px 16px rgba(0, 0, 0, 0.12)", // Daha güçlü hover shadow
                            backgroundColor: isSelected ? "#bbdefb" : "#f0f0f0",
                            borderColor: isSelected ? "#1976d2" : "#bdbdbd",
                          },
                        }}
                      >
                        <CardContent
                          sx={{
                            textAlign: "center",
                            p: 1.5,
                            "&:last-child": { pb: 1.5 },
                          }}
                        >
                          <Typography
                            variant="body2"
                            sx={{
                              fontWeight: isSelected ? 600 : 500,
                              color: isSelected ? "#1976d2" : "#333",
                              fontSize: "13px",
                              lineHeight: 1.2,
                              overflow: "hidden",
                              textOverflow: "ellipsis",
                              display: "-webkit-box",
                              WebkitLineClamp: 3, // Max 3 lines
                              WebkitBoxOrient: "vertical",
                              wordBreak: "break-word",
                            }}
                          >
                            {intl.formatMessage({
                              id: `category.${category.name
                                .toLowerCase()
                                .replace(/\s+/g, "")}`,
                              defaultMessage: category.name,
                            })}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  );
                })}
              </Grid>
            </Box>

            {/* Show count if many categories */}
            {mainCategories.length > 8 && (
              <Typography
                variant="caption"
                sx={{
                  display: "block",
                  textAlign: "center",
                  mt: 1,
                  color: "#666",
                  fontSize: "12px",
                }}
              >
                {mainCategories.length} kategori mevcut
              </Typography>
            )}
          </Box>
        </Box>
      );
    }

    if (
      currentStep ===
        intl.formatMessage({ id: "steps.subCategorySelection" }) &&
      subCategories.length > 0
    ) {
      return (
        <Box sx={{ mt: 2, px: { xs: 2, md: 0 } }}>
          {/* Desktop: Select dropdown */}
          <Box sx={{ display: { xs: "none", md: "block" } }}>
            <FormControl
              fullWidth
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": {
                  fontSize: "14px",
                  fontWeight: 400,
                  color: "rgba(0, 0, 0, 0.6)",
                },
                "& .MuiOutlinedInput-root": {
                  borderRadius: "4px",
                  backgroundColor: "transparent",
                },
                "& .MuiSelect-select": {
                  padding: "14px",
                  fontSize: "14px",
                  fontWeight: 400,
                },
              }}
            >
              <InputLabel>
                {intl.formatMessage({ id: "form.selectSubCategory" })}
              </InputLabel>
              <Select
                value={selectedSubCategories[0] || ""}
                onChange={(e) => handleSubCategoryChange(0, e.target.value)}
                label={intl.formatMessage({ id: "form.selectSubCategory" })}
              >
                {subCategories.map((category) => (
                  <MenuItem key={category.id} value={category.id.toString()}>
                    {intl.formatMessage({
                      id: `category.${category.name
                        .toLowerCase()
                        .replace(/\s+/g, "")}`,
                      defaultMessage: category.name,
                    })}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Desktop: Dynamic chained subcategory selects */}
            {selectedSubCategories.map((selected, idx) => {
              const nextSubCategories = getSubCategories(selected);
              if (nextSubCategories.length === 0) return null;
              return (
                <FormControl
                  fullWidth
                  sx={{
                    mb: 2,
                    "& .MuiInputLabel-root": {
                      fontSize: "14px",
                      fontWeight: 400,
                      color: "rgba(0, 0, 0, 0.6)",
                    },
                    "& .MuiOutlinedInput-root": {
                      borderRadius: "4px",
                      backgroundColor: "transparent",
                    },
                    "& .MuiSelect-select": {
                      padding: "14px",
                      fontSize: "14px",
                      fontWeight: 400,
                    },
                  }}
                  key={idx + 1}
                >
                  <InputLabel>
                    {intl.formatMessage({ id: "form.selectSubCategory" })}
                  </InputLabel>
                  <Select
                    value={selectedSubCategories[idx + 1] || ""}
                    onChange={(e) =>
                      handleSubCategoryChange(idx + 1, e.target.value)
                    }
                    label={intl.formatMessage({ id: "form.selectSubCategory" })}
                  >
                    {nextSubCategories.map((category) => (
                      <MenuItem
                        key={category.id}
                        value={category.id.toString()}
                      >
                        {intl.formatMessage({
                          id: `category.${category.name
                            .toLowerCase()
                            .replace(/\s+/g, "")}`,
                          defaultMessage: category.name,
                        })}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              );
            })}
          </Box>

          {/* Mobile: Card-based selection */}
          <Box sx={{ display: { xs: "block", md: "none" } }}>
            <Typography
              variant="h6"
              sx={{
                mb: 2,
                fontSize: "18px",
                fontWeight: 600,
                color: "#1976d2",
                textAlign: "center",
              }}
            >
              {intl.formatMessage({ id: "form.selectSubCategory" })}
            </Typography>
            {/* Scrollable container for subcategories */}
            <Box
              sx={{
                maxHeight: "50vh",
                overflowY: "auto",
                overflowX: "hidden",
                pr: 1,
                "&::-webkit-scrollbar": {
                  width: "6px",
                },
                "&::-webkit-scrollbar-track": {
                  backgroundColor: "#f1f1f1",
                  borderRadius: "3px",
                },
                "&::-webkit-scrollbar-thumb": {
                  backgroundColor: "#c1c1c1",
                  borderRadius: "3px",
                  "&:hover": {
                    backgroundColor: "#a8a8a8",
                  },
                },
              }}
            >
              <Grid container spacing={2}>
                {subCategories.map((category) => {
                  const isSelected =
                    selectedSubCategories[0] === category.id.toString();
                  return (
                    <Grid item xs={6} key={category.id}>
                      <Card
                        onClick={() =>
                          handleSubCategoryChange(0, category.id.toString())
                        }
                        sx={{
                          minHeight: 80,
                          maxHeight: 120,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          cursor: "pointer",
                          borderRadius: 3,
                          border: isSelected
                            ? "3px solid #1976d2"
                            : "2px solid #e0e0e0", // Daha belirgin border
                          backgroundColor: isSelected ? "#e3f2fd" : "#fafafa",
                          boxShadow: isSelected
                            ? "0 6px 24px rgba(25, 118, 210, 0.2)"
                            : "0 2px 8px rgba(0, 0, 0, 0.08)", // Varsayılan shadow
                          transition: "all 0.3s ease",
                          "&:hover": {
                            transform: "translateY(-2px)",
                            boxShadow: isSelected
                              ? "0 8px 32px rgba(25, 118, 210, 0.25)"
                              : "0 4px 16px rgba(0, 0, 0, 0.12)", // Daha güçlü hover shadow
                            backgroundColor: isSelected ? "#bbdefb" : "#f0f0f0",
                            borderColor: isSelected ? "#1976d2" : "#bdbdbd",
                          },
                        }}
                      >
                        <CardContent
                          sx={{
                            textAlign: "center",
                            p: 1.5,
                            "&:last-child": { pb: 1.5 },
                          }}
                        >
                          <Typography
                            variant="body2"
                            sx={{
                              fontWeight: isSelected ? 600 : 500,
                              color: isSelected ? "#1976d2" : "#333",
                              fontSize: "13px",
                              lineHeight: 1.2,
                              overflow: "hidden",
                              textOverflow: "ellipsis",
                              display: "-webkit-box",
                              WebkitLineClamp: 3,
                              WebkitBoxOrient: "vertical",
                              wordBreak: "break-word",
                            }}
                          >
                            {intl.formatMessage({
                              id: `category.${category.name
                                .toLowerCase()
                                .replace(/\s+/g, "")}`,
                              defaultMessage: category.name,
                            })}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  );
                })}
              </Grid>
            </Box>

            {subCategories.length > 6 && (
              <Typography
                variant="caption"
                sx={{
                  display: "block",
                  textAlign: "center",
                  mt: 1,
                  color: "#666",
                  fontSize: "12px",
                }}
              >
                {subCategories.length} alt kategori mevcut
              </Typography>
            )}

            {/* Mobile: Dynamic chained subcategory cards */}
            {selectedSubCategories.map((selected, idx) => {
              const nextSubCategories = getSubCategories(selected);
              if (nextSubCategories.length === 0) return null;
              return (
                <Box key={idx + 1} sx={{ mt: 3 }}>
                  <Typography
                    variant="h6"
                    sx={{
                      mb: 2,
                      fontSize: "16px",
                      fontWeight: 600,
                      color: "#1976d2",
                      textAlign: "center",
                    }}
                  >
                    Alt Kategori Seçiniz
                  </Typography>
                  <Grid container spacing={2}>
                    {nextSubCategories.map((category) => {
                      const isSelected =
                        selectedSubCategories[idx + 1] ===
                        category.id.toString();
                      return (
                        <Grid item xs={6} key={category.id}>
                          <Card
                            onClick={() =>
                              handleSubCategoryChange(
                                idx + 1,
                                category.id.toString()
                              )
                            }
                            sx={{
                              minHeight: 100,
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              cursor: "pointer",
                              borderRadius: 3,
                              border: isSelected
                                ? "3px solid #1976d2"
                                : "2px solid #e0e0e0", // Daha belirgin border
                              backgroundColor: isSelected
                                ? "#e3f2fd"
                                : "#fafafa",
                              boxShadow: isSelected
                                ? "0 6px 24px rgba(25, 118, 210, 0.2)"
                                : "0 2px 8px rgba(0, 0, 0, 0.08)", // Varsayılan shadow
                              transition: "all 0.3s ease",
                              "&:hover": {
                                transform: "translateY(-2px)",
                                boxShadow: isSelected
                                  ? "0 8px 32px rgba(25, 118, 210, 0.25)"
                                  : "0 4px 16px rgba(0, 0, 0, 0.12)", // Daha güçlü hover shadow
                                backgroundColor: isSelected
                                  ? "#bbdefb"
                                  : "#f0f0f0",
                                borderColor: isSelected ? "#1976d2" : "#bdbdbd",
                              },
                            }}
                          >
                            <CardContent sx={{ textAlign: "center", p: 2 }}>
                              <Typography
                                variant="body1"
                                sx={{
                                  fontWeight: isSelected ? 600 : 500,
                                  color: isSelected ? "#1976d2" : "#333",
                                  fontSize: "14px",
                                  lineHeight: 1.3,
                                }}
                              >
                                {intl.formatMessage({
                                  id: `category.${category.name
                                    .toLowerCase()
                                    .replace(/\s+/g, "")}`,
                                  defaultMessage: category.name,
                                })}
                              </Typography>
                            </CardContent>
                          </Card>
                        </Grid>
                      );
                    })}
                  </Grid>
                </Box>
              );
            })}
          </Box>
        </Box>
      );
    }

    if (currentStep === intl.formatMessage({ id: "steps.campaignSelection" })) {
      return (
        <Box sx={{ mt: 2, px: { xs: 2, md: 0 } }}>
          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center", py: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              {/* Desktop: Select dropdown */}
              <Box sx={{ display: { xs: "none", md: "block" } }}>
                <FormControl
                  fullWidth
                  sx={{
                    "& .MuiInputLabel-root": {
                      fontSize: "14px",
                      fontWeight: 400,
                      color: "rgba(0, 0, 0, 0.6)",
                    },
                    "& .MuiOutlinedInput-root": {
                      borderRadius: "4px",
                      backgroundColor: "transparent",
                    },
                    "& .MuiSelect-select": {
                      padding: "14px",
                      fontSize: "14px",
                      fontWeight: 400,
                    },
                  }}
                >
                  <InputLabel>
                    {intl.formatMessage({ id: "form.selectCampaign" })}
                  </InputLabel>
                  <Select
                    value={formData.selectedCampaign}
                    onChange={handleCampaignChange}
                    label={intl.formatMessage({ id: "form.selectCampaign" })}
                  >
                    {campaigns.map((campaign) => (
                      <MenuItem
                        key={campaign.id}
                        value={campaign.id.toString()}
                      >
                        <Typography
                          variant="body2"
                          sx={{
                            whiteSpace: "normal",
                            wordWrap: "break-word",
                            lineHeight: 1.4,
                            fontSize: "14px",
                          }}
                        >
                          {campaign.title}
                        </Typography>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>

              {/* Mobile: Card-based selection */}
              <Box sx={{ display: { xs: "block", md: "none" } }}>
                <Typography
                  variant="h6"
                  sx={{
                    mb: 2,
                    fontSize: "18px",
                    fontWeight: 600,
                    color: "#1976d2",
                    textAlign: "center",
                  }}
                >
                  {intl.formatMessage({ id: "form.selectCampaign" })}
                </Typography>
                {/* Scrollable container for campaigns */}
                <Box
                  sx={{
                    maxHeight: "55vh",
                    overflowY: "auto",
                    overflowX: "hidden",
                    pr: 1,
                    "&::-webkit-scrollbar": {
                      width: "6px",
                    },
                    "&::-webkit-scrollbar-track": {
                      backgroundColor: "#f1f1f1",
                      borderRadius: "3px",
                    },
                    "&::-webkit-scrollbar-thumb": {
                      backgroundColor: "#c1c1c1",
                      borderRadius: "3px",
                      "&:hover": {
                        backgroundColor: "#a8a8a8",
                      },
                    },
                  }}
                >
                  <Grid container spacing={2}>
                    {campaigns.map((campaign) => {
                      const isSelected =
                        formData.selectedCampaign === campaign.id.toString();
                      return (
                        <Grid item xs={12} key={campaign.id}>
                          <Card
                            onClick={() => {
                              const event = {
                                target: { value: campaign.id.toString() },
                              } as SelectChangeEvent;
                              handleCampaignChange(event);
                            }}
                            sx={{
                              minHeight: 70,
                              maxHeight: 120,
                              display: "flex",
                              alignItems: "center",
                              cursor: "pointer",
                              borderRadius: 3,
                              border: isSelected
                                ? "3px solid #1976d2"
                                : "2px solid #e0e0e0", // Daha belirgin border
                              backgroundColor: isSelected
                                ? "#e3f2fd"
                                : "#fafafa",
                              boxShadow: isSelected
                                ? "0 6px 24px rgba(25, 118, 210, 0.2)"
                                : "0 2px 8px rgba(0, 0, 0, 0.08)", // Varsayılan shadow
                              transition: "all 0.3s ease",
                              "&:hover": {
                                transform: "translateY(-2px)",
                                boxShadow: isSelected
                                  ? "0 8px 32px rgba(25, 118, 210, 0.25)"
                                  : "0 4px 16px rgba(0, 0, 0, 0.12)", // Daha güçlü hover shadow
                                backgroundColor: isSelected
                                  ? "#bbdefb"
                                  : "#f0f0f0",
                                borderColor: isSelected ? "#1976d2" : "#bdbdbd",
                              },
                            }}
                          >
                            <CardContent
                              sx={{
                                flex: 1,
                                p: 1.5,
                                "&:last-child": { pb: 1.5 },
                              }}
                            >
                              <Typography
                                variant="body2"
                                sx={{
                                  fontWeight: isSelected ? 600 : 500,
                                  color: isSelected ? "#1976d2" : "#333",
                                  fontSize: "14px",
                                  lineHeight: 1.3,
                                  overflow: "hidden",
                                  textOverflow: "ellipsis",
                                  display: "-webkit-box",
                                  WebkitLineClamp: 3,
                                  WebkitBoxOrient: "vertical",
                                  wordBreak: "break-word",
                                }}
                              >
                                {campaign.title}
                              </Typography>
                            </CardContent>
                          </Card>
                        </Grid>
                      );
                    })}
                  </Grid>
                </Box>

                {campaigns.length > 5 && (
                  <Typography
                    variant="caption"
                    sx={{
                      display: "block",
                      textAlign: "center",
                      mt: 1,
                      color: "#666",
                      fontSize: "12px",
                    }}
                  >
                    {campaigns.length} kampanya mevcut
                  </Typography>
                )}
              </Box>
            </>
          )}
        </Box>
      );
    }

    if (currentStep === intl.formatMessage({ id: "steps.contactInfo" })) {
      // Sadece seçili kampanya ve marka bilgisi, ekstra başlık yok
      let selectedCampaignInfo: {
        title: string;
        brand: string;
        category: string;
      } | null = null;
      if (campaignDetails) {
        selectedCampaignInfo = {
          title: campaignDetails.title,
          brand: campaignDetails.brand,
          category: campaignDetails.category,
        };
      } else if (formData.selectedCampaign && campaigns.length > 0) {
        const found = campaigns.find(
          (c) => c.id.toString() === formData.selectedCampaign
        );
        if (found) {
          selectedCampaignInfo = {
            title: found.title,
            brand: found.brand || "-",
            category: found.category?.name || "-",
          };
        }
      }
      return (
        <Box sx={{ mt: 2 }}>
          <Grid container spacing={2} ref={formFieldsRef}>
            {selectedCampaignInfo && (
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  {intl.formatMessage({ id: "form.selectedCampaign" })}:{" "}
                  {selectedCampaignInfo.title}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {selectedCampaignInfo.brand} -{" "}
                  {selectedCampaignInfo.category &&
                    intl.formatMessage({
                      id: `category.${selectedCampaignInfo.category
                        .toLowerCase()
                        .replace(/\s+/g, "")}`,
                      defaultMessage: selectedCampaignInfo.category,
                    })}
                </Typography>
              </Grid>
            )}
            {/* Form alanları burada başlıyor */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: "form.firstName" })}
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                required
                error={isSubmitted && !formData.firstName}
                helperText={
                  isSubmitted && !formData.firstName
                    ? intl.formatMessage({
                        id: "form.validation.firstNameRequired",
                      })
                    : ""
                }
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: { xs: "12px", md: "8px" },
                    backgroundColor: { xs: "#fafafa", md: "transparent" },
                    minHeight: { xs: "56px", md: "56px" },
                    "&:hover": {
                      backgroundColor: { xs: "#f5f5f5", md: "transparent" },
                    },
                    "&.Mui-focused": {
                      backgroundColor: { xs: "white", md: "transparent" },
                      boxShadow: {
                        xs: "0 0 0 2px rgba(25, 118, 210, 0.2)",
                        md: "none",
                      },
                    },
                  },
                  "& .MuiInputLabel-root": {
                    fontSize: { xs: "16px", md: "14px" },
                    fontWeight: { xs: 500, md: 400 },
                    color: { xs: "#1976d2", md: "rgba(0, 0, 0, 0.6)" },
                    "&.Mui-focused": {
                      color: "#1976d2",
                    },
                  },
                  "& .MuiOutlinedInput-input": {
                    fontSize: { xs: "16px", md: "14px" },
                    padding: { xs: "16px 14px", md: "16.5px 14px" },
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: "form.lastName" })}
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                required
                error={isSubmitted && !formData.lastName}
                helperText={
                  isSubmitted && !formData.lastName
                    ? intl.formatMessage({
                        id: "form.validation.lastNameRequired",
                      })
                    : ""
                }
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: { xs: "12px", md: "8px" },
                    backgroundColor: { xs: "#fafafa", md: "transparent" },
                    minHeight: { xs: "56px", md: "56px" },
                    "&:hover": {
                      backgroundColor: { xs: "#f5f5f5", md: "transparent" },
                    },
                    "&.Mui-focused": {
                      backgroundColor: { xs: "white", md: "transparent" },
                      boxShadow: {
                        xs: "0 0 0 2px rgba(25, 118, 210, 0.2)",
                        md: "none",
                      },
                    },
                  },
                  "& .MuiInputLabel-root": {
                    fontSize: { xs: "16px", md: "14px" },
                    fontWeight: { xs: 500, md: 400 },
                    color: { xs: "#1976d2", md: "rgba(0, 0, 0, 0.6)" },
                    "&.Mui-focused": {
                      color: "#1976d2",
                    },
                  },
                  "& .MuiOutlinedInput-input": {
                    fontSize: { xs: "16px", md: "14px" },
                    padding: { xs: "16px 14px", md: "16.5px 14px" },
                  },
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: "form.email" })}
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                placeholder="<EMAIL>"
                error={
                  isSubmitted &&
                  (!formData.email ||
                    !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email))
                }
                helperText={
                  isSubmitted && !formData.email
                    ? intl.formatMessage({
                        id: "form.validation.emailRequired",
                      })
                    : isSubmitted &&
                      !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)
                    ? intl.formatMessage({ id: "form.invalidEmail" })
                    : ""
                }
                disabled={false}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: { xs: "12px", md: "8px" },
                    backgroundColor: { xs: "#fafafa", md: "transparent" },
                    minHeight: { xs: "56px", md: "56px" },
                    "&:hover": {
                      backgroundColor: { xs: "#f5f5f5", md: "transparent" },
                    },
                    "&.Mui-focused": {
                      backgroundColor: { xs: "white", md: "transparent" },
                      boxShadow: {
                        xs: "0 0 0 2px rgba(25, 118, 210, 0.2)",
                        md: "none",
                      },
                    },
                    "&.Mui-disabled": {
                      backgroundColor: {
                        xs: "#f0f0f0",
                        md: "rgba(0, 0, 0, 0.06)",
                      },
                    },
                  },
                  "& .MuiInputLabel-root": {
                    fontSize: { xs: "16px", md: "14px" },
                    fontWeight: { xs: 500, md: 400 },
                    color: { xs: "#1976d2", md: "rgba(0, 0, 0, 0.6)" },
                    "&.Mui-focused": {
                      color: "#1976d2",
                    },
                  },
                  "& .MuiOutlinedInput-input": {
                    fontSize: { xs: "16px", md: "14px" },
                    padding: { xs: "16px 14px", md: "16.5px 14px" },
                  },
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: "form.phone" })}
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                required
                placeholder="5xxxxxxxxx"
                inputProps={{
                  maxLength: 10,
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                }}
                error={
                  isSubmitted &&
                  (!formData.phone || !/^[0-9]{10}$/.test(formData.phone))
                }
                helperText={
                  isSubmitted && !formData.phone
                    ? intl.formatMessage({
                        id: "form.validation.phoneRequired",
                      })
                    : isSubmitted && !/^[0-9]{10}$/.test(formData.phone)
                    ? intl.formatMessage({ id: "form.invalidPhone" })
                    : ""
                }
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: { xs: "12px", md: "8px" },
                    backgroundColor: { xs: "#fafafa", md: "transparent" },
                    minHeight: { xs: "56px", md: "56px" },
                    "&:hover": {
                      backgroundColor: { xs: "#f5f5f5", md: "transparent" },
                    },
                    "&.Mui-focused": {
                      backgroundColor: { xs: "white", md: "transparent" },
                      boxShadow: {
                        xs: "0 0 0 2px rgba(25, 118, 210, 0.2)",
                        md: "none",
                      },
                    },
                  },
                  "& .MuiInputLabel-root": {
                    fontSize: { xs: "16px", md: "14px" },
                    fontWeight: { xs: 500, md: 400 },
                    color: { xs: "#1976d2", md: "rgba(0, 0, 0, 0.6)" },
                    "&.Mui-focused": {
                      color: "#1976d2",
                    },
                  },
                  "& .MuiOutlinedInput-input": {
                    fontSize: { xs: "16px", md: "14px" },
                    padding: { xs: "16px 14px", md: "16.5px 14px" },
                  },
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <CityDistrictSelector
                cityValue={formData.city}
                districtValue={formData.district}
                onCityChange={(city) => setFormData({ ...formData, city })}
                onDistrictChange={(district) =>
                  setFormData({ ...formData, district })
                }
                cityError={
                  isSubmitted && !formData.city
                    ? intl.formatMessage({ id: "form.validation.cityRequired" })
                    : ""
                }
                districtError={
                  isSubmitted && !formData.district
                    ? intl.formatMessage({
                        id: "form.validation.districtRequired",
                      })
                    : ""
                }
                required
                size="medium"
                gridSize={{ city: 6, district: 6 }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.consent}
                    onChange={handleInputChange}
                    name="consent"
                    required
                  />
                }
                label={
                  <Typography
                    variant="body2"
                    sx={{ display: "inline", lineHeight: 1.5 }}
                  >
                    {intl.formatMessage({ id: "form.kvkkConsentPrefix" })}&nbsp;
                    <Link
                      component="button"
                      variant="body2"
                      onClick={handleKVKKClick}
                      sx={{
                        display: "inline",
                        p: 0,
                        m: 0,
                        verticalAlign: "baseline",
                      }}
                    >
                      {intl.formatMessage({ id: "form.kvkkConsentLink" })}
                    </Link>
                    &nbsp;{intl.formatMessage({ id: "form.kvkkConsentSuffix" })}
                  </Typography>
                }
              />
              {isSubmitted && !formData.consent && (
                <Typography variant="caption" color="error">
                  {intl.formatMessage({ id: "form.validation.kvkkRequired" })}
                </Typography>
              )}
            </Grid>
          </Grid>
        </Box>
      );
    }

    return null;
  };

  const handleKVKKClick = (event: React.MouseEvent) => {
    event.preventDefault();
    setKvkkDialogOpen(true);
  };

  // Success dialogu otomatik kapatmak için effect
  React.useEffect(() => {
    if (showSuccessDialog && isAuthenticated) {
      const timer = setTimeout(() => {
        setShowSuccessDialog(false);
        handleClose();
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [showSuccessDialog, isAuthenticated]);

  // Auto scroll to form fields on mobile when dialog opens or reaching contact info step
  React.useEffect(() => {
    if (open && isMobile && formFieldsRef.current) {
      const shouldScroll =
        !isNavbar || (isNavbar && activeStep === steps.length - 1);

      if (shouldScroll) {
        // Delay to ensure dialog is fully rendered and campaign images are loaded
        const timer = setTimeout(() => {
          formFieldsRef.current?.scrollIntoView({
            behavior: "smooth",
            block: "center", // Center the form fields in view
            inline: "nearest",
          });
        }, 800); // Increased delay to ensure all content is loaded

        return () => clearTimeout(timer);
      }
    }
  }, [open, isMobile, activeStep, isNavbar, steps.length]);

  // Overlay modunda sadece içeriği render et
  if (isOverlay) {
    return (
      <Box sx={{ p: 2, height: "100%", overflow: "auto" }}>
        <Box sx={{ mt: 0 }}>
          {isNavbar && activeStep !== steps.length - 1 && (
            <Stepper
              activeStep={activeStep}
              sx={{
                display: { xs: "none", md: "flex" }, // Mobilde gizle
                mb: { xs: 3, md: 4 },
                "& .MuiStepLabel-root": {
                  fontSize: { xs: "0.75rem", md: "0.875rem" },
                },
                "& .MuiStepConnector-line": {
                  borderTopWidth: { xs: 1, md: 2 },
                },
                "& .MuiStepIcon-root": {
                  fontSize: { xs: "1.2rem", md: "1.5rem" },
                },
              }}
            >
              {steps
                .filter((label) => {
                  // Mobilde "İletişim Bilgileri" adımını gizle
                  if (
                    isMobile &&
                    label === intl.formatMessage({ id: "steps.contactInfo" })
                  ) {
                    return false;
                  }
                  return true;
                })
                .map((label, index) => (
                  <Step key={index}>
                    <StepLabel>{label}</StepLabel>
                  </Step>
                ))}
            </Stepper>
          )}

          {/* Overlay modunda da kampanya detaylarını göster */}
          <Grid container spacing={{ xs: 1.5, md: 2 }}>
            {/* Campaign Images and Details - Son aşamada göster */}
            {currentCampaign && activeStep === steps.length - 1 && (
              <Grid item xs={12}>
                <Box sx={{ mb: 3 }}>
                  {/* Main Image with Navigation */}
                  <Box
                    sx={{
                      position: "relative",
                      width: "100%",
                      height: { xs: "200px", md: "250px" },
                      borderRadius: 2,
                      overflow: "hidden",
                      mb: 2,
                      boxShadow: "0 4px 20px rgba(0,0,0,0.1)",
                    }}
                  >
                    {campaignImages.length > 1 && (
                      <>
                        <IconButton
                          onClick={handlePreviousImage}
                          sx={{
                            position: "absolute",
                            left: { xs: 4, md: 8 },
                            top: "50%",
                            transform: "translateY(-50%)",
                            zIndex: 2,
                            bgcolor: "rgba(0, 0, 0, 0.5)",
                            color: "white",
                            "&:hover": {
                              bgcolor: "rgba(0, 0, 0, 0.7)",
                            },
                            width: { xs: "32px", md: "40px" },
                            height: { xs: "32px", md: "40px" },
                          }}
                        >
                          <NavigateBeforeIcon
                            sx={{ fontSize: { xs: "18px", md: "24px" } }}
                          />
                        </IconButton>
                        <IconButton
                          onClick={handleNextImage}
                          sx={{
                            position: "absolute",
                            right: { xs: 4, md: 8 },
                            top: "50%",
                            transform: "translateY(-50%)",
                            zIndex: 2,
                            bgcolor: "rgba(0, 0, 0, 0.5)",
                            color: "white",
                            "&:hover": {
                              bgcolor: "rgba(0, 0, 0, 0.7)",
                            },
                            width: { xs: "32px", md: "40px" },
                            height: { xs: "32px", md: "40px" },
                          }}
                        >
                          <NavigateNextIcon
                            sx={{ fontSize: { xs: "18px", md: "24px" } }}
                          />
                        </IconButton>
                      </>
                    )}
                    <img
                      src={
                        campaignImages.length > 0
                          ? campaignImages[currentImageIndex]?.url
                          : currentCampaign.imageUrl
                      }
                      alt={currentCampaign.title}
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                        objectPosition: "center",
                      }}
                      onError={(e) => {
                        e.currentTarget.src = currentCampaign.imageUrl;
                      }}
                    />
                    {/* Image counter */}
                    {campaignImages.length > 1 && (
                      <Box
                        sx={{
                          position: "absolute",
                          bottom: 8,
                          right: 8,
                          bgcolor: "rgba(0, 0, 0, 0.6)",
                          color: "white",
                          px: 1,
                          py: 0.5,
                          borderRadius: 1,
                          fontSize: "0.75rem",
                        }}
                      >
                        {currentImageIndex + 1} / {campaignImages.length}
                      </Box>
                    )}
                  </Box>

                  {/* Campaign Details */}
                  <Typography
                    variant="h6"
                    fontWeight={600}
                    gutterBottom
                    sx={{
                      wordWrap: "break-word",
                      overflowWrap: "break-word",
                      whiteSpace: "normal",
                    }}
                  >
                    {currentCampaign.title}
                  </Typography>

                  <Typography
                    variant="subtitle1"
                    fontWeight={500}
                    sx={{
                      color: "primary.main",
                      mb: 1,
                      wordWrap: "break-word",
                      overflowWrap: "break-word",
                      whiteSpace: "normal",
                    }}
                  >
                    {currentCampaign.brand}
                  </Typography>

                  {currentCampaign.description && (
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mb: 2 }}
                    >
                      {currentCampaign.description}
                    </Typography>
                  )}

                  {/* Campaign Additional Information */}
                  {(currentCampaign.details ||
                    currentCampaign.stats ||
                    currentCampaign.descriptionTitle ||
                    currentCampaign.title2 ||
                    currentCampaign.title3 ||
                    currentCampaign.campaignUrl ||
                    dynamicCampaignUrl) && (
                    <>
                      <Box
                        sx={{
                          bgcolor: isDarkMode ? "grey.900" : "grey.50",
                          borderRadius: 2,
                          p: { xs: 2, md: 3 },
                          mt: 2,
                          border: isDarkMode
                            ? "1px solid rgba(255, 255, 255, 0.1)"
                            : "1px solid rgba(0, 0, 0, 0.1)",
                        }}
                      >
                        {/* Description Title and Description 2 */}
                        {currentCampaign.descriptionTitle && (
                          <Box sx={{ mb: 2 }}>
                            <Typography
                              variant="subtitle2"
                              fontWeight={600}
                              sx={{ mb: 1, color: "primary.main" }}
                            >
                              {currentCampaign.descriptionTitle}
                            </Typography>
                            {currentCampaign.description2 && (
                              <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{ lineHeight: 1.6 }}
                              >
                                {currentCampaign.description2}
                              </Typography>
                            )}
                          </Box>
                        )}

                        {/* Title 2 and Description 2 */}
                        {currentCampaign.title2 && (
                          <Box sx={{ mb: 2 }}>
                            <Typography
                              variant="subtitle2"
                              fontWeight={600}
                              sx={{ mb: 1, color: "primary.main" }}
                            >
                              {currentCampaign.title2}
                            </Typography>
                            {currentCampaign.description2 &&
                              !currentCampaign.descriptionTitle && (
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                  sx={{ lineHeight: 1.6 }}
                                >
                                  {currentCampaign.description2}
                                </Typography>
                              )}
                          </Box>
                        )}

                        {/* Title 3 and Description 3 */}
                        {currentCampaign.title3 && (
                          <Box sx={{ mb: 2 }}>
                            <Typography
                              variant="subtitle2"
                              fontWeight={600}
                              sx={{ mb: 1, color: "primary.main" }}
                            >
                              {currentCampaign.title3}
                            </Typography>
                            {currentCampaign.description3 && (
                              <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{ lineHeight: 1.6 }}
                              >
                                {currentCampaign.description3}
                              </Typography>
                            )}
                          </Box>
                        )}

                        {/* Campaign Details */}
                        {currentCampaign.details &&
                          typeof currentCampaign.details === "object" && (
                            <Box sx={{ mb: 2 }}>
                              <Typography
                                variant="subtitle2"
                                fontWeight={600}
                                sx={{ mb: 1.5, color: "primary.main" }}
                              >
                                {intl.formatMessage({
                                  id: "campaign.features.title",
                                })}
                              </Typography>
                              {Object.entries(currentCampaign.details).map(
                                ([key, field]: [string, any]) => {
                                  if (!field || !field.label || !field.value)
                                    return null;
                                  return (
                                    <Box
                                      key={key}
                                      sx={{
                                        display: "flex",
                                        justifyContent: "space-between",
                                        alignItems: "center",
                                        py: 0.75,
                                        borderBottom: isDarkMode
                                          ? "1px solid rgba(255, 255, 255, 0.1)"
                                          : "1px solid rgba(0, 0, 0, 0.1)",
                                        "&:last-child": {
                                          borderBottom: "none",
                                        },
                                      }}
                                    >
                                      <Typography
                                        variant="body2"
                                        color="text.secondary"
                                        sx={{ fontSize: "0.85rem" }}
                                      >
                                        {field.label}:
                                      </Typography>
                                      <Typography
                                        variant="body2"
                                        fontWeight={500}
                                        sx={{ fontSize: "0.85rem" }}
                                      >
                                        {typeof field.value === 'boolean' 
                                          ? intl.formatMessage({ 
                                              id: field.value ? 'common.yes' : 'common.no' 
                                            })
                                          : field.value === 'true' 
                                            ? intl.formatMessage({ id: 'common.yes' })
                                            : field.value === 'false'
                                              ? intl.formatMessage({ id: 'common.no' })
                                              : field.value}
                                      </Typography>
                                    </Box>
                                  );
                                }
                              )}
                            </Box>
                          )}

                        {/* Stats */}
                        {currentCampaign.stats &&
                          typeof currentCampaign.stats === "object" && (
                            <Box sx={{ mb: 2 }}>
                              <Typography
                                variant="subtitle2"
                                fontWeight={600}
                                sx={{ mb: 1.5, color: "primary.main" }}
                              >
                                {intl.formatMessage({
                                  id: "campaign.features.statistics",
                                })}
                              </Typography>
                              {Object.entries(currentCampaign.stats).map(
                                ([key, value]) => (
                                  <Box
                                    key={key}
                                    sx={{
                                      display: "flex",
                                      justifyContent: "space-between",
                                      alignItems: "center",
                                      py: 0.75,
                                      borderBottom: isDarkMode
                                        ? "1px solid rgba(255, 255, 255, 0.1)"
                                        : "1px solid rgba(0, 0, 0, 0.1)",
                                      "&:last-child": {
                                        borderBottom: "none",
                                      },
                                    }}
                                  >
                                    <Typography
                                      variant="body2"
                                      color="text.secondary"
                                      sx={{ fontSize: "0.85rem" }}
                                    >
                                      {key}:
                                    </Typography>
                                    <Typography
                                      variant="body2"
                                      fontWeight={500}
                                      sx={{ fontSize: "0.85rem" }}
                                    >
                                      {String(value)}
                                    </Typography>
                                  </Box>
                                )
                              )}
                            </Box>
                          )}

                        {/* Campaign URL - Enhanced Style */}
                        {(currentCampaign.campaignUrl ||
                          dynamicCampaignUrl) && (
                          <Box sx={{ mt: 3 }}>
                            <Button
                              variant="outlined"
                              color="primary"
                              size="small"
                              fullWidth
                              href={
                                currentCampaign.campaignUrl ||
                                dynamicCampaignUrl
                              }
                              target="_blank"
                              rel="noopener noreferrer"
                              disabled={urlLoading}
                              startIcon={
                                urlLoading ? (
                                  <CircularProgress size={16} />
                                ) : null
                              }
                              sx={{
                                py: 1,
                                fontSize: "0.8rem",
                                fontWeight: 600,
                                textTransform: "none",
                                borderRadius: 2,
                                "&:hover": {
                                  bgcolor: alpha(
                                    theme.palette.primary.main,
                                    0.04
                                  ),
                                },
                              }}
                            >
                              {urlLoading
                                ? intl.formatMessage({
                                    id: "campaign.url.loading",
                                  })
                                : intl.formatMessage({
                                    id: "campaign.url.visit",
                                  })}
                            </Button>
                          </Box>
                        )}
                      </Box>
                    </>
                  )}
                </Box>
              </Grid>
            )}

            {/* Form Content */}
            <Grid item xs={12}>
              {getStepContent(activeStep)}
            </Grid>
          </Grid>

          <Box
            sx={{
              display: "flex",
              flexDirection: { xs: "column", sm: "row" },
              justifyContent: { xs: "stretch", sm: "flex-end" },
              alignItems: { xs: "stretch", sm: "center" },
              gap: { xs: 1.5, sm: 1 },
              mt: { xs: 2, md: 3 },
            }}
          >
            {activeStep !== 0 && (
              <Button
                onClick={handleBack}
                disabled={loading}
                sx={{
                  order: { xs: 2, sm: 1 },
                  color: isDarkMode ? "grey.100" : "grey.700",
                  py: { xs: 1.5, md: 1 },
                  fontSize: { xs: "0.9rem", md: "0.875rem" },
                  "&:hover": {
                    bgcolor: isDarkMode
                      ? "rgba(255, 255, 255, 0.08)"
                      : "rgba(0, 0, 0, 0.04)",
                  },
                }}
              >
                {intl.formatMessage({ id: "form.back" })}
              </Button>
            )}
            <Button
              variant="contained"
              onClick={handleNext}
              disabled={loading || !isStepValid()}
              startIcon={
                loading ? <CircularProgress size={20} color="inherit" /> : null
              }
              sx={{
                order: { xs: 1, sm: 2 },
                py: { xs: 1.8, md: 1.2 },
                fontSize: { xs: "0.95rem", md: "0.875rem" },
                fontWeight: 600,
                minHeight: { xs: "48px", md: "40px" },
                bgcolor:
                  loading || !isStepValid() ? "grey.400" : "primary.main",
                "&:hover": {
                  bgcolor:
                    loading || !isStepValid() ? "grey.400" : "primary.dark",
                },
                "&:disabled": {
                  bgcolor: "grey.400",
                  color: "grey.600",
                  cursor: "not-allowed",
                },
              }}
            >
              {loading
                ? activeStep === steps.length - 1
                  ? intl.formatMessage({ id: "form.loading.submitting" })
                  : intl.formatMessage({ id: "form.loading.loading" })
                : activeStep === steps.length - 1
                ? intl.formatMessage({ id: "form.send" })
                : intl.formatMessage({ id: "form.continue" })}
            </Button>
          </Box>
        </Box>

        {/* Overlay modunda da modal'ları göster */}
        {/* Success Dialog */}
        <Dialog
          open={showSuccessDialog}
          onClose={() => setShowSuccessDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogContent sx={{ textAlign: "center", py: 4 }}>
            <Typography variant="h6" gutterBottom color="success.main">
              {intl.formatMessage({ id: "form.success.title" })}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {intl.formatMessage({ id: "form.success.message" })}
            </Typography>
          </DialogContent>
        </Dialog>

        {/* Error Dialog */}
        <Dialog
          open={errorDialogOpen}
          onClose={() => setErrorDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogContent sx={{ textAlign: "center", py: 4 }}>
            <ErrorOutlineIcon
              sx={{ fontSize: 48, color: "error.main", mb: 2 }}
            />
            <Typography variant="h6" gutterBottom color="error.main">
              {intl.formatMessage({ id: "form.error.title" })}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {errorMessage}
            </Typography>
          </DialogContent>
          <DialogActions sx={{ justifyContent: "center", pb: 3 }}>
            <Button
              onClick={() => setErrorDialogOpen(false)}
              variant="contained"
            >
              {intl.formatMessage({ id: "form.ok" })}
            </Button>
          </DialogActions>
        </Dialog>

        {/* KVKK Dialog */}
        <KVKKDialog
          open={kvkkDialogOpen}
          onClose={() => setKvkkDialogOpen(false)}
        />

        {/* Register Prompt Modal */}
        <RegisterPromptModal
          open={showRegisterPrompt}
          onClose={() => setShowRegisterPrompt(false)}
          userEmail={formData.email}
        />
      </Box>
    );
  }

  return (
    <>
      <Dialog
        open={open}
        onClose={loading ? undefined : handleClose}
        maxWidth="lg"
        fullWidth
        fullScreen={isMobile} // Full screen on mobile and tablet
        TransitionProps={{
          timeout: 0,
          appear: false,
          enter: false,
          exit: false,
        }}
        PaperProps={{
          sx: {
            borderRadius: { xs: 0, md: "12px" }, // No border radius on mobile for fullscreen
            boxShadow: isDarkMode
              ? "0 8px 32px rgba(0, 0, 0, 0.4)"
              : "0 8px 32px rgba(0, 0, 0, 0.1)",
            height: { xs: "100vh", md: "auto" }, // Full height on mobile
            maxHeight: { xs: "100vh", md: "90vh" }, // Prevent overflow
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            bgcolor: isDarkMode ? "background.paper" : "#fff",
            borderBottom: `1px solid ${
              isDarkMode ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.1)"
            }`,
            padding: { xs: "16px 20px", md: "20px 24px" }, // Reduced padding on mobile
            minHeight: { xs: "60px", md: "auto" }, // Ensure minimum touch target on mobile
          }}
        >
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 600,
              fontSize: "1.25rem",
              color: isDarkMode ? "grey.100" : "text.primary",
            }}
          >
            {isNavbar
              ? intl.formatMessage({ id: "form.getQuoteTitle" })
              : intl.formatMessage(
                  { id: "form.campaignQuoteTitle" },
                  { title: campaignDetails?.title }
                )}
          </Typography>
          <IconButton
            onClick={handleClose}
            disabled={loading}
            sx={{
              color: isDarkMode ? "grey.400" : "grey.500",
              minWidth: { xs: "44px", md: "auto" }, // Better touch target on mobile
              minHeight: { xs: "44px", md: "auto" },
              "&:hover:not(:disabled)": {
                color: isDarkMode ? "primary.light" : "primary.main",
                bgcolor: isDarkMode
                  ? "rgba(255, 255, 255, 0.04)"
                  : "rgba(0, 0, 0, 0.04)",
              },
              "&:disabled": {
                opacity: 0.5,
                cursor: "not-allowed",
              },
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent
          sx={{
            padding: { xs: "16px 20px", md: "24px" }, // Mobile-optimized padding
            overflow: "auto", // Enable scrolling on mobile
            "&::-webkit-scrollbar": {
              width: "6px",
            },
            "&::-webkit-scrollbar-track": {
              background: "transparent",
            },
            "&::-webkit-scrollbar-thumb": {
              background: isDarkMode
                ? "rgba(255, 255, 255, 0.2)"
                : "rgba(0, 0, 0, 0.2)",
              borderRadius: "3px",
            },
          }}
        >
          <Grid
            container
            spacing={{ xs: 2, md: 3 }}
            sx={{ mt: { xs: 0, md: 1 } }}
          >
            {/* Campaign Images and Details - Stacked above form on mobile */}
            {currentCampaign && activeStep === steps.length - 1 && (
              <Grid
                item
                xs={12}
                md={6}
                sx={{
                  borderRight: {
                    md: `1px solid ${
                      isDarkMode
                        ? "rgba(255, 255, 255, 0.1)"
                        : "rgba(0, 0, 0, 0.1)"
                    }`,
                  },
                  pr: { md: 2 },
                  mb: { xs: 2, md: 0 }, // Add margin bottom on mobile to separate from form
                }}
              >
                <Box sx={{ height: "100%" }}>
                  {/* Show campaign details from props (CampaignDetail page) or selected campaign (Navbar) */}
                  <>
                    {/* Main Image with Navigation */}
                    <Box
                      sx={{
                        position: "relative",
                        width: "100%",
                        height: { xs: "200px", sm: "250px", md: "300px" }, // Responsive height
                        mb: 2,
                        overflow: "hidden",
                        borderRadius: { xs: "8px", md: "12px" }, // Smaller radius on mobile
                        boxShadow: "0 4px 20px rgba(0,0,0,0.1)",
                      }}
                    >
                      {campaignImages.length > 1 && (
                        <>
                          <IconButton
                            onClick={handlePreviousImage}
                            sx={{
                              position: "absolute",
                              left: { xs: 4, md: 8 },
                              top: "50%",
                              transform: "translateY(-50%)",
                              zIndex: 2,
                              bgcolor: "rgba(255,255,255,0.9)",
                              color: "text.primary",
                              "&:hover": {
                                bgcolor: "rgba(255,255,255,1)",
                              },
                              width: { xs: 36, md: 40 }, // Smaller on mobile
                              height: { xs: 36, md: 40 },
                              minWidth: { xs: 36, md: 40 }, // Ensure touch target
                              minHeight: { xs: 36, md: 40 },
                            }}
                          >
                            <NavigateBeforeIcon
                              sx={{ fontSize: { xs: "18px", md: "24px" } }}
                            />
                          </IconButton>
                          <IconButton
                            onClick={handleNextImage}
                            sx={{
                              position: "absolute",
                              right: { xs: 4, md: 8 },
                              top: "50%",
                              transform: "translateY(-50%)",
                              zIndex: 2,
                              bgcolor: "rgba(255,255,255,0.9)",
                              color: "text.primary",
                              "&:hover": {
                                bgcolor: "rgba(255,255,255,1)",
                              },
                              width: { xs: 36, md: 40 }, // Smaller on mobile
                              height: { xs: 36, md: 40 },
                              minWidth: { xs: 36, md: 40 }, // Ensure touch target
                              minHeight: { xs: 36, md: 40 },
                            }}
                          >
                            <NavigateNextIcon
                              sx={{ fontSize: { xs: "18px", md: "24px" } }}
                            />
                          </IconButton>
                        </>
                      )}
                      <img
                        src={
                          campaignImages.length > 0
                            ? campaignImages[currentImageIndex]?.url
                            : currentCampaign.imageUrl
                        }
                        alt={currentCampaign.title}
                        style={{
                          width: "100%",
                          height: "100%",
                          objectFit: "cover",
                          objectPosition: "center",
                        }}
                        onError={(e) => {
                          e.currentTarget.src = currentCampaign.imageUrl;
                        }}
                      />
                      {/* Image counter */}
                      {campaignImages.length > 1 && (
                        <Box
                          sx={{
                            position: "absolute",
                            bottom: 8,
                            right: 8,
                            bgcolor: "rgba(0,0,0,0.7)",
                            color: "white",
                            px: 1,
                            py: 0.5,
                            borderRadius: "12px",
                            fontSize: "0.75rem",
                          }}
                        >
                          {currentImageIndex + 1} / {campaignImages.length}
                        </Box>
                      )}
                    </Box>

                    {/* Thumbnail Gallery */}
                    {campaignImages.length > 1 && (
                      <Box
                        sx={{
                          display: "flex",
                          gap: { xs: 0.5, md: 1 }, // Smaller gap on mobile
                          mb: { xs: 2, md: 3 }, // Smaller margin on mobile
                          overflowX: "auto",
                          pb: 1,
                          "&::-webkit-scrollbar": {
                            height: "4px",
                          },
                          "&::-webkit-scrollbar-track": {
                            background: "transparent",
                          },
                          "&::-webkit-scrollbar-thumb": {
                            background: isDarkMode
                              ? "rgba(255, 255, 255, 0.2)"
                              : "rgba(0, 0, 0, 0.2)",
                            borderRadius: "2px",
                          },
                        }}
                      >
                        {campaignImages.map((image, index) => (
                          <Box
                            key={index}
                            onClick={() => handleThumbnailClick(index)}
                            sx={{
                              width: { xs: 50, md: 60 }, // Smaller on mobile
                              height: { xs: 50, md: 60 },
                              borderRadius: { xs: "6px", md: "8px" },
                              overflow: "hidden",
                              border: "2px solid",
                              borderColor:
                                index === currentImageIndex
                                  ? "primary.main"
                                  : "divider",
                              cursor: "pointer",
                              transition: "all 0.2s",
                              "&:hover": {
                                borderColor: "primary.main",
                                transform: { xs: "none", md: "scale(1.05)" }, // Disable scale on mobile
                              },
                              "&:active": {
                                transform: {
                                  xs: "scale(0.95)",
                                  md: "scale(1.05)",
                                }, // Add active state for mobile
                              },
                              flexShrink: 0,
                              minWidth: { xs: 50, md: 60 }, // Ensure consistent size
                              minHeight: { xs: 50, md: 60 },
                            }}
                          >
                            <img
                              src={image.url}
                              alt={`Kampanya görseli ${index + 1}`}
                              style={{
                                width: "100%",
                                height: "100%",
                                objectFit: "cover",
                                objectPosition: "center",
                              }}
                            />
                          </Box>
                        ))}
                      </Box>
                    )}

                    {/* Campaign Details */}
                    <Typography variant="h5" fontWeight={600} gutterBottom>
                      {currentCampaign.title}
                    </Typography>

                    <Typography
                      variant="subtitle1"
                      fontWeight={500}
                      sx={{ color: "primary.main", mb: 2 }}
                    >
                      {currentCampaign.brand}
                    </Typography>

                    {currentCampaign.description && (
                      <Typography
                        variant="body1"
                        color="text.secondary"
                        sx={{ mb: 2 }}
                      >
                        {currentCampaign.description}
                      </Typography>
                    )}

                    {/* Campaign Additional Information */}
                    {(currentCampaign.details ||
                      currentCampaign.stats ||
                      currentCampaign.descriptionTitle ||
                      currentCampaign.title2 ||
                      currentCampaign.title3 ||
                      currentCampaign.campaignUrl) && (
                      <>
                        <Box
                          sx={{
                            bgcolor: isDarkMode
                              ? "rgba(255,255,255,0.03)"
                              : "rgba(0,0,0,0.01)",
                            p: 2,
                            borderRadius: "8px",
                            border: `1px solid ${
                              isDarkMode
                                ? "rgba(255,255,255,0.08)"
                                : "rgba(0,0,0,0.08)"
                            }`,
                            mb: 2,
                          }}
                        >
                          {/* Description Title and Description 2 */}
                          {currentCampaign.descriptionTitle && (
                            <Box sx={{ mb: 2 }}>
                              <Typography
                                variant="subtitle2"
                                fontWeight={600}
                                sx={{ mb: 1, color: "primary.main" }}
                              >
                                {currentCampaign.descriptionTitle}
                              </Typography>
                              {currentCampaign.description2 && (
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                  sx={{ lineHeight: 1.6 }}
                                >
                                  {currentCampaign.description2}
                                </Typography>
                              )}
                            </Box>
                          )}

                          {/* Title 2 and Description 2 */}
                          {currentCampaign.title2 && (
                            <Box sx={{ mb: 2 }}>
                              <Typography
                                variant="subtitle2"
                                fontWeight={600}
                                sx={{ mb: 1, color: "primary.main" }}
                              >
                                {currentCampaign.title2}
                              </Typography>
                              {currentCampaign.description2 &&
                                !currentCampaign.descriptionTitle && (
                                  <Typography
                                    variant="body2"
                                    color="text.secondary"
                                    sx={{ lineHeight: 1.6 }}
                                  >
                                    {currentCampaign.description2}
                                  </Typography>
                                )}
                            </Box>
                          )}

                          {/* Title 3 and Description 3 */}
                          {currentCampaign.title3 && (
                            <Box sx={{ mb: 2 }}>
                              <Typography
                                variant="subtitle2"
                                fontWeight={600}
                                sx={{ mb: 1, color: "primary.main" }}
                              >
                                {currentCampaign.title3}
                              </Typography>
                              {currentCampaign.description3 && (
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                  sx={{ lineHeight: 1.6 }}
                                >
                                  {currentCampaign.description3}
                                </Typography>
                              )}
                            </Box>
                          )}

                          {/* Campaign Details */}
                          {currentCampaign.details &&
                            typeof currentCampaign.details === "object" && (
                              <Box sx={{ mb: 2 }}>
                                <Typography
                                  variant="subtitle2"
                                  fontWeight={600}
                                  sx={{ mb: 1, color: "primary.main" }}
                                >
                                  {intl.formatMessage({
                                    id: "campaign.features.title",
                                  })}
                                </Typography>
                                {Object.entries(currentCampaign.details).map(
                                  ([key, field]: [string, any]) => {
                                    if (!field || !field.label || !field.value)
                                      return null;
                                    return (
                                      <Box
                                        key={key}
                                        sx={{
                                          display: "flex",
                                          justifyContent: "space-between",
                                          alignItems: "center",
                                          mb: 0.5,
                                        }}
                                      >
                                        <Typography
                                          variant="body2"
                                          color="text.secondary"
                                        >
                                          {field.label}
                                        </Typography>
                                        <Typography
                                          variant="body2"
                                          fontWeight={500}
                                        >
                                          {typeof field.value === 'boolean' 
                                            ? intl.formatMessage({ 
                                                id: field.value ? 'common.yes' : 'common.no' 
                                              })
                                            : field.value === 'true' 
                                              ? intl.formatMessage({ id: 'common.yes' })
                                              : field.value === 'false'
                                                ? intl.formatMessage({ id: 'common.no' })
                                                : field.value}
                                        </Typography>
                                      </Box>
                                    );
                                  }
                                )}
                              </Box>
                            )}

                          {/* Stats */}
                          {currentCampaign.stats &&
                            typeof currentCampaign.stats === "object" && (
                              <Box sx={{ mb: 2 }}>
                                <Typography
                                  variant="subtitle2"
                                  fontWeight={600}
                                  sx={{ mb: 1, color: "primary.main" }}
                                >
                                  {intl.formatMessage({
                                    id: "campaign.features.statistics",
                                  })}
                                </Typography>
                                {Object.entries(currentCampaign.stats).map(
                                  ([key, value]) => (
                                    <Box
                                      key={key}
                                      sx={{
                                        display: "flex",
                                        justifyContent: "space-between",
                                        alignItems: "center",
                                        mb: 0.5,
                                      }}
                                    >
                                      <Typography
                                        variant="body2"
                                        color="text.secondary"
                                        sx={{ textTransform: "capitalize" }}
                                      >
                                        {key === "views"
                                          ? intl.formatMessage({
                                              id: "campaign.features.views",
                                            })
                                          : key === "applications"
                                          ? intl.formatMessage({
                                              id: "campaign.features.applications",
                                            })
                                          : key === "clicks"
                                          ? intl.formatMessage({
                                              id: "campaign.features.clicks",
                                            })
                                          : key
                                              .replace(/([A-Z])/g, " $1")
                                              .toLowerCase()}
                                      </Typography>
                                      <Typography
                                        variant="body2"
                                        fontWeight={500}
                                      >
                                        {String(value)}
                                      </Typography>
                                    </Box>
                                  )
                                )}
                              </Box>
                            )}

                          {/* Campaign URL - Enhanced Style */}
                          {(currentCampaign.campaignUrl ||
                            dynamicCampaignUrl) && (
                            <Box sx={{ mt: 3 }}>
                              <Button
                                variant="outlined"
                                color="primary"
                                size="small"
                                fullWidth
                                href={
                                  currentCampaign.campaignUrl ||
                                  dynamicCampaignUrl
                                }
                                target="_blank"
                                rel="noopener noreferrer"
                                sx={{
                                  textTransform: "none",
                                  fontWeight: 600,
                                  borderRadius: "8px",
                                  py: 1,
                                  fontSize: "0.875rem",
                                  border: `1px solid ${
                                    isDarkMode
                                      ? "rgba(255, 255, 255, 0.2)"
                                      : "rgba(0, 0, 0, 0.2)"
                                  }`,
                                  color: isDarkMode
                                    ? "primary.light"
                                    : "primary.main",
                                  "&:hover": {
                                    transform: "translateY(-1px)",
                                    boxShadow: isDarkMode
                                      ? "0 4px 12px rgba(33, 150, 243, 0.3)"
                                      : "0 4px 12px rgba(25, 118, 210, 0.2)",
                                    backgroundColor: isDarkMode
                                      ? "rgba(33, 150, 243, 0.04)"
                                      : "rgba(25, 118, 210, 0.04)",
                                  },
                                  transition: "all 0.2s ease-in-out",
                                }}
                              >
                                🔗{" "}
                                {intl.formatMessage({
                                  id: "campaign.visitCampaignPage",
                                })}
                              </Button>
                            </Box>
                          )}
                        </Box>
                      </>
                    )}
                  </>
                </Box>
              </Grid>
            )}

            {/* Form Section - Full width on mobile, right column on desktop */}
            <Grid
              item
              xs={12}
              md={currentCampaign && activeStep === steps.length - 1 ? 6 : 12}
              sx={{
                pl: {
                  md:
                    currentCampaign && activeStep === steps.length - 1 ? 2 : 0,
                },
                mt: {
                  xs:
                    currentCampaign && activeStep === steps.length - 1 ? 2 : 0,
                  md: 0,
                }, // Add top margin on mobile when campaign is shown
              }}
            >
              <Box sx={{ mt: 0 }}>
                {isNavbar && (
                  <Stepper
                    activeStep={activeStep}
                    sx={{
                      display: { xs: "none", md: "flex" }, // Mobilde gizle
                      mb: { xs: 3, md: 4 }, // Reduced margin on mobile
                      "& .MuiStepLabel-root": {
                        fontSize: { xs: "0.75rem", md: "0.875rem" }, // Smaller text on mobile
                      },
                      "& .MuiStepIcon-root": {
                        fontSize: { xs: "1.2rem", md: "1.5rem" }, // Smaller icons on mobile
                      },
                    }}
                  >
                    {steps
                      .filter((label) => {
                        // Mobilde "İletişim Bilgileri" adımını gizle
                        if (
                          isMobile &&
                          label ===
                            intl.formatMessage({ id: "steps.contactInfo" })
                        ) {
                          return false;
                        }
                        return true;
                      })
                      .map((label) => (
                        <Step key={label}>
                          <StepLabel
                            sx={{
                              "& .MuiStepLabel-label": {
                                fontSize: { xs: "0.75rem", md: "0.875rem" }, // Responsive font size
                                lineHeight: { xs: 1.2, md: 1.43 },
                              },
                            }}
                          >
                            {label}
                          </StepLabel>
                        </Step>
                      ))}
                  </Stepper>
                )}
                {getStepContent(activeStep)}
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: { xs: "column", sm: "row" }, // Stack buttons on mobile
                    justifyContent: { xs: "stretch", sm: "flex-end" }, // Full width on mobile
                    alignItems: { xs: "stretch", sm: "center" },
                    gap: { xs: 1.5, sm: 1 }, // Better spacing on mobile
                    mt: { xs: 2, md: 3 },
                  }}
                >
                  {activeStep !== 0 && (
                    <Button
                      onClick={handleBack}
                      disabled={loading}
                      sx={{
                        order: { xs: 2, sm: 1 }, // Show back button below on mobile
                        color: isDarkMode ? "grey.100" : "grey.700",
                        py: { xs: 1.5, md: 1 }, // Larger padding on mobile
                        fontSize: { xs: "0.95rem", md: "0.875rem" },
                        minHeight: { xs: "48px", md: "auto" }, // Better touch target
                        borderRadius: { xs: "8px", md: "4px" },
                        textTransform: "none",
                        fontWeight: 500,
                        "&:hover:not(:disabled)": {
                          color: isDarkMode ? "primary.light" : "primary.main",
                          bgcolor: isDarkMode
                            ? "rgba(255, 255, 255, 0.04)"
                            : "rgba(0, 0, 0, 0.04)",
                        },
                        "&:disabled": {
                          opacity: 0.5,
                          cursor: "not-allowed",
                        },
                      }}
                    >
                      {intl.formatMessage({ id: "form.back" })}
                    </Button>
                  )}
                  <Button
                    variant="contained"
                    onClick={handleNext}
                    disabled={loading}
                    startIcon={
                      loading ? (
                        <CircularProgress size={20} color="inherit" />
                      ) : null
                    }
                    sx={{
                      order: { xs: 1, sm: 2 }, // Show submit button above on mobile
                      py: { xs: 1.8, md: 1.2 }, // Larger padding on mobile
                      px: { xs: 3, md: 2.5 },
                      borderRadius: { xs: "8px", md: "12px" },
                      textTransform: "none",
                      fontSize: { xs: "1rem", md: "1rem" },
                      fontWeight: 600,
                      minHeight: { xs: "52px", md: "auto" }, // Better touch target on mobile
                      background: isDarkMode
                        ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`
                        : `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`,
                      color: "#fff",
                      border: isDarkMode
                        ? "1px solid rgba(255, 255, 255, 0.1)"
                        : "none",
                      boxShadow: isDarkMode
                        ? `0 4px 20px ${alpha(theme.palette.primary.main, 0.3)}`
                        : `0 4px 20px ${alpha(
                            theme.palette.primary.main,
                            0.2
                          )}`,
                      transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                      "&:hover:not(:disabled)": {
                        transform: { xs: "none", md: "translateY(-2px)" }, // Disable transform on mobile
                        background: isDarkMode
                          ? `linear-gradient(135deg, ${theme.palette.primary.light} 0%, ${theme.palette.primary.main} 100%)`
                          : `linear-gradient(135deg, ${theme.palette.primary.light} 0%, ${theme.palette.primary.main} 100%)`,
                        boxShadow: isDarkMode
                          ? `0 8px 24px ${alpha(
                              theme.palette.primary.main,
                              0.4
                            )}`
                          : `0 8px 24px ${alpha(
                              theme.palette.primary.main,
                              0.3
                            )}`,
                      },
                      "&:active:not(:disabled)": {
                        transform: { xs: "scale(0.98)", md: "translateY(0)" }, // Mobile-friendly active state
                        boxShadow: isDarkMode
                          ? `0 4px 12px ${alpha(
                              theme.palette.primary.main,
                              0.2
                            )}`
                          : `0 4px 12px ${alpha(
                              theme.palette.primary.main,
                              0.1
                            )}`,
                      },
                      "&:disabled": {
                        opacity: 0.7,
                        cursor: "not-allowed",
                      },
                    }}
                  >
                    {loading
                      ? activeStep === steps.length - 1
                        ? intl.formatMessage({ id: "form.loading.submitting" })
                        : intl.formatMessage({ id: "form.loading.loading" })
                      : activeStep === steps.length - 1
                      ? intl.formatMessage({ id: "form.send" })
                      : intl.formatMessage({ id: "form.continue" })}
                  </Button>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
      </Dialog>

      {/* KVKK Dialog */}
      <KVKKDialog
        open={kvkkDialogOpen}
        onClose={() => setKvkkDialogOpen(false)}
      />

      {/* Register Prompt Modal */}
      <RegisterPromptModal
        open={showRegisterPrompt}
        onClose={() => setShowRegisterPrompt(false)}
        userEmail={formData.email}
      />

      {/* Error Dialog */}
      <Dialog
        open={errorDialogOpen}
        onClose={() => {
          setErrorDialogOpen(false);
          document.body.style.overflow = "auto";
        }}
        PaperProps={{
          sx: {
            borderRadius: "12px",
            overflow: "hidden",
            minWidth: "400px",
            maxWidth: "90%",
            boxShadow: "0 8px 24px rgba(0, 0, 0, 0.15)",
            bgcolor: isDarkMode ? "background.paper" : "#fff",
            position: "relative",
            zIndex: 1300,
          },
        }}
        disableScrollLock={true}
      >
        <DialogTitle
          sx={{
            borderBottom: "1px solid",
            borderColor: isDarkMode
              ? "rgba(255, 255, 255, 0.1)"
              : "rgba(0, 0, 0, 0.1)",
            py: 2,
            px: 3,
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Typography
            variant="h6"
            sx={{
              color: theme.palette.error.main,
              fontWeight: 600,
              fontSize: "1.1rem",
            }}
          >
            Uyarı
          </Typography>
          <IconButton
            onClick={() => setErrorDialogOpen(false)}
            size="small"
            sx={{
              color: isDarkMode ? "grey.400" : "grey.500",
              "&:hover": {
                bgcolor: isDarkMode
                  ? "rgba(255, 255, 255, 0.04)"
                  : "rgba(0, 0, 0, 0.04)",
              },
            }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ py: 3, px: 3 }}>
          <Box
            sx={{
              display: "flex",
              alignItems: "flex-start",
              gap: 2,
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                width: 40,
                height: 40,
                borderRadius: "50%",
                bgcolor: alpha(theme.palette.error.main, 0.1),
                color: theme.palette.error.main,
                flexShrink: 0,
              }}
            >
              <ErrorOutlineIcon />
            </Box>
            <Typography
              variant="body1"
              sx={{
                whiteSpace: "pre-line",
                lineHeight: 1.6,
              }}
            >
              {errorMessage}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button
            variant="contained"
            onClick={() => setErrorDialogOpen(false)}
            sx={{
              py: 1,
              px: 2.5,
              borderRadius: "8px",
              textTransform: "none",
              fontSize: "0.95rem",
              fontWeight: 600,
              background: isDarkMode
                ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`
                : `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`,
              color: "#fff",
              boxShadow: isDarkMode
                ? `0 4px 12px ${alpha(theme.palette.primary.main, 0.3)}`
                : `0 4px 12px ${alpha(theme.palette.primary.main, 0.2)}`,
              transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
              "&:hover": {
                transform: "translateY(-2px)",
                boxShadow: isDarkMode
                  ? `0 8px 24px ${alpha(theme.palette.primary.main, 0.4)}`
                  : `0 8px 24px ${alpha(theme.palette.primary.main, 0.3)}`,
              },
            }}
          >
            Tamam
          </Button>
        </DialogActions>
      </Dialog>

      {/* Success Dialog - sadece giriş yapmış kullanıcılar için */}
      {isAuthenticated && (
        <Dialog
          open={showSuccessDialog}
          onClose={() => {
            setShowSuccessDialog(false);
            handleClose();
          }}
          PaperProps={{
            sx: {
              borderRadius: "12px",
              overflow: "hidden",
              minWidth: "500px",
              maxWidth: "600px",
              minHeight: "180px",
              boxShadow: "0 8px 24px rgba(0, 0, 0, 0.15)",
              bgcolor: isDarkMode ? "background.paper" : "#fff",
              position: "relative",
              zIndex: 1300,
            },
          }}
          disableScrollLock={true}
        >
          <DialogTitle
            sx={{
              borderBottom: "1px solid",
              borderColor: isDarkMode
                ? "rgba(255, 255, 255, 0.1)"
                : "rgba(0, 0, 0, 0.1)",
              py: 2,
              px: 3,
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Typography
              variant="h6"
              sx={{
                color: theme.palette.success.main,
                fontWeight: 600,
                fontSize: "1.1rem",
              }}
            >
              Başarılı
            </Typography>
          </DialogTitle>
          <DialogContent sx={{ py: 3, px: 3 }}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                gap: 3,
                minHeight: "140px",
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  width: 48,
                  height: 48,
                  borderRadius: "50%",
                  bgcolor: alpha(theme.palette.success.main, 0.12),
                  color: theme.palette.success.main,
                  flexShrink: 0,
                  mb: 2,
                }}
              >
                {/* Başarı ikonu */}
                <svg
                  width="36"
                  height="36"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle
                    cx="12"
                    cy="12"
                    r="12"
                    fill="#4caf50"
                    fillOpacity="0.15"
                  />
                  <path
                    d="M7 13.5L10.5 17L17 10.5"
                    stroke="#4caf50"
                    strokeWidth="2.2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </Box>
              <Typography
                variant="h6"
                sx={{ fontWeight: 600, textAlign: "center", mb: 1 }}
              >
                Formunuz başarıyla gönderildi!
              </Typography>
            </Box>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};

export default QuoteForm;
