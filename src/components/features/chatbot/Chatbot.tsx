import React, { useState, useEffect } from 'react';
import ChatbotIcon from './ChatbotIcon';
import ChatWindow from './ChatWindow';
import ContactForm from './ContactForm'; // Assuming ContactForm is adapted for initial info gathering
import '../../../styles/chatbot.css';
import ChatBubble from "./ChatBubble";

interface UserInfo {
  name: string;
  email: string;
  phone: string;
  surname: string;
}

const Chatbot: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [showInitialForm, setShowInitialForm] = useState(true);
  const [contactInfo, setContactInfo] = useState<UserInfo | null>(null);
  const [hasNewMessage, setHasNewMessage] = useState(false);
  const [showWelcomeBubble, setShowWelcomeBubble] = useState(true);

  useEffect(() => {
    // Hide welcome bubble after a few seconds
    const timer = setTimeout(() => {
      if (!isOpen) {
        setShowWelcomeBubble(false);
      }
    }, 5000); // Hide after 5 seconds

    return () => clearTimeout(timer);
  }, [isOpen]);

  const handleIconClick = () => {
    setIsOpen(prev => !prev);
    setHasNewMessage(false); // Clear notification when opened
    setShowWelcomeBubble(false); // Hide bubble when icon is clicked
  };

  const handleCloseChat = () => {
    setIsOpen(false);
  };
  
  const handleNewBotMessage = () => {
    if (!isOpen) {
      setHasNewMessage(true);
    }
  };

  const handleInitialFormSubmit = (data: UserInfo) => {
    setContactInfo(data);
    setShowInitialForm(false); // Hide form and show chat window
  };

  const InitialWelcomeBubble = () => (
    <div className="initial-bubble-container">
      <div className="initial-bubble-wrapper">
        <ChatBubble
          message={{
            id: 0,
            sender: 'bot',
            text: 'Merhaba! Size nasıl yardımcı olabilirim?',
          }}
          onOptionClick={() => {}}
          onClose={() => setShowWelcomeBubble(false)}
        />
        <div className="initial-bubble-arrow" />
      </div>
    </div>
  );

  return (
    <div className="chatbot-container">
      {isOpen && (
        <>
          <div className="chatbot-mobile-overlay" onClick={handleCloseChat}></div>
          {showInitialForm ? (
            <ContactForm onSubmit={handleInitialFormSubmit} onClose={handleCloseChat} />
          ) : (
            <ChatWindow 
              onClose={handleCloseChat} 
              onNewBotMessage={handleNewBotMessage} 
              contactInfo={contactInfo} 
            />
          )}
        </>
      )}

      {showWelcomeBubble && <InitialWelcomeBubble />}

      <ChatbotIcon 
        onClick={handleIconClick} 
        isOpen={isOpen}
        hasNewMessage={hasNewMessage} 
      />
    </div>
  );
};

export default Chatbot;
