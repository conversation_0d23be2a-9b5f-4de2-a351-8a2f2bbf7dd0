import React, { useState } from 'react';

interface UserInfo {
  name: string;
  surname: string;
  email: string;
  phone: string;
}

interface ContactFormProps {
  onSubmit: (data: UserInfo) => void;
  onClose: () => void;
}

const ContactForm: React.FC<ContactFormProps> = ({ onSubmit, onClose }) => {
  const [name, setName] = useState('');
  const [surname, setSurname] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (name.trim() && surname.trim() && email.trim() && phone.trim()) {
      onSubmit({ name, surname, email, phone });
    } else {
      alert("Lütfen tüm alanları doldurun.");
    }
  };

  return (
    <div className="chatbot-window">
        <div className="chatbot-header">
            <h3>G<PERSON>r<PERSON>şmeye Başla</h3>
            <button onClick={onClose} className="close-button" aria-label="Close chat">X</button>
        </div>
        <div className="contact-form-body">
            <p>Lütfen devam etmek için bilgilerinizi giriniz.</p>
            <form onSubmit={handleSubmit} className="contact-form">
                <div className="form-group">
                    <input type="text" name="name" placeholder="Adınız" className="form-control" value={name} onChange={(e) => setName(e.target.value)} required />
                </div>
                <div className="form-group">
                    <input type="text" name="surname" placeholder="Soyadınız" className="form-control" value={surname} onChange={(e) => setSurname(e.target.value)} required />
                </div>
                <div className="form-group">
                    <input type="email" name="email" placeholder="E-posta Adresiniz" className="form-control" value={email} onChange={(e) => setEmail(e.target.value)} required />
                </div>
                <div className="form-group">
                    <input type="tel" name="phone" placeholder="Telefon Numaranız" className="form-control" value={phone} onChange={(e) => setPhone(e.target.value)} required />
                </div>
                <button type="submit" className="submit-button">
                    Görüşmeyi Başlat
                </button>
            </form>
        </div>
    </div>
  );
};

export default ContactForm;
