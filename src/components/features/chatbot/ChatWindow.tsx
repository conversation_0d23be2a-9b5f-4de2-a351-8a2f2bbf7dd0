import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ChatMessage, CampaignInfo } from '../../../types/chatbot';
import ChatBubble from './ChatBubble';
import { fetchCampaigns, fetchBrands, getAiResponse } from '../../../services/chatbotService';
import { formService } from '../../../services/formService';
import '../../../styles/chatbot.css';
import axios from 'axios';
import { createSlug } from '../../../utils/urlUtils';

interface UserInfo {
  name: string;
  surname: string;
  email: string;
  phone: string;
}

interface ChatWindowProps {
  onClose: () => void;
  onNewBotMessage: () => void;
  contactInfo: UserInfo | null;
}

const ChatInput = ({ onSendMessage, isLoading }: { onSendMessage: (message: string) => void, isLoading: boolean }) => {
    const [inputValue, setInputValue] = useState('');

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (inputValue.trim() && !isLoading) {
            onSendMessage(inputValue.trim());
            setInputValue('');
        }
    };

    return (
        <form className="chatbot-input-form" onSubmit={handleSubmit}>
            <input
                type="text"
                className="chatbot-input"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder={isLoading ? 'Cevap hazırlanıyor...' : 'Mesajınızı yazın...'}
                disabled={isLoading}
                aria-label="Chat input"
                autoComplete="off"
                autoCorrect="off"
                autoCapitalize="sentences"
                spellCheck="false"
            />
            <button 
                type="submit" 
                className="send-button" 
                disabled={isLoading || !inputValue.trim()} 
                aria-label="Send message"
            >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="white"><path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/></svg>
            </button>
        </form>
    );
};

const ChatWindow: React.FC<ChatWindowProps> = ({ onClose, onNewBotMessage, contactInfo }) => {
    const [messages, setMessages] = useState<ChatMessage[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const messagesEndRef = useRef<null | HTMLDivElement>(null);
    const navigate = useNavigate();

    const scrollToBottom = () => {
        if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({ 
                behavior: "smooth",
                block: "end"
            });
        }
    }

    useEffect(() => {
        const timer = setTimeout(scrollToBottom, 100);
        return () => clearTimeout(timer);
    }, [messages, isLoading]);
 
    const handleOfferClick = async (campaignId: number | string) => {
        if (!contactInfo) {
            alert("Lütfen önce iletişim bilgilerinizi girin.");
            return;
        }

        setIsLoading(true);
        try {
            const formData = {
                name: contactInfo.name,
                surname: contactInfo.surname,
                email: contactInfo.email,
                phoneNumber: contactInfo.phone,
                country: "Türkiye",
                city: "",
                town: "",
                gender: "",
                birthday: "",
            };

            // Duplicate kontrolü yap
            const isDuplicate = await formService.checkDuplicateSubmission(
                contactInfo.email,
                parseInt(campaignId as string, 10)
            );

            if (isDuplicate) {
                const duplicateMessage: ChatMessage = { 
                    id: Date.now(), 
                    text: "Bu kampanya için daha önce teklif aldınız. Başka bir kampanya seçebilirsiniz.", 
                    sender: 'bot' 
                };
                setMessages(prev => [...prev, duplicateMessage]);
                return;
            }

            await formService.submitFormAndCreateCampaignRelationV2(
                formData, 
                parseInt(campaignId as string, 10),
                false, // isAuthenticated
                undefined // username
            );

            const successMessage: ChatMessage = { id: Date.now(), text: `Harika! ${campaignId} ID'li kampanya için başvurunuz alınmıştır. En kısa sürede sizinle iletişime geçilecektir.`, sender: 'bot' };
            setMessages(prev => [...prev, successMessage]);

        } catch (error) {
            console.error("Error submitting offer:", error);
            const errorMessage: ChatMessage = { id: Date.now(), text: "Başvurunuz gönderilirken bir hata oluştu. Lütfen tekrar deneyin.", sender: 'bot' };
            setMessages(prev => [...prev, errorMessage]);
        } finally {
            setIsLoading(false);
        }
    };

    const submitOfferInBackground = async (campaignId: number | string) => {
        if (!contactInfo) {
            console.error("Chatbot: User info is missing, cannot submit offer.");
            return;
        }
        try {
            const formData = {
                name: contactInfo.name,
                surname: contactInfo.surname,
                email: contactInfo.email,
                phoneNumber: contactInfo.phone,
                country: "Türkiye", city: "", town: "", gender: "", birthday: "",
            };
            
            // Duplicate kontrolü yap (background'da sessizce)
            const isDuplicate = await formService.checkDuplicateSubmission(
                contactInfo.email,
                parseInt(campaignId as string, 10)
            );

            if (isDuplicate) {
                console.log(`Chatbot: Duplicate submission detected for campaign ${campaignId}`);
                return;
            }

            // This runs in the background, no need to update UI state
            await formService.submitFormAndCreateCampaignRelationV2(
                formData, 
                parseInt(campaignId as string, 10),
                false, // isAuthenticated
                undefined // username
            );
            console.log(`Chatbot: Offer submitted successfully for campaign ${campaignId}`);
        } catch (error) {
            console.error(`Chatbot: Error submitting offer for campaign ${campaignId} in background:`, error);
        }
    };

    const handleCampaignLinkClick = (campaign: CampaignInfo) => {
        // Step 1: Immediately navigate the user to the detail page.
        navigate(campaign.url);
        // Step 2: Fire off the submission in the background without waiting.
        submitOfferInBackground(campaign.id);
    };

    useEffect(() => {
        if (messages.length === 0) {
            const welcomeText = contactInfo
                ? `Merhaba ${contactInfo.name}! Size kampanyalar için hızlı bir şekilde ulaşmamız için Botumuzdaki teklif al butonlarını veya Tüm kampanyalara basarak ve ilgilendiğinz kampanyayı seçerek size ulaşılmasını sağlayabilirsiniz. İlgilendiğiniz marka adını yazarak o marka kampanyalarını da görüntüleyebilirsiniz.`
                : 'Merhaba! Size nasıl yardımcı olabilirim?';

            setMessages([
                {
                    id: 1,
                    text: welcomeText,
                    sender: 'bot',
                    options: ['Tüm Kampanyalar', 'Tüm Markalar'],
                },
            ]);
        }
    }, [contactInfo]);

    const processAiResponse = async (aiText: string) => {
        try {
            const jsonRegex = /```json\s*([\s\S]*?)\s*```/;
            const match = aiText.match(jsonRegex);

            if (match && match[1]) {
                const jsonString = match[1];
                const parsedResponse = JSON.parse(jsonString);

                if (parsedResponse.action === 'submit_lead' && parsedResponse.campaignId) {
                    if (!contactInfo) {
                        throw new Error("Kullanıcı bilgileri eksik, başvuru yapılamadı.");
                    }
                    
                    const formData = {
                        name: contactInfo.name,
                        surname: contactInfo.surname,
                        email: contactInfo.email,
                        phoneNumber: contactInfo.phone,
                        country: "Türkiye",
                        city: "", // Bu alanlar gerekirse AI'dan istenebilir.
                        town: "",
                        gender: "",
                        birthday: "",
                    };

                    // Duplicate kontrolü yap
                    const isDuplicate = await formService.checkDuplicateSubmission(
                        contactInfo.email,
                        parseInt(parsedResponse.campaignId)
                    );

                    if (isDuplicate) {
                        const duplicateMessage: ChatMessage = { 
                            id: Date.now(), 
                            text: "Bu kampanya için daha önce teklif aldınız. Başka bir kampanya seçebilirsiniz.", 
                            sender: 'bot' 
                        };
                        setMessages(prev => [...prev, duplicateMessage]);
                        return;
                    }

                    await formService.submitFormAndCreateCampaignRelationV2(
                        formData, 
                        parseInt(parsedResponse.campaignId),
                        false, // isAuthenticated
                        undefined // username
                    );

                    const successMessage: ChatMessage = { id: Date.now(), text: "Harika! Başvurunuz alınmıştır. En kısa sürede sizinle iletişime geçilecektir.", sender: 'bot' };
                    setMessages(prev => [...prev, successMessage]);

                } else if (parsedResponse.campaigns) {
                    const campaignsWithImages = await Promise.all(
                        parsedResponse.campaigns.map(async (campaign: CampaignInfo) => {
                            try {
                                const imgResponse = await axios.get(`https://360avantajli.com/api/Campaign_Service/campaign-image/by-campaign/${campaign.id}`);
                                if (imgResponse.data && imgResponse.data.length > 0) {
                                    const imagePath = imgResponse.data[0].imagePath.replace('/root', '');
                                    const fullUrl = `https://360avantajli.com${imagePath}`;
                                    return { ...campaign, imageUrl: fullUrl };
                                }
                            } catch (imgError) {
                                // Fallback
                            }
                            const fallbackImageUrl = `https://360avantajli.com/api/Campaign_Service/campaign-image/showcase/${campaign.id}`;
                            return { ...campaign, imageUrl: fallbackImageUrl };
                        })
                    );
                    const botResponse: ChatMessage = {
                        id: Date.now() + 1,
                        text: "Elbette, aradığınız kampanyaları sizin için listeledim:",
                        sender: 'bot',
                        campaigns: campaignsWithImages,
                    };
                    setMessages(prev => [...prev, botResponse]);
                } else {
                    throw new Error("Unknown JSON action");
                }
            } else {
                const botMessage: ChatMessage = { id: Date.now(), text: aiText, sender: 'bot' };
                setMessages(prev => [...prev, botMessage]);
            }
        } catch (error) {
            console.error("Error processing AI response:", error);
            const errorMessage: ChatMessage = { id: Date.now(), text: "Üzgünüm, bir hata oluştu. Lütfen daha sonra tekrar deneyin.", sender: 'bot' };
            setMessages(prev => [...prev, errorMessage]);
        }
    };

    const handleUserRequest = async (text: string, isOption: boolean) => {
        const userMessage: ChatMessage = { id: Date.now(), text, sender: 'user' };
        setMessages(prev => [...prev, userMessage]);
        setIsLoading(true);

        if (isOption) {
             // Handle simple options without AI
             let botResponse: ChatMessage;
             if (text === 'Tüm Kampanyalar') {
                 const rawCampaigns = await fetchCampaigns();
                 const campaignsAsInfo: CampaignInfo[] = rawCampaigns.map(c => ({
                    ...c,
                    title: c.title || '', // Ensure title is always a string
                    url: `/kampanyalar/${createSlug(c.name)}`,
                    imageUrl: `https://360avantajli.com/api/Campaign_Service/campaign-image/showcase/${c.id}`
                 }));

                const campaignListElement = campaignsAsInfo.length > 0
                    ? (
                        <ul className="campaign-link-list">
                            {campaignsAsInfo.slice(0, 5).map(c => (
                                <li key={c.id}>
                                    <a href={c.url} onClick={(e) => { e.preventDefault(); handleCampaignLinkClick(c); }}>
                                        {c.name}
                                    </a>
                                </li>
                            ))}
                        </ul>
                    )
                    : 'Şu anda aktif kampanya bulunmamaktadır.';

                 botResponse = { 
                     id: Date.now() + 1, 
                     text: <div>İşte bazı kampanyalar:<br/>{campaignListElement}</div>,
                     sender: 'bot', 
                     options: ['Tüm Markalar'] 
                 };
             } else if (text === 'Tüm Markalar') {
                 const brands = await fetchBrands();
                 const brandText = brands.length > 0
                     ? <ul>{brands.slice(0, 5).map(b => <li key={b.id}><Link to={`/marka/${createSlug(b.name)}`}>{b.name}</Link></li>)}</ul>
                     : 'Sistemde kayıtlı marka bulunmamaktadır.';
                 botResponse = { id: Date.now() + 1, text: <div>İşte bazı markalar:<br/>{brandText}</div>, sender: 'bot', options: ['Tüm Kampanyalar'] };
             } else {
                 // Fallback for other options, or send to AI
                 botResponse = { id: Date.now() + 1, text: `${text} seçeneği ile ilgili işlem yapılıyor...`, sender: 'bot'};
             }
             setMessages(prev => [...prev, botResponse]);

        } else {
            const currentHistory = messages.map(msg => ({
                role: msg.sender === 'user' ? 'user' : 'model',
                parts: [{ text: typeof msg.text === 'string' ? msg.text : "Kullanıcı bir seçenek belirledi." }]
            }));
            
            const aiText = await getAiResponse(text, currentHistory as any, contactInfo);
            await processAiResponse(aiText);
        }
        
        onNewBotMessage();
        setIsLoading(false);
    };

    return (
        <div className="chatbot-window">
            <div className="chatbot-header">
                <h3>Kampanya Avcısı</h3>
                <button onClick={onClose} className="close-button" aria-label="Close chat">X</button>
            </div>
            <div className="chatbot-messages">
                {messages.map(msg => (
                    <ChatBubble key={msg.id} message={msg} onOptionClick={(option) => handleUserRequest(option, true)} onOfferClick={handleOfferClick} />
                ))}

                {isLoading && (
                    <div className="chat-bubble bot">
                        <div className="typing-indicator">
                            <span></span><span></span><span></span>
                        </div>
                    </div>
                )}
                <div ref={messagesEndRef} />
            </div>
            <ChatInput onSendMessage={(message) => handleUserRequest(message, false)} isLoading={isLoading} />
        </div>
    );
};

export default ChatWindow; 