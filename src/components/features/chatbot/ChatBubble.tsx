import React, { useState } from 'react';
import Slider from 'react-slick';
import { ChatMessage } from '../../../types/chatbot';
import CampaignCard from './CampaignCard';
import '../../../styles/chatbot.css';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

interface ChatBubbleProps {
  message: ChatMessage;
  onOptionClick: (option: string) => void;
  onOfferClick?: (campaignId: number | string) => void;
  onClose?: () => void;
}

const ChatBubble: React.FC<ChatBubbleProps> = ({ message, onOptionClick, onOfferClick, onClose }) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isMobile, setIsMobile] = useState(false);

  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const totalCampaigns = message.campaigns?.length ?? 0;
  const hasCampaigns = totalCampaigns > 0;

  const sliderSettings = {
    dots: true,
    infinite: false,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: !isMobile,
    afterChange: (current: number) => setCurrentSlide(current),
    responsive: [
      {
        breakpoint: 768,
        settings: {
          arrows: false,
          dots: true,
        },
      },
    ],
  };

  const handlePrev = () => {
    setCurrentSlide(prevIndex => (prevIndex === 0 ? totalCampaigns - 1 : prevIndex - 1));
  };

  const handleNext = () => {
    setCurrentSlide(prevIndex => (prevIndex === totalCampaigns - 1 ? 0 : prevIndex + 1));
  };

  return (
    <div className={`chat-bubble ${message.sender} ${hasCampaigns ? 'has-campaigns' : ''}`} style={{ position: 'relative' }}>
        {onClose && (
          <button
            onClick={onClose}
            className="chat-bubble-close-button"
            aria-label="Close message"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18" stroke="#6B7280" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M6 6L18 18" stroke="#6B7280" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        )}
        {message.text && !hasCampaigns && <div className="message-text">{message.text}</div>}
        
        {message.options && (
            <div className="message-options">
            {message.options.map((option, index) => (
              <button key={index} onClick={() => onOptionClick(option)} className="option-button">
                {option}
              </button>
            ))}
            </div>
        )}

        {hasCampaigns && onOfferClick && (
            <div className="campaign-cards-container">
                {message.text && <p className="message-text" style={{marginBottom: '10px', display: 'inline-block'}}>{message.text}</p>}
                {isMobile ? (
                    <div className="custom-mobile-slider">
                      <div
                        className="slider-content-wrapper"
                        style={{ transform: `translateX(-${currentSlide * 100}%)` }}
                      >
                        {message.campaigns?.map((campaign, index) => (
                          <div key={index} className="slider-card">
                            <CampaignCard campaign={campaign} onOfferClick={onOfferClick} />
                          </div>
                        ))}
                      </div>
                      {totalCampaigns > 1 && (
                        <>
                          <button onClick={handlePrev} className="custom-slider-arrow prev" aria-label="Önceki">‹</button>
                          <button onClick={handleNext} className="custom-slider-arrow next" aria-label="Sonraki">›</button>
                          <div className="slider-counter">
                            {currentSlide + 1} / {totalCampaigns}
                          </div>
                        </>
                      )}
                    </div>
                ) : (
                    <Slider {...sliderSettings}>
                      {message.campaigns?.map((campaign, index) => (
                        <div key={index}>
                          <CampaignCard campaign={campaign} onOfferClick={onOfferClick}/>
                        </div>
                      ))}
                    </Slider>
                )}
                 {!isMobile && totalCampaigns > 1 && (
                    <div className="slider-counter">
                        {currentSlide + 1} / {totalCampaigns}
                    </div>
                 )}
            </div>
        )}
    </div>
  );
};

export default ChatBubble; 