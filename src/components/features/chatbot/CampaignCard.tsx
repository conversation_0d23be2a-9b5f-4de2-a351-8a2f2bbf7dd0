import React from 'react';
import { Link } from 'react-router-dom';
import { CampaignInfo } from '../../../types/chatbot';
import { FaArrowRight } from "react-icons/fa";

interface CampaignCardProps {
  campaign: CampaignInfo;
  onOfferClick: (campaignId: number | string) => void;
}

const CampaignCard: React.FC<CampaignCardProps> = ({ campaign, onOfferClick }) => {

  const handleOfferButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation(); // Prevents the Link from navigating
    e.preventDefault(); // Prevents default button behavior
    onOfferClick(campaign.id);
  };

  return (
    <Link to={campaign.url} className="campaign-card-link">
      <div className="campaign-card">
        {campaign.imageUrl ? (
          <img src={campaign.imageUrl} alt={campaign.name} className="campaign-card-image" loading="lazy" />
        ) : (
          <div className="campaign-card-image-placeholder" />
        )}
        <div className="campaign-card-content">
          <h4>{campaign.name}</h4>
          <p>{campaign.title}</p>
          <button onClick={handleOfferButtonClick} className="campaign-card-button">
            Teklif Al <FaArrowRight style={{ marginLeft: '8px' }}/>
          </button>
        </div>
      </div>
    </Link>
  );
};

export default CampaignCard; 