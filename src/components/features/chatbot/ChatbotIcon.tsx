import React, { useState } from 'react';
import '../../../styles/chatbot.css';

interface ChatbotIconProps {
  onClick: () => void;
  hasNewMessage: boolean;
  isOpen: boolean;
}

const ChatbotIcon: React.FC<ChatbotIconProps> = ({ onClick, hasNewMessage, isOpen }) => {
  const [isHovered, setIsHovered] = useState(false);
  
  return (
    <div 
      className="chatbot-icon-container" 
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      aria-label="Chat with us"
    >
      <div className={`chatbot-icon ${isHovered ? 'chatbot-icon-hover' : ''}`}>
        {isOpen ? (
          <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="white" className="chat-icon">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        ) : (
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            width="28" 
            height="28" 
            viewBox="0 0 24 24" 
            fill="white"
            className="chat-icon"
          >
            <path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H5.17L4 17.17V4h16v12z"/>
            <path d="M7 9h10v2H7zm0 3h7v2H7z" fill="white"/>
          </svg>
        )}
      </div>
      {hasNewMessage && !isOpen && <span className="new-message-indicator"></span>}
    </div>
  );
};

export default ChatbotIcon; 