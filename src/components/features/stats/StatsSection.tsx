import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Paper,
  Typography,
  CircularProgress,
  alpha,
} from '@mui/material';
import { useIntl } from 'react-intl';
import {
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  LocalOffer as LocalOfferIcon,
  Category as CategoryIcon,
} from '@mui/icons-material';
import { useCategories } from '../../../contexts/CategoryContext';
import axios from 'axios';
import { useTheme } from '@mui/material/styles';
import { isExpired, standardizeCampaignEndDate } from '../../../utils/dateUtils';

export const StatsSection: React.FC = () => {
  const intl = useIntl();
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  // Context'ten kategorileri al
  const { allCategories } = useCategories();

  // State'leri tanımla
  const [activeCampaigns, setActiveCampaigns] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);

  // Toplam kategori sayısını hesapla
  const totalCategories = allCategories.length;

  // Kampanyaları API'den çek ve aktif olanları say
  useEffect(() => {
    const fetchCampaigns = async () => {
      try {
        setLoading(true);
        const response = await axios.get('https://360avantajli.com/api/Campaign_Service/campaign');

        if (response.data && Array.isArray(response.data)) {
          // Sadece aktif kampanyaları filtrele (süresi geçmemiş olanlar)
          const activeCampaignsList = response.data.filter((campaign: { endDate?: string; updatedAt?: string | null }) => {
            if (!campaign.endDate) return false;

            // Bitiş tarihini standardize et
            const standardizedEndDate = standardizeCampaignEndDate(campaign.endDate);

            // Süresi geçmemiş kampanyaları say
            return !isExpired(standardizedEndDate);
          });

          setActiveCampaigns(activeCampaignsList.length);
        } else {
          setActiveCampaigns(0);
        }
      } catch (error) {
        console.error('Kampanyalar yüklenirken hata oluştu:', error);
        setActiveCampaigns(0);
      } finally {
        setLoading(false);
      }
    };

    fetchCampaigns();
  }, []);

  const stats = [
    {
      icon: <TrendingUpIcon sx={{ fontSize: 30 }} />,
      value: '1M+',
      label: intl.formatMessage({ id: 'home.stats.activeUsers' }),
    },
    {
      icon: <PeopleIcon sx={{ fontSize: 30 }} />,
      value: '500K+',
      label: intl.formatMessage({ id: 'home.stats.happyCustomers' }),
    },
    {
      icon: <LocalOfferIcon sx={{ fontSize: 30 }} />,
      value: `${activeCampaigns}+`,
      label: intl.formatMessage({ id: 'home.stats.activeCampaigns' }),
    },
    {
      icon: <CategoryIcon sx={{ fontSize: 30 }} />,
      value: `${totalCategories}+`,
      label: intl.formatMessage({ id: 'home.stats.totalCategories' }),
    },
  ];

  return (
    <Box
      sx={{
        py: { xs: 2, sm: 3 },
        background: 'transparent',
        width: '100%',
        maxWidth: '100%',
        overflow: 'hidden',
      }}
    >
      <Container 
        maxWidth="lg" 
        sx={{ 
          px: { xs: 2, sm: 3 },
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 2, width: '100%' }}>
            <CircularProgress size={22} />
          </Box>
        ) : (
          <Grid 
            container 
            spacing={{ xs: 1.5, sm: 2 }} 
            justifyContent="space-between"
            sx={{
              maxWidth: '1000px', // Kartların maksimum genişliğini sınırla
              mx: 'auto', // Otomatik margin ile ortalama
            }}
          >
            {stats.map((stat, index) => (
              <Grid 
                item 
                xs={5} // Daha az genişlik vererek yan yana daha iyi sığsın
                sm={2.8} // 4 kart yan yana olacak şekilde ayarlandı
                key={index} 
                sx={{ 
                  display: 'flex',
                  justifyContent: 'center',
                  mb: 1,
                }}
              >
                <Paper
                  elevation={0}
                  sx={{
                    p: { xs: 1.5, sm: 2 },
                    textAlign: 'center',
                    width: '100%',
                    maxWidth: '220px', // Maksimum genişlik ekledim
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: isDarkMode 
                      ? `linear-gradient(145deg, ${alpha('#fff', 0.05)} 0%, ${alpha('#fff', 0.02)} 100%)`
                      : '#ffffff',
                    borderRadius: 2,
                    border: isDarkMode ? `1px solid ${alpha('#fff', 0.08)}` : '1px solid rgba(0, 0, 0, 0.05)',
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      transform: { xs: 'none', sm: 'translateY(-2px)' },
                      boxShadow: { xs: 'none', sm: '0 4px 12px rgba(0,0,0,0.05)' },
                    },
                  }}
                >
                  <Box
                    sx={{
                      color: theme.palette.primary.main,
                      mb: { xs: 0.5, sm: 1 },
                      '& .MuiSvgIcon-root': {
                        fontSize: { xs: '1.75rem', sm: '2rem' },
                      },
                    }}
                  >
                    {stat.icon}
                  </Box>
                  <Typography
                    variant="h6"
                    component="div"
                    sx={{
                      fontWeight: 700,
                      mb: { xs: 0.25, sm: 0.5 },
                      fontSize: { xs: '1.25rem', sm: '1.5rem' },
                      color: isDarkMode ? 'grey.100' : 'grey.900',
                      lineHeight: 1.2,
                      whiteSpace: 'nowrap', // Metnin tek satırda kalmasını sağlar
                    }}
                  >
                    {stat.value}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      color: isDarkMode ? 'grey.400' : 'text.secondary',
                      textAlign: 'center',
                      fontSize: { xs: '0.75rem', sm: '0.85rem' },
                      lineHeight: 1.2,
                      display: 'block',
                      whiteSpace: 'nowrap', // Metnin tek satırda kalmasını sağlar
                    }}
                  >
                    {stat.label}
                  </Typography>
              </Paper>
            </Grid>
          ))}
        </Grid>
        )}
      </Container>
    </Box>
  );
};