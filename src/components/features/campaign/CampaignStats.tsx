import React from 'react';
import {
  <PERSON>,
  Typography,
  Tooltip,
  IconButton,
  useTheme,
} from '@mui/material';
import {
  RemoveRedEye as ViewIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  Share as ShareIcon,
} from '@mui/icons-material';
import { useIntl } from 'react-intl';
import { alpha } from '@mui/material/styles';

interface CampaignStatsProps {
  views: number;
  followers: number;
  isFollowing?: boolean;
  onFollow?: () => void;
  onShare?: () => void;
}

export const CampaignStats: React.FC<CampaignStatsProps> = ({
  views,
  followers,
  isFollowing = false,
  onFollow,
  onShare,
}) => {
  const theme = useTheme();
  const intl = useIntl();
  const isDarkMode = theme.palette.mode === 'dark';

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}B`;
    }
    return num.toString();
  };

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 3,
        p: 2,
        borderRadius: 3,
        background: isDarkMode 
          ? `linear-gradient(145deg, ${alpha('#fff', 0.08)} 0%, ${alpha('#fff', 0.04)} 100%)`
          : `linear-gradient(145deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.primary.main, 0.02)} 100%)`,
        backdropFilter: 'blur(10px)',
        transition: 'all 0.3s ease',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          color: isDarkMode ? 'grey.300' : 'grey.700',
          transition: 'all 0.3s ease',
        }}
      >
        <ViewIcon fontSize="small" />
        <Typography variant="body2">
          {formatNumber(views)} {intl.formatMessage({ id: 'campaign.views' })}
        </Typography>
      </Box>

      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
          }}
        >
          <Tooltip
            title={
              isFollowing
                ? intl.formatMessage({ id: 'campaign.unfollow' })
                : intl.formatMessage({ id: 'campaign.follow' })
            }
          >
            <IconButton
              onClick={onFollow}
              size="small"
              sx={{
                color: isFollowing ? 'error.main' : isDarkMode ? 'grey.300' : 'grey.700',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'scale(1.1)',
                  color: 'error.main',
                  bgcolor: isFollowing
                    ? alpha(theme.palette.error.main, 0.1)
                    : isDarkMode
                    ? alpha('#fff', 0.05)
                    : alpha('#000', 0.05),
                },
              }}
            >
              {isFollowing ? <FavoriteIcon /> : <FavoriteBorderIcon />}
            </IconButton>
          </Tooltip>
          <Typography
            variant="body2"
            sx={{
              color: isFollowing ? 'error.main' : isDarkMode ? 'grey.300' : 'grey.700',
              fontWeight: 500,
              minWidth: '40px',
              transition: 'all 0.3s ease',
            }}
          >
            {formatNumber(followers)}
          </Typography>
        </Box>

        <Tooltip title={intl.formatMessage({ id: 'campaign.share' })}>
          <IconButton
            onClick={onShare}
            size="small"
            sx={{
              color: isDarkMode ? 'grey.300' : 'grey.700',
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'scale(1.1)',
                color: theme.palette.primary.main,
                bgcolor: isDarkMode ? alpha('#fff', 0.05) : alpha('#000', 0.05),
              },
            }}
          >
            <ShareIcon />
          </IconButton>
        </Tooltip>
      </Box>
    </Box>
  );
}; 