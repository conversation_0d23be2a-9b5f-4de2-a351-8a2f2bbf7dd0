import React from "react";
import { Box, Typography } from "@mui/material";
import { Link as RouterLink } from "react-router-dom";

interface CampaignDetailProps {
  brand: string;
  logoUrl?: string;
  brandId?: string;
  brandUrl?: string;
}

const CampaignDetail: React.FC<CampaignDetailProps> = ({
  brand,
  logoUrl,
  brandId,
  brandUrl,
}) => {
  // Determine the brand slug for navigation
  const getBrandSlug = (): string => {
    // Use brand name as first priority if it's not a number
    if (brand && !/^\d+$/.test(brand)) {
      // Use proper Turkish character handling
      return brand
        .toLowerCase()
        .replace(/ç/g, "c")
        .replace(/ğ/g, "g")
        .replace(/ı/g, "i")
        .replace(/ö/g, "o")
        .replace(/ş/g, "s")
        .replace(/ü/g, "u")
        .replace(/\s+/g, "-")
        .replace(/[^a-z0-9-]+/g, "-")
        .replace(/^-+|-+$/g, "");
    }
    // Use brandId as second priority
    else if (brandId) {
      return brandId;
    }
    // Only use brandUrl if it's not a full URL
    else if (brandUrl && !brandUrl.includes("://")) {
      return brandUrl;
    }
    // Fallback to brand (even if it's a number) or unknown
    return brand || "unknown";
  };

  return (
    <Box
      component={RouterLink}
      to={`/marka/${getBrandSlug()}`}
      sx={{
        display: "flex",
        alignItems: "center",
        textDecoration: "none",
        mb: 2,
        "&:hover": {
          "& .brand-logo": {
            transform: "scale(1.05)",
            boxShadow: "0 6px 16px rgba(0,0,0,0.25)",
          },
          "& .brand-name": {
            transform: "translateY(-2px)",
            boxShadow: "0 4px 12px rgba(0,0,0,0.2)",
          },
        },
      }}
    >
      <Box
        className="brand-logo"
        sx={{
          width: 48,
          height: 48,
          borderRadius: "50%",
          backgroundColor: "white",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          padding: "4px",
          boxShadow: "0 4px 12px rgba(0,0,0,0.2)",
          transition: "transform 0.2s ease, box-shadow 0.2s ease",
        }}
      >
        <img
          src={logoUrl || "/placeholder-logo.jpg"}
          alt={`${brand} logo`}
          style={{
            width: "100%",
            height: "100%",
            objectFit: "contain",
            borderRadius: "50%",
          }}
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.onerror = null;
            target.src = "/placeholder-logo.jpg";
          }}
        />
      </Box>

      <Typography
        className="brand-name"
        variant="h6"
        sx={{
          ml: 2,
          px: 2.5,
          py: 1,
          borderRadius: 2,
          backgroundColor: "rgba(255, 255, 255, 0.9)",
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
          fontWeight: 600,
          color: "text.primary",
          maxWidth: "100%",
          whiteSpace: "nowrap",
          overflow: "hidden",
          textOverflow: "ellipsis",
          transition: "transform 0.2s ease, box-shadow 0.2s ease",
        }}
      >
        {brand}
      </Typography>
    </Box>
  );
};

export default CampaignDetail;
