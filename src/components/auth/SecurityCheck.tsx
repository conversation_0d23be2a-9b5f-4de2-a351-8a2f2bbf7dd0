import React, { useEffect } from 'react';
import { checkCookieSecurity } from '../../utils/cookieUtils';

/**
 * SecurityCheck bileşeni
 * 
 * Bu bileşen, güvenlik kontrollerini yapar ve gerekirse uyarı gösterir.
 * Geçici CORS çözümü için bu bileşen herhangi bir işlem yapmaz.
 */
const SecurityCheck: React.FC = () => {
  useEffect(() => {
    // Geçici CORS çözümü için güvenlik kontrollerini devre dışı bıraktık
    // Normalde burada cookie güvenliği kontrol edilir
    const isSecure = checkCookieSecurity();
    
    // Geliştirme ortamında güvenlik uyarılarını gösterme
    if (process.env.NODE_ENV !== 'development' && !isSecure) {
      // Güvenlik sorunu varsa konsola uyarı yaz
      console.warn('Security warning: Cookie security checks failed');
    }
  }, []);

  // Bu bileşen herhangi bir UI render etmez
  return null;
};

export default SecurityCheck;
