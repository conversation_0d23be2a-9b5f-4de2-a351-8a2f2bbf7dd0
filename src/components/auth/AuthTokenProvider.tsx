import React, { useEffect } from 'react';
// import { useAuth } from '../../contexts/AuthContext'; // Kaldırıldı, artık kullanılmıyor

/**
 * AuthTokenProvider bileşeni
 *
 * Bu bileşen, HttpOnly cookie yapısına geçişle birlikte güncellenmiştir.
 * Token yönetimi artık büyük ölçüde backend ve tarayıcı tarafından HttpOnly cookie'ler aracılığıyla yapılmaktadır.
 * AuthContext, oturum durumunu ve logout olaylarını yönetmektedir.
 */
const AuthTokenProvider: React.FC = () => {
  // const { logout } = useAuth(); // logout çağrısı AuthContext içindeki event listener ile çakışabilir

  useEffect(() => {
    // HttpOnly cookie'lere geçişle birlikte bu bileşendeki spesifik event listener'lara
    // (auth:token-refreshed) ihtiyaç kalmamıştır.
    // 'auth:logout' eventi artık doğrudan AuthContext içinde dinlenmektedir
    // ve kullanıcı state'ini temizlemektedir.

    // Gerekirse, gelecekte buraya farklı global auth event'leri için listener'lar eklenebilir.

    // Cleanup fonksiyonu boş kalabilir veya kaldırılabilir
    return () => {};
  }, []); // Bağımlılık dizisi boşaltıldı veya useAuth'dan alınan fonksiyonlara göre düzenlenebilir

  // Bu bileşen herhangi bir UI render etmez
  return null;
};

export default AuthTokenProvider;
