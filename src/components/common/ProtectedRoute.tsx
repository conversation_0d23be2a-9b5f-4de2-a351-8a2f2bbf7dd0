import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?:
    | "ADMIN"
    | "USER"
    | "CRM_ADMIN"
    | ("ADMIN" | "USER" | "CRM_ADMIN")[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
}) => {
  const { isAuthenticated, user, isLoading } = useAuth();
  const location = useLocation();



  // Auth durumu yüklenirken loading göster
  if (isLoading) {
  
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh'
      }}>
        Loading...
      </div>
    );
  }

  // Admin sayfaları için özel kontrol
  if (location.pathname.startsWith("/admin")) {
    // Kullanıcı giriş yapmamışsa veya admin değilse, doğrudan /test sayfasına yönlendir
    if (
      !isAuthenticated ||
      (requiredRole === "ADMIN" && user?.role !== "ADMIN")
    ) {
      return <Navigate to="/" replace />;
    }
  }
  // CRM Admin sayfaları için özel kontrol
  else if (location.pathname.startsWith("/crm-admin")) {
    // Kullanıcı giriş yapmamışsa veya gerekli role sahip değilse, ana sayfaya yönlendir
    if (!isAuthenticated) {
      return <Navigate to="/" replace />;
    }

    // Role kontrolü - array veya string olabilir
    if (requiredRole) {
      const allowedRoles = Array.isArray(requiredRole)
        ? requiredRole
        : [requiredRole];
      if (!allowedRoles.includes(user?.role as any)) {
        return <Navigate to="/" replace />;
      }
    }
  } else {
    // Admin/CRM Admin sayfası değilse ve kullanıcı giriş yapmamışsa, login sayfasına yönlendir
    if (!isAuthenticated) {
      // Mevcut konumu state olarak kaydet, böylece giriş yaptıktan sonra geri dönebilir
      return <Navigate to="/login" state={{ from: location }} replace />;
    }
  }

  // Kullanıcı giriş yapmış ve gerekli role sahipse, içeriği göster
  return <>{children}</>;
};

export default ProtectedRoute;
