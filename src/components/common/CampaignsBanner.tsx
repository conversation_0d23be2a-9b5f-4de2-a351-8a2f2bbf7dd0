import React from 'react';
import { Container, Typography, Box } from '@mui/material';
import { useIntl } from 'react-intl';

const CampaignsBanner: React.FC = () => {
  const intl = useIntl();
  return (
    <Box
      sx={{
        width: '100%',
        bgcolor: 'primary.main',
        py: { xs: 5, sm: 7, md: 8 },
        px: 0,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'column',
        minHeight: { xs: 180, sm: 220, md: 260 },
      }}
    >
      <Container maxWidth="lg">
        <Typography
          variant="h2"
          component="h1"
          align="center"
          gutterBottom
          sx={{
            fontWeight: 800,
            color: '#fff',
            fontSize: {
              xs: '1.75rem',
              sm: '2.25rem',
              md: '2.5rem',
            },
            textShadow: '0 2px 8px rgba(0,0,0,0.10)',
          }}
        >
          {intl.formatMessage({ id: 'home.hero.title' })}
        </Typography>
        <Typography
          variant="h5"
          align="center"
          sx={{
            maxWidth: {
              xs: '100%',
              sm: '600px',
              md: '800px',
            },
            mx: 'auto',
            color: '#fff',
            textShadow: '0 2px 4px rgba(0,0,0,0.2)',
            backgroundColor: 'rgba(0,0,0,0.08)',
            padding: {
              xs: '12px',
              sm: '14px',
              md: '16px',
            },
            borderRadius: {
              xs: '6px',
              sm: '7px',
              md: '8px',
            },
            backdropFilter: 'blur(4px)',
            fontWeight: 500,
            letterSpacing: '0.5px',
            lineHeight: 1.4,
            fontSize: {
              xs: '1rem',
              sm: '1.125rem',
              md: '1.25rem',
            },
            mt: 2,
          }}
        >
          {intl.formatMessage({ id: 'home.hero.subtitle' })}
        </Typography>
      </Container>
    </Box>
  );
};

export default CampaignsBanner; 