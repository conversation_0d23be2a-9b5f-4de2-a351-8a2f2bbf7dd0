import React from "react";
import { Box, Container, useTheme, useMediaQuery } from "@mui/material";
import { SxProps, Theme } from "@mui/material/styles";

interface ResponsiveWrapperProps {
  children: React.ReactNode;
  maxWidth?: "xs" | "sm" | "md" | "lg" | "xl" | false;
  disableGutters?: boolean;
  sx?: SxProps<Theme>;
  component?: React.ElementType;
  className?: string;
}

/**
 * ResponsiveWrapper - Tüm sayfalarda tutarlı responsive padding ve ortalama sağlar
 */
const ResponsiveWrapper: React.FC<ResponsiveWrapperProps> = ({
  children,
  maxWidth = "lg",
  disableGutters = false,
  sx = {},
  component = "div",
  className,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isTablet = useMediaQuery(theme.breakpoints.between("sm", "md"));

  const responsiveSx: SxProps<Theme> = {
    width: "100%",
    margin: "0 auto",
    px: disableGutters
      ? 0
      : {
          xs: 1, // 8px - mobil için daha az padding
          sm: 1.5, // 12px
          md: 2, // 16px
          lg: 2.5, // 20px
          xl: 3, // 24px
        },
    py: {
      xs: 1, // 8px - mobil için daha az padding
      sm: 1.5, // 12px
      md: 2, // 16px
      lg: 2.5, // 20px
    },
    boxSizing: "border-box",
    // Mobil görünümde overflow'u önle
    overflow: "hidden",
    // Mobil görünümde minimum height
    minHeight: isMobile ? "auto" : "inherit",
    ...sx,
  };

  if (maxWidth) {
    return (
      <Container
        maxWidth={maxWidth}
        disableGutters={disableGutters}
        component={component}
        className={className}
        sx={responsiveSx}
      >
        {children}
      </Container>
    );
  }

  return (
    <Box component={component} className={className} sx={responsiveSx}>
      {children}
    </Box>
  );
};

export default ResponsiveWrapper;
