import React from "react";
import {
  Box,
  Container,
  Breadcrumbs,
  <PERSON> as Mu<PERSON><PERSON><PERSON>,
  Typography,
} from "@mui/material";
import { NavigateNext as NavigateNextIcon } from "@mui/icons-material";

interface BreadcrumbItem {
  label: string;
  to?: string;
}

interface ModernBreadcrumbsProps {
  items: BreadcrumbItem[];
  maxWidth?: "xs" | "sm" | "md" | "lg" | "xl" | false;
}

const ModernBreadcrumbs: React.FC<ModernBreadcrumbsProps> = ({
  items,
  maxWidth = "lg",
}) => {
  return (
    <Box
      sx={{
        background:
          "linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)",
        borderBottom: "1px solid rgba(102, 126, 234, 0.1)",
        py: { xs: 3, md: 4 },
        mb: 4,
      }}
    >
      <Container maxWidth={maxWidth}>
        <Breadcrumbs
          separator={<NavigateNextIcon fontSize="small" />}
          aria-label="breadcrumb"
          sx={{
            "& .MuiBreadcrumbs-separator": {
              mx: 1,
              color: "rgba(102, 126, 234, 0.6)",
            },
            "& .MuiBreadcrumbs-ol": {
              flexWrap: "wrap",
            },
          }}
        >
          {items.map((item, idx) =>
            idx < items.length - 1 && item.to ? (
              <MuiLink
                key={item.to}
                href={item.to}
                sx={{
                  color: "rgba(102, 126, 234, 0.7)",
                  fontSize: "0.9rem",
                  fontWeight: 500,
                  transition: "all 0.3s ease",
                  padding: "4px 8px",
                  borderRadius: "4px",
                  textDecoration: "none",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.color = "#667eea";
                  e.currentTarget.style.backgroundColor =
                    "rgba(102, 126, 234, 0.1)";
                  e.currentTarget.style.transform = "translateY(-1px)";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.color = "rgba(102, 126, 234, 0.7)";
                  e.currentTarget.style.backgroundColor = "transparent";
                  e.currentTarget.style.transform = "translateY(0)";
                }}
              >
                {item.label}
              </MuiLink>
            ) : (
              <Typography
                key={idx}
                sx={{
                  fontSize: "0.9rem",
                  fontWeight: 700,
                  color: "#667eea",
                  padding: "4px 8px",
                  borderRadius: 1,
                  background: "rgba(102, 126, 234, 0.1)",
                }}
              >
                {item.label}
              </Typography>
            )
          )}
        </Breadcrumbs>
      </Container>
    </Box>
  );
};

export default ModernBreadcrumbs;
