import React, { useState, useEffect, useCallback } from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  FormHelperText,
  Grid,
  CircularProgress,
  Box,
} from '@mui/material';
import { useIntl } from 'react-intl';
import turkiyeApiService, { Province, District } from '../../services/turkiyeApiService';

interface CityDistrictSelectorProps {
  cityValue: string;
  districtValue: string;
  onCityChange: (city: string) => void;
  onDistrictChange: (district: string) => void;
  cityError?: string;
  districtError?: string;
  required?: boolean;
  disabled?: boolean;
  size?: 'small' | 'medium';
  gridSize?: {
    city?: number;
    district?: number;
  };
}

const CityDistrictSelector: React.FC<CityDistrictSelectorProps> = ({
  cityValue,
  districtValue,
  onCityChange,
  onDistrictChange,
  cityError = '',
  districtError = '',
  required = false,
  disabled = false,
  size = 'medium',
  gridSize = { city: 6, district: 6 },
}) => {
  const intl = useIntl();
  const [provinces, setProvinces] = useState<Province[]>([]);
  const [districts, setDistricts] = useState<District[]>([]);
  const [loading, setLoading] = useState({
    provinces: true,
    districts: false,
  });

  // Load provinces on component mount
  useEffect(() => {
    const loadProvinces = async () => {
      try {
        setLoading(prev => ({ ...prev, provinces: true }));
        const provincesData = await turkiyeApiService.getProvinces();
        setProvinces(provincesData);
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('Failed to load provinces:', error);
        }
      } finally {
        setLoading(prev => ({ ...prev, provinces: false }));
      }
    };

    loadProvinces();
  }, []);

  // Load districts when city changes
  const loadDistricts = useCallback(async (cityName: string) => {
    if (!cityName) {
      setDistricts([]);
      return;
    }

    try {
      setLoading(prev => ({ ...prev, districts: true }));
      
      // Find province by name
      const province = provinces.find(p => p.name === cityName);
      if (!province) {
        setDistricts([]);
        return;
      }

      const districtsData = await turkiyeApiService.getDistricts(province.id);
      setDistricts(districtsData);
      
      // If current district is not in the new districts list, clear it
      if (districtValue && !districtsData.find(d => d.name === districtValue)) {
        onDistrictChange('');
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('Failed to load districts:', error);
      }
      setDistricts([]);
    } finally {
      setLoading(prev => ({ ...prev, districts: false }));
    }
  }, [provinces, districtValue, onDistrictChange]);

  // Load districts when city value or provinces change
  useEffect(() => {
    if (cityValue && provinces.length > 0) {
      loadDistricts(cityValue);
    } else {
      setDistricts([]);
    }
  }, [cityValue, provinces, loadDistricts]);

  const handleCityChange = (event: SelectChangeEvent<string>) => {
    const newCity = event.target.value;
    onCityChange(newCity);
    
    // Clear district when city changes
    if (districtValue) {
      onDistrictChange('');
    }
  };

  const handleDistrictChange = (event: SelectChangeEvent<string>) => {
    onDistrictChange(event.target.value);
  };

  return (
    <Grid container spacing={2}>
      {/* City Selector */}
      <Grid item xs={12} sm={gridSize.city}>
        <FormControl 
          fullWidth 
          error={!!cityError} 
          required={required}
          disabled={disabled}
          size={size}
        >
          <InputLabel>
            {intl.formatMessage({ 
              id: 'form.city', 
              defaultMessage: 'Şehir' 
            })}
          </InputLabel>
          <Select
            value={cityValue}
            onChange={handleCityChange}
            label={intl.formatMessage({ 
              id: 'form.city', 
              defaultMessage: 'Şehir' 
            })}
            disabled={disabled || loading.provinces}
            endAdornment={
              loading.provinces ? (
                <Box sx={{ mr: 2 }}>
                  <CircularProgress size={20} />
                </Box>
              ) : null
            }
          >
            <MenuItem value="">
              <em>
                {intl.formatMessage({ 
                  id: 'form.selectCity', 
                  defaultMessage: 'Şehir Seçiniz' 
                })}
              </em>
            </MenuItem>
            {provinces.map((province) => (
              <MenuItem key={province.id} value={province.name}>
                {province.name}
              </MenuItem>
            ))}
          </Select>
          {cityError && (
            <FormHelperText>{cityError}</FormHelperText>
          )}
        </FormControl>
      </Grid>

      {/* District Selector */}
      <Grid item xs={12} sm={gridSize.district}>
        <FormControl 
          fullWidth 
          error={!!districtError} 
          required={required}
          disabled={disabled || !cityValue}
          size={size}
        >
          <InputLabel>
            {intl.formatMessage({ 
              id: 'form.district', 
              defaultMessage: 'İlçe' 
            })}
          </InputLabel>
          <Select
            value={districtValue}
            onChange={handleDistrictChange}
            label={intl.formatMessage({ 
              id: 'form.district', 
              defaultMessage: 'İlçe' 
            })}
            disabled={disabled || !cityValue || loading.districts}
            endAdornment={
              loading.districts ? (
                <Box sx={{ mr: 2 }}>
                  <CircularProgress size={20} />
                </Box>
              ) : null
            }
          >
            <MenuItem value="">
              <em>
                {intl.formatMessage({ 
                  id: 'form.selectDistrict', 
                  defaultMessage: 'İlçe Seçiniz' 
                })}
              </em>
            </MenuItem>
            {districts.map((district) => (
              <MenuItem key={district.id} value={district.name}>
                {district.name}
              </MenuItem>
            ))}
          </Select>
          {districtError && (
            <FormHelperText>{districtError}</FormHelperText>
          )}
        </FormControl>
      </Grid>
    </Grid>
  );
};

export default CityDistrictSelector; 