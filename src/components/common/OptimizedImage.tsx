import React, { useState, useEffect } from 'react';
import { Box, Skeleton, BoxProps } from '@mui/material';
import { useInView } from '../../hooks/useInView';

// Gö<PERSON><PERSON>ntü önbelleği
const imageCache: Record<string, string> = {};

interface OptimizedImageProps extends BoxProps {
  src: string;
  alt: string;
  width?: string | number;
  height?: string | number;
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  placeholderColor?: string;
  loadingHeight?: string | number;
  borderRadius?: string | number;
  onLoad?: () => void;
  onError?: () => void;
  priority?: boolean; // Yüksek öncelikli görüntüler için
}

/**
 * Optimize edilmiş görüntü bileşeni
 * - Lazy loading
 * - Görüntü önbelleğe alma
 * - Yükleme durumu gösterimi
 * - Hata yönetimi
 */
const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width = '100%',
  height = 'auto',
  objectFit = 'cover',
  placeholderColor = '#f0f0f0',
  loadingHeight = '200px',
  borderRadius = '0',
  onLoad,
  onError,
  priority = false,
  sx,
  ...props
}) => {
  const [loaded, setLoaded] = useState<boolean>(false);
  const [error, setError] = useState<boolean>(false);
  const [imgSrc, setImgSrc] = useState<string>('');
  
  // Görünüm alanı içinde olup olmadığını takip et
  const [ref, isInView] = useInView<HTMLDivElement>({
    rootMargin: '200px', // 200px önce yüklemeye başla
    threshold: 0.01
  });

  useEffect(() => {
    // Öncelikli görüntüler veya görünüm alanındaki görüntüler için yükleme işlemi başlat
    if (priority || isInView) {
      // Önbellekte varsa, oradan al
      if (imageCache[src]) {
        setImgSrc(imageCache[src]);
        setLoaded(true);
        onLoad?.();
        return;
      }

      // Önbellekte yoksa, yükle
      setImgSrc(src);
    }
  }, [src, isInView, priority, onLoad]);

  const handleLoad = () => {
    setLoaded(true);
    setError(false);
    // Görüntüyü önbelleğe al
    imageCache[src] = imgSrc;
    onLoad?.();
  };

  const handleError = () => {
    setError(true);
    setLoaded(true); // Yükleme durumunu sonlandır
    onError?.();
  };

  return (
    <Box
      ref={ref}
      sx={{
        position: 'relative',
        width,
        height: loaded ? height : loadingHeight,
        backgroundColor: placeholderColor,
        borderRadius,
        overflow: 'hidden',
        ...sx
      }}
      {...props}
    >
      {!loaded && !error && (
        <Skeleton
          variant="rectangular"
          width="100%"
          height="100%"
          animation="wave"
        />
      )}

      {imgSrc && (
        <img
          src={imgSrc}
          alt={alt}
          onLoad={handleLoad}
          onError={handleError}
          style={{
            display: loaded ? 'block' : 'none',
            width: '100%',
            height: '100%',
            objectFit,
            borderRadius,
          }}
        />
      )}

      {error && (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '100%',
            height: '100%',
            backgroundColor: '#f5f5f5',
            color: '#666',
            fontSize: '0.75rem',
            textAlign: 'center',
            padding: 2,
          }}
        >
          {alt || 'Görüntü yüklenemedi'}
        </Box>
      )}
    </Box>
  );
};

export default React.memo(OptimizedImage);
