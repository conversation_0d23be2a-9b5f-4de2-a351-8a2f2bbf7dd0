import React, { useState } from 'react';
import {
  Box,
  Typography,
  Tooltip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  useTheme
} from '@mui/material';
import { alpha } from '@mui/material/styles';
import CloseIcon from '@mui/icons-material/Close';
import { useIntl } from 'react-intl';

interface TruncatedTextProps {
  text: string;
  maxLength?: number;
  variant?: 'body1' | 'body2' | 'subtitle1' | 'subtitle2' | 'h6' | 'h5' | 'h4' | 'caption';
  component?: React.ElementType;
  sx?: any;
  expandable?: boolean;
  tooltip?: boolean;
  alwaysShowExpand?: boolean;
  useModal?: boolean;
  modalTitle?: string;
}

/**
 * TruncatedText component that truncates text if it exceeds maxLength
 * and provides options to expand/collapse or show tooltip with full text
 */
const TruncatedText: React.FC<TruncatedTextProps> = ({
  text,
  maxLength = 150,
  variant = 'body1',
  component = 'div',
  sx = {},
  expandable = true,
  tooltip = true,
  alwaysShowExpand = false,
  useModal = false,
  modalTitle = 'Detay',
}) => {
  const [expanded, setExpanded] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  const intl = useIntl();

  // If text is shorter than maxLength, just render it normally
  if (!text || text.length <= maxLength) {
    return (
      <Typography variant={variant} component={component} sx={sx}>
        {text}
      </Typography>
    );
  }

  const truncatedText = `${text.substring(0, maxLength)}...`;
  const shouldShowExpandButton = expandable && !useModal && (alwaysShowExpand || text.length > maxLength);

  const handleToggleExpand = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (useModal) {
      setModalOpen(true);
    } else {
      setExpanded(!expanded);
    }
  };

  // Metin tıklandığında da genişletilsin veya modal açılsın
  const handleTextClick = expandable ? (e: React.MouseEvent) => {
    e.stopPropagation();
    if (useModal) {
      setModalOpen(true);
    } else if (!expanded) {
      setExpanded(true);
    }
  } : undefined;

  const handleCloseModal = () => {
    setModalOpen(false);
  };

  const textContent = expanded ? text : truncatedText;

  const renderTextWithPotentialTooltip = () => {
    const textElement = (
      <Typography
        variant={variant}
        component={component}
        sx={{
          ...sx,
          cursor: expandable && text.length > maxLength && !expanded ? 'pointer' : 'default',
        }}
        onClick={text.length > maxLength && !expanded ? handleTextClick : undefined}
      >
        {textContent}
      </Typography>
    );

    if (tooltip && text.length > maxLength && !expanded) {
      return (
        <Tooltip title={text} arrow placement="top">
          <span> {/* Tooltip needs a DOM element if Typography is complex or for better positioning control */}
            {textElement}
          </span>
        </Tooltip>
      );
    }
    return textElement;
  };

  const content = (
    <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
      {renderTextWithPotentialTooltip()}

      {shouldShowExpandButton && !expanded && (
        <Tooltip title={text} arrow placement="top">
          <Typography
            className="read-more-text"
            variant="caption"
            onClick={handleToggleExpand}
            sx={{
              color: 'primary.main',
              fontWeight: 500,
              display: 'inline-block', // Changed from block to inline-block
              textAlign: 'right',
              cursor: 'pointer',
              mt: 0.5, // Added some margin top
              '&:hover': {
                textDecoration: 'underline',
              },
            }}
          >
            {intl.formatMessage({ id: 'common.readMore' })}
          </Typography>
        </Tooltip>
      )}

      {expanded && expandable && !useModal && (
        <Typography
          variant="caption"
          onClick={handleToggleExpand}
          sx={{
            color: 'primary.main',
            fontWeight: 500,
            display: 'inline-block',
            textAlign: 'right',
            cursor: 'pointer',
            mt: 0.5,
            '&:hover': {
              textDecoration: 'underline',
            },
          }}
        >
          {intl.formatMessage({ id: 'common.showLess' })}
        </Typography>
      )}

      {/* Modal for displaying full text (if useModal is true) */}
      {useModal && (
          <Box
            sx={{
              position: 'relative',
              cursor: 'pointer',
              '&:hover': {
                '& .read-more-text-modal': {
                  color: 'primary.main',
                  textDecoration: 'underline',
                }
              }
            }}
            onClick={() => setModalOpen(true)}
          >
            <Typography
              variant={variant}
              component={component}
              sx={{
                ...sx,
                mb: 0.5,
              }}
            >
              {truncatedText}
            </Typography>
            <Typography
              className="read-more-text-modal"
              variant="caption"
              sx={{
                color: 'primary.main',
                fontWeight: 500,
                display: 'block',
                textAlign: 'right',
              }}
            >
              {intl.formatMessage({ id: 'common.readMore' })}
            </Typography>
          </Box>
      )}
      <Dialog
        open={modalOpen}
        onClose={handleCloseModal}
        maxWidth="sm"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            margin: '16px',
            width: 'calc(100% - 32px)',
            maxHeight: 'calc(100% - 32px)',
            borderRadius: '16px',
            background: isDarkMode
              ? `linear-gradient(145deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.8)} 100%)`
              : '#fff',
            backdropFilter: 'blur(10px)',
          }
        }}
      >
        <DialogTitle sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: `1px solid ${isDarkMode ? alpha('#fff', 0.1) : alpha('#000', 0.05)}`,
          pb: 2,
          px: 3,
          pt: 2.5
        }}>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: isDarkMode ? theme.palette.primary.light : theme.palette.primary.main,
              fontSize: '1.1rem'
            }}
          >
            {modalTitle || 'Detay'}
          </Typography>
          <IconButton
            onClick={handleCloseModal}
            size="small"
            sx={{
              bgcolor: isDarkMode ? alpha('#fff', 0.05) : alpha('#000', 0.03),
              '&:hover': {
                bgcolor: isDarkMode ? alpha('#fff', 0.1) : alpha('#000', 0.05),
              }
            }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ py: 3 }}>
          {/* Metni satırlara bölerek göster */}
          {text.split(/(?<=\.|\?|!)\s+/)
.map((sentence, index) => (
            <Typography
              key={index}
              variant="body1"
              sx={{
                lineHeight: 1.8,
                mb: 1.5,
                pb: 1,
                borderBottom: index < text.split(/(?<=\.|\?|!)\s+/)
                .length - 1 ?
                  `1px solid ${isDarkMode ? alpha('#fff', 0.05) : alpha('#000', 0.05)}` :
                  'none'
              }}
            >
              {sentence}
            </Typography>
          ))}
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3, justifyContent: 'center' }}>
          <Button
            onClick={handleCloseModal}
            variant="contained"
            color="primary"
            sx={{
              borderRadius: 8,
              textTransform: 'none',
              px: 4,
              py: 1,
              fontWeight: 500,
              boxShadow: isDarkMode ? '0 4px 12px rgba(0,0,0,0.3)' : '0 4px 12px rgba(0,0,0,0.1)',
              '&:hover': {
                boxShadow: isDarkMode ? '0 6px 16px rgba(0,0,0,0.4)' : '0 6px 16px rgba(0,0,0,0.15)',
              }
            }}
            startIcon={<CloseIcon fontSize="small" />}
          >
            Kapat
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );

  // Eğer sayfa yolu /campaigns/ içeriyorsa ve bu bir kampanya koşulu ise tooltip'i devre dışı bırak
  const isCampaignDetailPage = typeof window !== 'undefined' &&
    window.location.pathname.includes('/campaigns/') &&
    modalTitle?.includes('Kampanya Koşulu');

  return content;
};

export default TruncatedText;
