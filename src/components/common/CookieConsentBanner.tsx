import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Slide,
  useTheme,
  alpha,
  Stack,
  Link,
} from '@mui/material';
import { Settings as SettingsIcon } from '@mui/icons-material';
import { useIntl } from 'react-intl';

import {
  shouldShowCookieBanner,
  acceptAllCookies,
  acceptOnlyNecessary,
  getOrCreateUniqueId,
} from '../../utils/cookieConsentUtils';
import CookieSettingsModal from './CookieSettingsModal';

interface CookieConsentBannerProps {
  onConsentGiven?: () => void;
}

const CookieConsentBanner: React.FC<CookieConsentBannerProps> = ({ onConsentGiven }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [pendingShowSettings, setPendingShowSettings] = useState(false);
  const [uniqueId, setUniqueId] = useState<string>('');
  const intl = useIntl();
  const theme = useTheme();

  useEffect(() => {
    // Component mount olduğunda banner'ın gösterilip gösterilmeyeceğini kontrol et
    const shouldShow = shouldShowCookieBanner();
    setIsVisible(shouldShow);
    
    // Unique ID'yi al
    setUniqueId(getOrCreateUniqueId());
  }, []);

  const handleAcceptAll = () => {
    acceptAllCookies();
    setIsVisible(false);
    onConsentGiven?.();
  };

  const handleRejectAll = () => {
    acceptOnlyNecessary();
    setIsVisible(false);
    onConsentGiven?.();
  };

  const handleCustomize = () => {
    setIsVisible(false);
    setPendingShowSettings(true);
  };

  const handleSettingsClose = () => {
    setShowSettings(false);
    setIsVisible(false);
    onConsentGiven?.();
  };

  const handleClose = () => {
    // Pazarlama şirketi için optimize edilmiş: X butonuna basınca da tüm çerezleri kabul et
    acceptAllCookies();
    setIsVisible(false);
    onConsentGiven?.();
  };

  return (
    <>
      <Slide
        direction="up"
        in={isVisible}
        mountOnEnter
        unmountOnExit
        onExited={() => {
          if (pendingShowSettings) {
            setShowSettings(true);
            setPendingShowSettings(false);
          }
        }}
        appear={false}
      >
        <Box
          sx={{
            position: 'fixed',
            bottom: 0,
            left: 0,
            right: 0,
            zIndex: 9999,
            p: { xs: 2, sm: 3 },
            pointerEvents: 'auto',
          }}
          onClick={e => e.stopPropagation()}
        >
          <Paper
            elevation={8}
            sx={{
              p: { xs: 3, sm: 4 },
              borderRadius: { xs: 2, sm: 3 },
              background: `linear-gradient(145deg, ${alpha('#ffffff', 0.95)} 0%, ${alpha('#f8f9fa', 0.95)} 100%)`,
              backdropFilter: 'blur(20px)',
              border: `1px solid ${alpha('#000', 0.08)}`,
              boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
              maxWidth: '1200px',
              mx: 'auto',
              position: 'relative',
            }}
            onClick={e => e.stopPropagation()}
          >
            <Stack spacing={3}>
              {/* Başlık ve açıklama */}
              <Box>
                <Typography
                  variant="h6"
                  component="h2"
                  gutterBottom
                  sx={{
                    fontWeight: 600,
                    color: 'grey.900',
                    pr: 4, // Kapatma butonu için alan bırak
                  }}
                >
                  {intl.formatMessage({ id: 'cookie.banner.title' })}
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    color: 'grey.700',
                    lineHeight: 1.6,
                  }}
                >
                  {intl.formatMessage({ id: 'cookie.banner.description' })}
                </Typography>
                
                {/* Cookie Policy ve Privacy Policy linkleri */}
                <Stack direction="row" spacing={2} sx={{ mt: 1 }}>
                  <Link
                    href="/cookie-policy"
                    color="primary"
                    underline="hover"
                    sx={{ fontSize: '0.875rem' }}
                  >
                    {intl.formatMessage({ id: 'cookie.policy.link' })}
                  </Link>
                  <Link
                    href="/privacy-policy"
                    color="primary"
                    underline="hover"
                    sx={{ fontSize: '0.875rem' }}
                  >
                    {intl.formatMessage({ id: 'cookie.privacy.link' })}
                  </Link>
                </Stack>
              </Box>

              {/* Butonlar */}
              <Stack
                direction={{ xs: 'column', sm: 'row' }}
                spacing={2}
                sx={{
                  alignItems: { xs: 'stretch', sm: 'center' },
                  justifyContent: { sm: 'flex-end' },
                }}
              >
                <Button
                  variant="outlined"
                  startIcon={<SettingsIcon />}
                  onClick={handleCustomize}
                  sx={{
                    borderColor: theme.palette.primary.main,
                    color: theme.palette.primary.main,
                    '&:hover': {
                      borderColor: theme.palette.primary.dark,
                      backgroundColor: alpha(theme.palette.primary.main, 0.05),
                    },
                    minWidth: { xs: 'auto', sm: '140px' },
                  }}
                >
                  {intl.formatMessage({ id: 'cookie.banner.customize' })}
                </Button>

                <Button
                  variant="contained"
                  onClick={handleAcceptAll}
                  sx={{
                    backgroundColor: theme.palette.primary.main,
                    color: theme.palette.primary.contrastText,
                    '&:hover': {
                      backgroundColor: theme.palette.primary.dark,
                    },
                    minWidth: { xs: 'auto', sm: '140px' },
                    fontWeight: 600,
                  }}
                >
                  {intl.formatMessage({ id: 'cookie.banner.acceptAll' })}
                </Button>
              </Stack>
            </Stack>
          </Paper>
        </Box>
      </Slide>

      {/* Cookie Ayarları Modal */}
      <CookieSettingsModal
        open={showSettings}
        onClose={handleSettingsClose}
      />
    </>
  );
};

export default CookieConsentBanner;
