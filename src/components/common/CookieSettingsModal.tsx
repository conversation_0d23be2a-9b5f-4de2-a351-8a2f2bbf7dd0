import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Button,
  Switch,
  FormControlLabel,
  Box,
  Divider,
  Stack,
  useTheme,
  alpha,
} from '@mui/material';
import { useIntl } from 'react-intl';

import {
  CookiePreferences,
  getCurrentPreferences,
  setCookieConsent,
  CUSTOMIZE_DEFAULT_PREFERENCES,
} from '../../utils/cookieConsentUtils';

interface CookieSettingsModalProps {
  open: boolean;
  onClose: () => void;
}

const CookieSettingsModal: React.FC<CookieSettingsModalProps> = ({ open, onClose }) => {
  const [preferences, setPreferences] = useState<CookiePreferences>({
    necessary: true,
    analytics: false,
    marketing: false,
    preferences: false,
  });

  const intl = useIntl();
  const theme = useTheme();

  useEffect(() => {
    if (open) {
      // Modal açıldığında varsayılan olarak tüm seçenekleri açık getir
      // Eğer kullanıcı daha önce tercih yapmışsa onları yükle, yoksa tümünü açık getir
      const currentPrefs = getCurrentPreferences();
      const hasUserMadeChoice = localStorage.getItem('cookieConsent') !== null;

      if (hasUserMadeChoice) {
        setPreferences(currentPrefs);
      } else {
        setPreferences(CUSTOMIZE_DEFAULT_PREFERENCES);
      }
    }
  }, [open]);

  const handlePreferenceChange = (category: keyof CookiePreferences) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (category === 'necessary') {
      // Gerekli çerezler her zaman aktif olmalı
      return;
    }

    setPreferences(prev => ({
      ...prev,
      [category]: event.target.checked,
    }));
  };

  const handleSave = () => {
    setCookieConsent(preferences);
    onClose();
  };

  const handleAcceptAll = () => {
    const allAccepted: CookiePreferences = {
      necessary: true,
      analytics: true,
      marketing: true,
      preferences: true,
    };
    setPreferences(allAccepted);
    setCookieConsent(allAccepted);
    onClose();
  };

  const handleRejectAll = () => {
    const onlyNecessary: CookiePreferences = {
      necessary: true,
      analytics: false,
      marketing: false,
      preferences: false,
    };
    setPreferences(onlyNecessary);
    setCookieConsent(onlyNecessary);
    onClose();
  };

  const cookieCategories = [
    {
      key: 'necessary' as keyof CookiePreferences,
      titleKey: 'cookie.necessary.title',
      descriptionKey: 'cookie.necessary.description',
      disabled: true,
    },
    {
      key: 'analytics' as keyof CookiePreferences,
      titleKey: 'cookie.analytics.title',
      descriptionKey: 'cookie.analytics.description',
      disabled: false,
    },
    {
      key: 'marketing' as keyof CookiePreferences,
      titleKey: 'cookie.marketing.title',
      descriptionKey: 'cookie.marketing.description',
      disabled: false,
    },
    {
      key: 'preferences' as keyof CookiePreferences,
      titleKey: 'cookie.preferences.title',
      descriptionKey: 'cookie.preferences.description',
      disabled: false,
    },
  ];

  return (
    <Dialog
      open={open}
      onClose={() => {}}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          background: `linear-gradient(145deg, ${alpha('#ffffff', 0.95)} 0%, ${alpha('#f8f9fa', 0.95)} 100%)`,
          backdropFilter: 'blur(20px)',
          border: `1px solid ${alpha('#000', 0.08)}`,
        },
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          pb: 2,
          color: 'grey.900',
        }}
      >
        <Typography variant="h6" component="h2" sx={{ fontWeight: 600 }}>
          {intl.formatMessage({ id: 'cookie.settings.title' })}
        </Typography>
      </DialogTitle>

      <DialogContent sx={{ pb: 3 }}>
        <Typography
          variant="body2"
          sx={{
            mb: 3,
            color: 'grey.700',
            lineHeight: 1.6,
          }}
        >
          {intl.formatMessage({ id: 'cookie.settings.description' })}
        </Typography>

        <Stack spacing={3}>
          {cookieCategories.map((category, index) => (
            <Box key={category.key}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'flex-start',
                  mb: 1,
                }}
              >
                <Box sx={{ flex: 1, mr: 2 }}>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontWeight: 600,
                      color: 'grey.900',
                      mb: 0.5,
                    }}
                  >
                    {intl.formatMessage({ id: category.titleKey })}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      color: 'grey.600',
                      lineHeight: 1.5,
                    }}
                  >
                    {intl.formatMessage({ id: category.descriptionKey })}
                  </Typography>
                </Box>
                <FormControlLabel
                  control={
                    <Switch
                      checked={preferences[category.key]}
                      onChange={handlePreferenceChange(category.key)}
                      disabled={category.disabled}
                      color="primary"
                    />
                  }
                  label=""
                  sx={{ m: 0 }}
                />
              </Box>
              {index < cookieCategories.length - 1 && (
                <Divider sx={{ mt: 2, borderColor: 'grey.200' }} />
              )}
            </Box>
          ))}
        </Stack>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ width: '100%' }}>
          <Button
            variant="outlined"
            onClick={handleRejectAll}
            sx={{
              borderColor: 'grey.400',
              color: 'grey.700',
              '&:hover': {
                borderColor: 'grey.500',
                backgroundColor: alpha('#000', 0.02),
              },
              flex: { xs: 1, sm: 'none' },
            }}
          >
            {intl.formatMessage({ id: 'cookie.banner.onlyNecessary' })}
          </Button>

          <Button
            variant="outlined"
            onClick={handleAcceptAll}
            sx={{
              borderColor: theme.palette.primary.main,
              color: theme.palette.primary.main,
              '&:hover': {
                borderColor: theme.palette.primary.dark,
                backgroundColor: alpha(theme.palette.primary.main, 0.05),
              },
              flex: { xs: 1, sm: 'none' },
            }}
          >
            {intl.formatMessage({ id: 'cookie.banner.acceptAll' })}
          </Button>

          <Button
            variant="contained"
            onClick={handleSave}
            sx={{
              backgroundColor: theme.palette.primary.main,
              color: theme.palette.primary.contrastText,
              '&:hover': {
                backgroundColor: theme.palette.primary.dark,
              },
              flex: { xs: 1, sm: 'none' },
              fontWeight: 600,
            }}
          >
            {intl.formatMessage({ id: 'cookie.settings.save' })}
          </Button>
        </Stack>
      </DialogActions>
    </Dialog>
  );
};

export default CookieSettingsModal;
