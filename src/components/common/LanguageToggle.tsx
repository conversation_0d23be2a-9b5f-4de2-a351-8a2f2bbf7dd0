import React, { useState } from "react";
import {
  Box,
  Button,
  Menu,
  MenuItem,
  alpha,
  useTheme,
  useMediaQuery,
  Typography,
} from "@mui/material";
import { ExpandMore, Language } from "@mui/icons-material";
import { useLanguage } from "../../i18n/LanguageContext";

const LanguageToggle: React.FC = () => {
  const { locale, setLocale } = useLanguage();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const languages = {
    "tr-TR": {
      label: "Türkçe",
      short: "TR",
      flag: "🇹🇷",
    },
    "en-US": {
      label: "English",
      short: "EN",
      flag: "🇺🇸",
    },
  };

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLanguageSelect = (newLocale: "tr-TR" | "en-US") => {
    setLocale(newLocale);
    handleClose();
  };

  const currentLanguage = languages[locale];

  return (
    <Box>
      <Button
        onClick={handleClick}
        endIcon={
          <ExpandMore
            sx={{
              transform: open ? "rotate(180deg)" : "rotate(0deg)",
              transition: "transform 0.2s ease",
              fontSize: isMobile ? "1rem" : "1.2rem",
            }}
          />
        }
        startIcon={<Language sx={{ fontSize: isMobile ? "1rem" : "1.1rem" }} />}
        sx={{
          minWidth: isMobile ? "50px" : "80px",
          height: isMobile ? "36px" : "44px",
          px: isMobile ? 1 : 2.5,
          py: isMobile ? 0.5 : 1,
          borderRadius: isMobile ? "18px" : "22px",
          textTransform: "none",
          fontWeight: isMobile ? 600 : 700,
          fontSize: isMobile ? "0.75rem" : "0.9rem",
          color: theme.palette.text.primary,
          border: `1px solid ${alpha(
            theme.palette.primary.main,
            isMobile ? 0.2 : 0.3
          )}`,
          backgroundColor: alpha(
            theme.palette.background.paper,
            isMobile ? 0.95 : 0.9
          ),
          backdropFilter: "blur(10px)",
          boxShadow: isMobile
            ? `0 2px 8px ${alpha(theme.palette.common.black, 0.08)}`
            : `0 4px 16px ${alpha(theme.palette.common.black, 0.1)}`,
          "&:hover": {
            backgroundColor: alpha(theme.palette.primary.main, 0.08),
            borderColor: alpha(theme.palette.primary.main, 0.5),
            transform: isMobile ? "none" : "translateY(-2px)",
            boxShadow: isMobile
              ? `0 3px 12px ${alpha(theme.palette.primary.main, 0.15)}`
              : `0 6px 20px ${alpha(theme.palette.primary.main, 0.2)}`,
          },
          "&:active": {
            transform: "translateY(0px)",
          },
          transition: "all 0.2s ease-in-out",
        }}
      >
        {currentLanguage.short}
      </Button>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        sx={{
          mt: 0.5,
          "& .MuiPaper-root": {
            borderRadius: "14px",
            minWidth: "130px",
            maxWidth: "150px",
            boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.12)}`,
            border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
            backgroundColor: alpha(theme.palette.background.paper, 0.95),
            backdropFilter: "blur(20px)",
            overflow: "hidden",
          },
        }}
      >
        {Object.entries(languages).map(([langCode, lang]) => (
          <MenuItem
            key={langCode}
            onClick={() => handleLanguageSelect(langCode as "tr-TR" | "en-US")}
            selected={locale === langCode}
            sx={{
              py: 1.2,
              px: 2.5,
              minHeight: "46px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              "&.Mui-selected": {
                backgroundColor: alpha(theme.palette.primary.main, 0.12),
                color: theme.palette.primary.main,
                fontWeight: 700,
                "&:hover": {
                  backgroundColor: alpha(theme.palette.primary.main, 0.18),
                },
              },
              "&:hover": {
                backgroundColor: alpha(theme.palette.primary.main, 0.06),
              },
              transition: "all 0.2s ease",
            }}
          >
            <Typography
              variant="body1"
              sx={{
                fontSize: "0.9rem",
                fontWeight: locale === langCode ? 700 : 500,
                lineHeight: 1.2,
                textAlign: "center",
                width: "100%",
              }}
            >
              {lang.label}
            </Typography>
          </MenuItem>
        ))}
      </Menu>
    </Box>
  );
};

export default LanguageToggle;
