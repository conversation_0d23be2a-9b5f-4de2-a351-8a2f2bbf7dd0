import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@mui/material";
import { FileDownload as FileDownloadIcon } from "@mui/icons-material";
import { exportToExcel } from "../../utils/excelExport";
import { maskUsersArray, conditionalMask } from "../../utils/dataMaskingUtils";

// User data type
interface User {
  id: number;
  name: string;
  surname: string;
  email: string;
  username: string;
  role: string;
  phoneNumber?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  [key: string]: any;
}

interface UserExcelExportProps {
  data: User[];
  filename?: string;
  disabled?: boolean;
}

/**
 * Excel export button component specifically for User data
 */
const UserExcelExport: React.FC<UserExcelExportProps> = ({
  data,
  filename = `Kullanıcılar_${new Date().toISOString().split("T")[0]}`,
  disabled = false,
}) => {
  const handleExport = () => {
    // Define column mapping for user data
    const columnMapping = {
      id: "ID",
      name: "<PERSON>",
      surname: "<PERSON><PERSON><PERSON>",
      email: "E-posta",
      username: "<PERSON><PERSON><PERSON><PERSON>",
      role: "Rol",
      phoneNumber: "Telefon",
      isActive: "Aktif",
      createdAt: "Oluşturulma Tarihi",
      updatedAt: "Güncellenme Tarihi",
    };

    // Mask user data before export
    const maskedData = conditionalMask(data, maskUsersArray);

    exportToExcel(maskedData, filename, "Kullanıcılar", columnMapping);
  };

  return (
    <Tooltip title="Excel olarak indir">
      <span>
        <Button
          variant="contained"
          color="primary"
          onClick={handleExport}
          disabled={disabled || !data.length}
          startIcon={<FileDownloadIcon />}
        >
          Excel
        </Button>
      </span>
    </Tooltip>
  );
};

export default UserExcelExport;
