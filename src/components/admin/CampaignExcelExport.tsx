import React from 'react';
import { But<PERSON>, Tooltip } from '@mui/material';
import { FileDownload as FileDownloadIcon } from '@mui/icons-material';
import { exportToExcel } from '../../utils/excelExport';

// Campaign data type
interface Campaign {
  id: number;
  name: string;
  title?: string | null;
  description?: string | null;
  category?: {
    id: number;
    name: string;
  };
  details?: any;
  isActive: boolean;
  startDate?: string | null;
  endDate?: string | null;
  createdAt: string;
  updatedAt: string;
  [key: string]: any;
}

interface CampaignExcelExportProps {
  data: Campaign[];
  filename?: string;
  disabled?: boolean;
}

/**
 * Excel export button component specifically for Campaign data
 */
const CampaignExcelExport: React.FC<CampaignExcelExportProps> = ({
  data,
  filename = `Kampanyalar_${new Date().toISOString().split('T')[0]}`,
  disabled = false,
}) => {
  const handleExport = () => {
    // Define column mapping for campaign data
    const columnMapping = {
      'id': 'ID',
      'name': '<PERSON><PERSON><PERSON><PERSON>',
      'title': '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      'description': 'Açıklama',
      'category.name': 'Kategori',
      'isActive': 'Aktif',
      'startDate': 'Başlangıç Tarihi',
      'endDate': 'Bitiş Tarihi',
      'createdAt': 'Oluşturulma Tarihi',
      'updatedAt': 'Güncellenme Tarihi'
    };
    
    exportToExcel(data, filename, 'Kampanyalar', columnMapping);
  };

  return (
    <Tooltip title="Excel olarak indir">
      <span>
        <Button
          variant="contained"
          color="primary"
          onClick={handleExport}
          disabled={disabled || !data.length}
          startIcon={<FileDownloadIcon />}
        >
          Excel
        </Button>
      </span>
    </Tooltip>
  );
};

export default CampaignExcelExport; 