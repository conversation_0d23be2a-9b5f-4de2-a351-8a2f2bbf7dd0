import React from "react";
import { <PERSON><PERSON>, Toolt<PERSON> } from "@mui/material";
import { FileDownload as FileDownloadIcon } from "@mui/icons-material";
import {
  exportToExcel,
  createColumnMappingFromHeaders,
} from "../../utils/excelExport";
import {
  conditionalMask,
  maskUsersArray,
  maskCustomersArray,
  maskFormsArray,
} from "../../utils/dataMaskingUtils";

interface ExportButtonProps<T> {
  data: T[];
  filename: string;
  headCells: Array<{ id: keyof T | string; label: string }>;
  disabled?: boolean;
  variant?: "text" | "outlined" | "contained";
  color?:
    | "inherit"
    | "primary"
    | "secondary"
    | "success"
    | "error"
    | "info"
    | "warning";
  sx?: React.CSSProperties;
  tooltip?: string;
}

function ExportButton<T extends Record<string, any>>({
  data,
  filename,
  headCells,
  disabled = false,
  variant = "contained",
  color = "primary",
  sx,
  tooltip = "Excel olarak indir",
}: ExportButtonProps<T>) {
  const handleExport = () => {
    const columnMapping = createColumnMappingFromHeaders(headCells);

    // Determine data type and apply appropriate masking
    let maskedData = data;

    // Check if data contains user-like properties
    if (data.length > 0 && data[0]) {
      const firstItem = data[0];

      // Check for user data (has username, role, etc.)
      if ("username" in firstItem && "role" in firstItem) {
        maskedData = conditionalMask(data, maskUsersArray);
      }
      // Check for customer data (has remindMe, country, city, etc.)
      else if (
        "remindMe" in firstItem ||
        ("country" in firstItem && "city" in firstItem)
      ) {
        maskedData = conditionalMask(data, maskCustomersArray);
      }
      // Check for form data (has basic personal info but no user-specific fields)
      else if (
        "name" in firstItem &&
        "surname" in firstItem &&
        "email" in firstItem &&
        !("username" in firstItem)
      ) {
        maskedData = conditionalMask(data, maskFormsArray);
      }
    }

    exportToExcel(maskedData, filename, "Sayfa1", columnMapping);
  };

  const button = (
    <Button
      variant={variant}
      color={color}
      onClick={handleExport}
      disabled={disabled || !data.length}
      startIcon={<FileDownloadIcon />}
      sx={{ ...sx }}
    >
      Excel
    </Button>
  );

  if (tooltip) {
    return (
      <Tooltip title={tooltip}>
        <span>{button}</span>
      </Tooltip>
    );
  }

  return button;
}

export default ExportButton;
