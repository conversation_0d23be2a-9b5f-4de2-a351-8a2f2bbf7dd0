import React from "react";
import { Button } from "@mui/material";
import { FileDownload as DownloadIcon } from "@mui/icons-material";
import { exportToExcel } from "../../utils/excelExport";
import {
  maskCustomerData,
  conditionalMask,
} from "../../utils/dataMaskingUtils";

interface Customer {
  id: number;
  name: string;
  surname: string;
  email: string;
  phoneNumber?: string;
  country?: string;
  city?: string;
  town?: string;
  gender?: string;
  birthday?: string | null; // string, null veya undefined kabul et
  username?: string;
  isActive: boolean; // isActive özelliğini ekledik
  job?: {
    id: number;
    description: string;
  };
}

interface Campaign {
  id: number;
  name: string;
  description?: string;
  title?: string;
  title2?: string;
  description2?: string;
  title3?: string;
  description3?: string;
  isActive: boolean; // isActive özelliğini ekledik
  category?: {
    id: number;
    name: string;
    isActive?: boolean;
    parentCategoryId: number;
  };
}

export interface CustomerCampaign {
  id: number;
  customer: Customer;
  campaign: Campaign;
  isCalled: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt?: string | null; // string, null veya undefined kabul et
  contactEmail?: string; // Kampanyaya özel iletişim e-postası
  contactPhone?: string; // Kampanyaya özel iletişim telefonu
}

interface CustomerCampaignExcelExportProps {
  data: CustomerCampaign[];
  disabled?: boolean;
  includeDetailedInfo?: boolean;
  filename?: string;
}

const CustomerCampaignExcelExport: React.FC<
  CustomerCampaignExcelExportProps
> = ({
  data,
  disabled = false,
  includeDetailedInfo = false,
  filename = "musteri-kampanya-iliskileri",
}) => {
  const handleExport = () => {
    if (!data || data.length === 0) {
      console.warn("Dışa aktarılacak veri yok!");
      return;
    }

    // Column mapping nesnesini oluştur
    const basicColumnMapping: Record<string, string> = {
      ID: "ID",
      Müşteri: "Müşteri",
      "Müşteri E-posta": "Müşteri E-posta",
      "Müşteri Telefon": "Müşteri Telefon",
      Kampanya: "Kampanya",
      "Kampanya E-posta": "Kampanya E-posta",
      "Kampanya Telefon": "Kampanya Telefon",
      Arandı: "Arandı",
      Aktif: "Aktif",
      "Oluşturulma Tarihi": "Oluşturulma Tarihi",
    };

    const detailedColumnMapping: Record<string, string> = {
      ...basicColumnMapping,
      Kategori: "Kategori",
      Şehir: "Şehir",
      İlçe: "İlçe",
      Cinsiyet: "Cinsiyet",
      "Doğum Tarihi": "Doğum Tarihi",
      "Kullanıcı Adı": "Kullanıcı Adı",
      Meslek: "Meslek",
      "Güncellenme Tarihi": "Güncellenme Tarihi",
    };

    const excelData = data.map((cc) => {
      const maskedCustomer = conditionalMask(cc.customer, maskCustomerData);
      const basicRow = {
        ID: cc.id,
        Müşteri: `${maskedCustomer.name} ${maskedCustomer.surname}`,
        "Müşteri E-posta": maskedCustomer.email,
        "Müşteri Telefon": maskedCustomer.phoneNumber || "",
        Kampanya: cc.campaign.name,
        "Kampanya E-posta": cc.contactEmail || "",
        "Kampanya Telefon": cc.contactPhone || "",
        Arandı: cc.isCalled ? "Evet" : "Hayır",
        Aktif: cc.isActive ? "Evet" : "Hayır",
        "Oluşturulma Tarihi": new Date(cc.createdAt).toLocaleString("tr-TR"),
      };

      if (!includeDetailedInfo) {
        return basicRow;
      }

      const detailedRow = {
        ...basicRow,
        Kategori: cc.campaign.category?.name || "",
        Şehir: maskedCustomer.city || "",
        İlçe: maskedCustomer.town || "",
        Cinsiyet: maskedCustomer.gender || "",
        "Doğum Tarihi": maskedCustomer.birthday
          ? new Date(maskedCustomer.birthday).toLocaleDateString("tr-TR")
          : "",
        "Kullanıcı Adı": maskedCustomer.username || "",
        Meslek: maskedCustomer.job?.description || "",
        "Güncellenme Tarihi": cc.updatedAt
          ? new Date(cc.updatedAt).toLocaleString("tr-TR")
          : "",
      };

      return detailedRow;
    });

    exportToExcel(
      excelData,
      filename,
      "Müşteri-Kampanya İlişkileri",
      includeDetailedInfo ? detailedColumnMapping : basicColumnMapping
    );
  };

  return (
    <Button
      variant="contained"
      color="primary"
      startIcon={<DownloadIcon />}
      onClick={handleExport}
      disabled={disabled || data.length === 0}
      size="small"
    >
      Excel
    </Button>
  );
};

export default CustomerCampaignExcelExport;
