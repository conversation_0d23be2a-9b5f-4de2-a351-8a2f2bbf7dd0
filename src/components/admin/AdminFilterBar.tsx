import React from 'react';
import {
  Box,
  TextField,
  Button,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Checkbox,
  FormControlLabel,
  SelectChangeEvent,
  Collapse,
  Paper,
} from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon
} from '@mui/icons-material';

export interface AdminFilterData {
  searchText: string;
  isActive?: 'all' | 'active' | 'inactive';
  dateRange?: {
    startDate: string | null;
    endDate: string | null;
    startTime: string | null;
    endTime: string | null;
  };
  searchIn?: Record<string, boolean>;
  [key: string]: any; // Allow for additional custom filter fields
}

export interface SelectOption {
  value: string;
  label: string;
}

interface AdminFilterBarProps {
  filterData: AdminFilterData;
  setFilterData: (data: AdminFilterData) => void;
  onFilter?: () => void;
  filteredCount?: number;
  totalCount?: number;
  showActiveFilter?: boolean;
  showDateFilter?: boolean;
  showSearchInOptions?: boolean;
  searchInOptions?: { field: string; label: string }[];
  additionalFilters?: {
    field: string;
    label: string;
    type: 'select' | 'text' | 'number';
    options?: SelectOption[];
  }[];
  searchPlaceholder?: string;
  renderExportButton?: () => React.ReactNode;
}

const NewAdminFilterBar: React.FC<AdminFilterBarProps> = ({
  filterData,
  setFilterData,
  filteredCount,
  totalCount,
  showActiveFilter = true,
  showDateFilter = false,
  showSearchInOptions = false,
  searchInOptions = [],
  additionalFilters = [],
  searchPlaceholder = "Arama yapmak için yazın...",
  renderExportButton,
}) => {
  const handleChange = (newFilterData: Partial<AdminFilterData>) => {
    setFilterData({ ...filterData, ...newFilterData });
  };

  const handleDateInputChange = (
    type: 'start' | 'end',
    value: string
  ) => {
    const dateField = type === 'start' ? 'startDate' : 'endDate';
    const timeField = type === 'start' ? 'startTime' : 'endTime';

    if (value) {
      const [date, time] = value.split('T');
      handleChange({
        dateRange: {
          ...filterData.dateRange!,
          [dateField]: date,
          [timeField]: time === undefined ? null : time,
        },
      });
    } else {
      handleChange({
        dateRange: {
          ...filterData.dateRange!,
          [dateField]: null,
          [timeField]: null,
        },
      });
    }
  };

  const getDisplayValue = (type: 'start' | 'end'): string => {
    const date = type === 'start' ? filterData.dateRange?.startDate : filterData.dateRange?.endDate;
    const time = type === 'start' ? filterData.dateRange?.startTime : filterData.dateRange?.endTime;
    if (date) {
      return `${date}T${time || ''}`;
    }
    return '';
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    handleChange({ searchText: e.target.value });
  };

  const handleIsActiveChange = (e: SelectChangeEvent<string>) => {
    handleChange({ isActive: e.target.value as 'all' | 'active' | 'inactive' });
  };

  const handleSearchInChange = (field: string) => {
    handleChange({
      searchIn: {
        ...filterData.searchIn!,
        [field]: !filterData.searchIn![field],
      },
    });
  };

  const handleAdditionalFilterChange = (field: string, value: any) => {
    handleChange({ [field]: value });
  };

  const handleClearSearch = () => {
    const clearedData: AdminFilterData = {
      searchText: '',
      dateRange: { startDate: null, endDate: null, startTime: null, endTime: null },
    };

    if (showActiveFilter) {
      clearedData.isActive = 'all';
    }

    additionalFilters.forEach(filter => {
      const hasAllOption = filter.options?.some(opt => opt.value === 'all');
      if (filter.type === 'select') {
        clearedData[filter.field] = hasAllOption ? 'all' : '';
      } else {
        clearedData[filter.field] = '';
      }
    });
    
    setFilterData(clearedData);
  };

  const isFilterActive = () => {
    if (filterData.searchText) return true;
    if (filterData.isActive && filterData.isActive !== 'all') return true;
    if (filterData.dateRange && (filterData.dateRange.startDate || filterData.dateRange.endDate)) return true;
    if (additionalFilters.some(filter => {
      const value = filterData[filter.field];
      if (typeof value === 'string' && value) return true;
      if (typeof value === 'boolean' && value) return true;
      if (typeof value === 'number') return true;
      const hasAllOption = filter.options?.some(opt => opt.value === 'all');
      if (hasAllOption && value !== 'all') return true;
      return false;
    })) {
      return true;
    }
    return false;
  };

  return (
    <Paper sx={{ p: 2, mb: 3 }}>
      {/* Search toggle */}
      <Box 
        sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          mb: 2,
          cursor: 'pointer',
          '&:hover': {
            opacity: 0.8
          }
        }}
      >
        <Typography variant="subtitle1" fontWeight={600}>
          Arama ve Filtreleme
        </Typography>
      </Box>

      <Collapse in={true}>
        {/* Basic search always visible */}
        <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
          <TextField
            fullWidth
            label="Arama"
            placeholder={searchPlaceholder}
            value={filterData.searchText}
            onChange={handleSearchChange}
            InputProps={{
              endAdornment: filterData.searchText ? (
                <IconButton size="small" onClick={() => handleChange({ searchText: '' })}>
                  <ClearIcon />
                </IconButton>
              ) : <SearchIcon color="action" />
            }}
          />
          <Button
            variant="outlined"
            color="error"
            onClick={handleClearSearch}
            disabled={!isFilterActive()}
          >
            Temizle
          </Button>
          {renderExportButton && (
            <Box sx={{ ml: 1 }}>
              {renderExportButton()}
            </Box>
          )}
        </Box>

        {/* Advanced search options */}
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            {/* Active status filter */}
            {showActiveFilter && (
              <FormControl sx={{ minWidth: 200 }}>
                <InputLabel id="active-filter-label">Durum</InputLabel>
                <Select
                  labelId="active-filter-label"
                  value={filterData.isActive || 'all'}
                  onChange={handleIsActiveChange}
                  label="Durum"
                >
                  <MenuItem value="all">Tümü</MenuItem>
                  <MenuItem value="active">Aktif</MenuItem>
                  <MenuItem value="inactive">Pasif</MenuItem>
                </Select>
              </FormControl>
            )}

            {/* Additional filters */}
            {additionalFilters.map((filter) => {
              if (filter.type === 'select') {
                return (
                  <FormControl key={filter.field} sx={{ minWidth: 200 }}>
                    <InputLabel id={`${filter.field}-filter-label`}>{filter.label}</InputLabel>
                    <Select
                      labelId={`${filter.field}-filter-label`}
                      value={filterData[filter.field] || 'all'}
                      onChange={(e) => handleAdditionalFilterChange(filter.field, e.target.value)}
                      label={filter.label}
                    >
                      {
                        !filter.options?.some(o => o.value === 'all' || o.value === '') && 
                        <MenuItem value="all">
                          <em>Tümü</em>
                        </MenuItem>
                      }
                      {filter.options?.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                );
              }
              return (
                <TextField
                  key={filter.field}
                  sx={{ minWidth: 200 }}
                  label={filter.label}
                  type={filter.type}
                  value={filterData[filter.field] || ''}
                  onChange={(e) => handleAdditionalFilterChange(filter.field, e.target.value)}
                  InputLabelProps={{ shrink: true }}
                />
              );
            })}
          </Box>

          {/* Date range filters */}
          {showDateFilter && (
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <TextField
                sx={{ flex: '1 1 200px' }}
                label="Başlangıç Tarihi"
                type="datetime-local"
                value={getDisplayValue('start')}
                onChange={(e) => handleDateInputChange('start', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
              <TextField
                sx={{ flex: '1 1 200px' }}
                label="Bitiş Tarihi"
                type="datetime-local"
                value={getDisplayValue('end')}
                onChange={(e) => handleDateInputChange('end', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Box>
          )}

          {/* Search in options */}
          {showSearchInOptions && searchInOptions.length > 0 && (
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Arama Yeri:
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                {searchInOptions.map((option) => (
                  <FormControlLabel
                    key={option.field}
                    control={
                      <Checkbox
                        checked={filterData.searchIn?.[option.field] || false}
                        onChange={() => handleSearchInChange(option.field)}
                      />
                    }
                    label={option.label}
                  />
                ))}
              </Box>
            </Box>
          )}
        </Box>

        {/* Filter stats */}
        {filteredCount !== undefined && totalCount !== undefined && (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Toplam {totalCount} kayıt içinden {filteredCount} kayıt gösteriliyor
            {filterData.searchText ? ` (Arama: "${filterData.searchText}")` : ''}
          </Typography>
        )}
      </Collapse>
    </Paper>
  );
};

export default NewAdminFilterBar; 