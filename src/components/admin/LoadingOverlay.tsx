import React from 'react';
import {
  Box,
  CircularProgress,
  Typography,
  Backdrop,
  LinearProgress,
} from '@mui/material';

interface LoadingOverlayProps {
  open: boolean;
  message?: string;
  progress?: number;
  variant?: 'circular' | 'linear' | 'backdrop';
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  open,
  message = 'Yükleniyor...',
  progress,
  variant = 'circular'
}) => {
  if (!open) return null;

  if (variant === 'backdrop') {
    return (
      <Backdrop
        sx={{
          color: '#fff',
          zIndex: (theme) => theme.zIndex.drawer + 1,
          flexDirection: 'column',
          gap: 2,
        }}
        open={open}
      >
        <CircularProgress color="inherit" size={60} />
        <Typography variant="h6" color="inherit">
          {message}
        </Typography>
        {progress !== undefined && (
          <Box sx={{ width: 300, mt: 2 }}>
            <LinearProgress
              variant="determinate"
              value={progress}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: 'rgba(255, 255, 255, 0.3)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: '#fff',
                },
                '& .MuiLinearProgress-root': {
                  overflow: 'hidden',
                },
              }}
            />
            <Typography variant="body2" color="inherit" sx={{ mt: 1, textAlign: 'center' }}>
              %{Math.round(progress)}
            </Typography>
          </Box>
        )}
      </Backdrop>
    );
  }

  if (variant === 'linear') {
    return (
      <Box sx={{ width: '100%', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            {message}
          </Typography>
        </Box>
        <LinearProgress
          variant={progress !== undefined ? 'determinate' : 'indeterminate'}
          value={progress}
          sx={{ height: 6, borderRadius: 3 }}
        />
        {progress !== undefined && (
          <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
            %{Math.round(progress)}
          </Typography>
        )}
      </Box>
    );
  }

  // Default circular variant
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        p: 3,
        gap: 2,
      }}
    >
      <CircularProgress size={40} />
      <Typography variant="body2" color="text.secondary">
        {message}
      </Typography>
      {progress !== undefined && (
        <Typography variant="caption" color="text.secondary">
          %{Math.round(progress)}
        </Typography>
      )}
    </Box>
  );
};

export default LoadingOverlay;
