import React, { ReactNode, useState, useEffect } from "react";
import {
  Box,
  CssBaseline,
  AppBar,
  Toolbar,
  Typography,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  IconButton,
  useMediaQuery,
  useTheme,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@mui/material";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Category as CategoryIcon,
  Campaign as CampaignIcon,
  BrandingWatermark as BrandIcon,
  People as CustomerIcon,
  Description as FormIcon,
  Link as LinkIcon,
  Menu as MenuIcon,
  Work as WorkIcon,
  Image as ImageIcon,
  Settings as SettingsIcon,
  Dashboard as DashboardIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon,
  Logout as LogoutIcon,
  Home as HomeIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Email as EmailIcon,
  ContactPhone as ContactPhoneIcon,
  PersonOff as PersonOffIcon, // İkonu import et
} from "@mui/icons-material";
import { useAuth } from "../../contexts/AuthContext";
import QuickActions from "./QuickActions";

const drawerWidth = 240;

interface AdminLayoutProps {
  children: ReactNode;
}

interface MenuItem {
  text: string;
  icon: React.ReactNode;
  path?: string;
  subItems?: MenuItem[];
}

const menuItems: MenuItem[] = [
  {
    text: "Dashboard",
    icon: <DashboardIcon />,
    path: "/admin/dashboard",
  },
  {
    text: "Ana Yönetim",
    icon: <DashboardIcon />,
    subItems: [
      {
        text: "Kategoriler",
        icon: <CategoryIcon />,
        path: "/admin/categories",
      },
      { text: "Meslekler", icon: <WorkIcon />, path: "/admin/jobs" },
      { text: "Kullanıcılar", icon: <CustomerIcon />, path: "/admin/users" },
    ],
  },
  {
    text: "CRM Yönetimi",
    icon: <ContactPhoneIcon />,
    subItems: [
      {
        text: "CRM Dashboard",
        icon: <DashboardIcon />,
        path: "/crm-admin/dashboard",
      },
      {
        text: "Ulaşılamayan Müşteriler",
        icon: <PersonOffIcon />,
        path: "/crm-admin/unreachable-users",
      },
      {
        text: "Contacts",
        icon: <CustomerIcon />,
        path: "/crm-admin/contacts",
      },
      {
        text: "Webhooks",
        icon: <ContactPhoneIcon />,
        path: "/crm-admin/webhooks",
      },
    ],
  },
  {
    text: "Kampanya Yönetimi",
    icon: <CampaignIcon />,
    subItems: [
      { text: "Kampanyalar", icon: <CampaignIcon />, path: "/admin/campaigns" },
      {
        text: "Kampanya Detayları",
        icon: <SettingsIcon />,
        path: "/admin/campaign-details",
      },
      {
        text: "Kampanya Görselleri",
        icon: <ImageIcon />,
        path: "/admin/campaign-images",
      },
      {
        text: "Kampanya URL'leri",
        icon: <LinkIcon />,
        path: "/admin/campaign-urls",
      },
    ],
  },
  {
    text: "Marka Yönetimi",
    icon: <BrandIcon />,
    subItems: [
      { text: "Markalar", icon: <BrandIcon />, path: "/admin/brands" },
      {
        text: "Marka-Kategori İlişkileri",
        icon: <CategoryIcon />,
        path: "/admin/brand-categories",
      },
      {
        text: "Marka-Kampanya İlişkileri",
        icon: <CampaignIcon />,
        path: "/admin/brand-campaigns",
      },
    ],
  },
  {
    text: "Müşteri Yönetimi",
    icon: <CustomerIcon />,
    subItems: [
      { text: "Müşteriler", icon: <CustomerIcon />, path: "/admin/customers" },
      {
        text: "Abone Olan Kullanıcılar",
        icon: <EmailIcon />,
        path: "/admin/subscribed-users",
      },
      {
        text: "Müşteri-Kampanya İlişkileri",
        icon: <CampaignIcon />,
        path: "/admin/customer-campaigns",
      },
      {
        text: "Müşteri-Kampanya Yönetimi",
        icon: <CampaignIcon />,
        path: "/admin/customer-campaigns-management",
      },
    ],
  },
  {
    text: "Form Yönetimi",
    icon: <FormIcon />,
    subItems: [
      {
        text: "Kampanya Form Yönetimi",
        icon: <FormIcon />,
        path: "/admin/campaign-forms-management",
      },
      { text: "Formlar (Eski)", icon: <FormIcon />, path: "/admin/forms" },
      {
        text: "Form-Kampanya İlişkileri (Eski)",
        icon: <CampaignIcon />,
        path: "/admin/form-campaigns",
      },
    ],
  },
];

export default function AdminLayout({ children }: AdminLayoutProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const [mobileOpen, setMobileOpen] = useState(false);
  const [isDrawerOpen, setIsDrawerOpen] = useState(true);
  const { logout } = useAuth();

  const [expandedItems, setExpandedItems] = useState<{
    [key: string]: boolean;
  }>(() => {
    const savedState = localStorage.getItem("expandedMenuItems");
    return savedState ? JSON.parse(savedState) : {};
  });

  useEffect(() => {
    localStorage.setItem("expandedMenuItems", JSON.stringify(expandedItems));
  }, [expandedItems]);

  const handleDrawerToggle = () => {
    if (isMobile) {
      setMobileOpen(!mobileOpen);
    } else {
      setIsDrawerOpen(!isDrawerOpen);
    }
  };

  const handleItemClick = (item: MenuItem) => {
    if (item.path) {
      navigate(item.path);
      if (isMobile) setMobileOpen(false);
    } else if (item.subItems) {
      setExpandedItems((prev) => ({
        ...prev,
        [item.text]: !prev[item.text],
      }));
    }
  };

  const isCurrentPath = (path?: string) => path && location.pathname === path;

  const renderMenuItem = (item: MenuItem, level: number = 0) => {
    const hasSubItems = item.subItems && item.subItems.length > 0;
    const isExpanded = expandedItems[item.text];
    const isActive = item.path ? isCurrentPath(item.path) : false;

    return (
      <React.Fragment key={`${item.text}-${level}-${item.path || 'no-path'}-${Math.random()}`}>
        <ListItem
          onClick={() => handleItemClick(item)}
          sx={{
            cursor: "pointer",
            pl: level * 2,
            bgcolor: isActive ? "action.selected" : "transparent",
            "&:hover": {
              bgcolor: "action.hover",
            },
          }}
        >
          <ListItemIcon sx={{ minWidth: "40px", ml: 1 }}>
            {item.icon}
          </ListItemIcon>
          <ListItemText primary={item.text} />
          {hasSubItems &&
            (isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />)}
        </ListItem>
        {hasSubItems && (
          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {item.subItems?.map((subItem, index) =>
                renderMenuItem(subItem, level + 1)
              )}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };

  const handleLogout = () => {
    logout();
    navigate("/");
  };

  const handleGoHome = () => {
    navigate("/");
  };

  const drawer = (
    <>
      <Toolbar />
      <Box sx={{ overflow: "auto" }}>
        <List>{menuItems.map((item) => renderMenuItem(item))}</List>
        <Divider />
        <List>
          <ListItem
            sx={{
              cursor: "pointer",
              "&:hover": {
                bgcolor: "action.hover",
              },
            }}
            onClick={handleGoHome}
          >
            <ListItemIcon>
              <HomeIcon />
            </ListItemIcon>
            <ListItemText primary="Ana Sayfaya Dön" />
          </ListItem>
        </List>
      </Box>
    </>
  );

  return (
    <Box sx={{ display: "flex" }}>
      <CssBaseline />
      <AppBar
        position="fixed"
        sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}
      >
        <Toolbar
          sx={{
            display: "flex",
            justifyContent: "space-between",
            minHeight: { xs: "56px", sm: "64px" },
            px: { xs: 1, sm: 2 },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <IconButton
              color="inherit"
              aria-label="toggle drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{
                mr: { xs: 1, sm: 2 },
                p: { xs: 1, sm: 1.5 },
              }}
            >
              {isMobile ? (
                <MenuIcon />
              ) : isDrawerOpen ? (
                <ChevronLeftIcon />
              ) : (
                <ChevronRightIcon />
              )}
            </IconButton>
            <Typography
              variant="h6"
              noWrap
              component="div"
              sx={{
                fontSize: { xs: "1rem", sm: "1.25rem" },
                fontWeight: 600,
              }}
            >
              360Avantajlı Admin Panel
            </Typography>
          </Box>

          <Box
            sx={{
              display: "flex",
              gap: { xs: 0.5, sm: 1, md: 2 },
              alignItems: "center",
            }}
          >
            {/* Desktop Tools */}
            <Box sx={{ display: { xs: "none", lg: "flex" }, gap: 1 }}>
              <Button
                color="inherit"
                size="small"
                onClick={() =>
                  window.open(
                    "http://188.40.52.26:3000",
                    "_blank",
                    "noopener,noreferrer"
                  )
                }
                sx={{ fontSize: "0.8rem" }}
              >
                Grafana
              </Button>
              <Button
                color="inherit"
                size="small"
                onClick={() =>
                  window.open(
                    "http://188.40.52.26:9000",
                    "_blank",
                    "noopener,noreferrer"
                  )
                }
                sx={{ fontSize: "0.8rem" }}
              >
                Graylog
              </Button>
              <Button
                color="inherit"
                size="small"
                onClick={() =>
                  window.open(
                    "http://188.40.52.26:9090",
                    "_blank",
                    "noopener,noreferrer"
                  )
                }
                sx={{ fontSize: "0.8rem" }}
              >
                Prometheus
              </Button>
            </Box>

            {/* Essential buttons for all screen sizes */}
            <Button
              color="inherit"
              onClick={handleGoHome}
              startIcon={<HomeIcon fontSize="small" />}
              size="small"
              sx={{
                fontSize: { xs: "0.75rem", sm: "0.875rem" },
                px: { xs: 1, sm: 2 },
                "& .MuiButton-startIcon": {
                  marginRight: { xs: 0.5, sm: 1 },
                },
              }}
            >
              <Box
                component="span"
                sx={{ display: { xs: "none", sm: "inline" } }}
              >
                Ana Sayfaya Dön
              </Box>
            </Button>
            <Button
              color="inherit"
              onClick={handleLogout}
              startIcon={<LogoutIcon fontSize="small" />}
              size="small"
              sx={{
                fontSize: { xs: "0.75rem", sm: "0.875rem" },
                px: { xs: 1, sm: 2 },
                "& .MuiButton-startIcon": {
                  marginRight: { xs: 0.5, sm: 1 },
                },
              }}
            >
              <Box
                component="span"
                sx={{ display: { xs: "none", sm: "inline" } }}
              >
                Çıkış Yap
              </Box>
            </Button>
          </Box>
        </Toolbar>
      </AppBar>

      {isMobile ? (
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            "& .MuiDrawer-paper": {
              boxSizing: "border-box",
              width: drawerWidth,
              position: "fixed",
              top: 0,
              left: 16,
            },
          }}
        >
          {drawer}
        </Drawer>
      ) : (
        <Drawer
          variant="permanent"
          sx={{
            width: isDrawerOpen ? drawerWidth : 0,
            flexShrink: 0,
            position: "fixed",
            top: 0,
            left: 16,
            "& .MuiDrawer-paper": {
              width: isDrawerOpen ? drawerWidth : 0,
              boxSizing: "border-box",
              position: "fixed",
              top: 0,
              left: 16,
              transition: theme.transitions.create("width", {
                easing: theme.transitions.easing.sharp,
                duration: theme.transitions.duration.enteringScreen,
              }),
              overflowX: "hidden",
              borderRight: isDrawerOpen
                ? "1px solid rgba(0, 0, 0, 0.12)"
                : "none",
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      )}

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: { xs: 1, sm: 1.5, md: 2, lg: 3 },
          width: {
            xs: "100%",
            sm: `calc(100% - ${isDrawerOpen ? drawerWidth : 0}px - 16px)`,
          },
          ml: {
            xs: 0,
            sm: `${isDrawerOpen ? drawerWidth + 16 : 16}px`,
          },
          mt: { xs: 0, sm: 0 },
          pt: { xs: 0.5, sm: 1, md: 1.5, lg: 2 },
          overflow: "auto",
          minHeight: "100vh",
          transition: theme.transitions.create(["margin", "width"], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
        }}
      >
        <Toolbar sx={{ minHeight: { xs: "56px", sm: "64px" } }} />
        <Box
          sx={{
            maxWidth: "100%",
            overflow: "hidden",
          }}
        >
          {children}
        </Box>

        {/* Quick Actions Speed Dial */}
        <QuickActions onRefresh={() => window.location.reload()} />
      </Box>
    </Box>
  );
}
