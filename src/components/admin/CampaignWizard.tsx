import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Typography,
  Box,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  IconButton,
  CircularProgress,
  Alert,
  FormControlLabel,
  Switch,
  Chip,
  Card,
  CardContent,
  Avatar,
  Checkbox,
  Paper,
} from '@mui/material';
import {
  Add as AddIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Category as CategoryIcon,
  Settings as SettingsIcon,
  CloudUpload as UploadIcon,
  Image as ImageIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import axios from 'axios';
import LoadingOverlay from './LoadingOverlay';
import { useCategories } from '../../contexts/CategoryContext';
import { useAuth } from '../../contexts/AuthContext';
import { SelectChangeEvent } from '@mui/material/Select';

interface CampaignWizardProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  editCampaign?: any;
}
interface CampaignFormData {
  name: string;
  title: string | null;
  description: string | null;
  title2: string | null;
  description2: string | null;
  title3: string | null;
  description3: string | null;
  categoryId: string;
  details: any;
  isActive: boolean;
  startDate: string | null;
  endDate: string | null;
  campaignUrl: string;
  createNewBrand: boolean;
  brandId: string;
  newBrandData: {
    name: string;
    countryCode: string;
    brandUrl: string;
    logo?: File;
  };
  createNewTemplate: boolean;
  selectedTemplateId: string;
  newTemplateData: {
    name: string;
    details: Record<string, {
      label: string;
      type: string;
      required: boolean;
      value: string | boolean;
      options?: string[];
    }>;
  };
  createNewCategory: boolean;
  newCategoryData: {
    name: string;
    parentCategoryId: number | null;
    campaignDetailId: number | null;
    isActive: boolean;
  };
  detailImages: File[];
  detailImagePreviews: string[];
  showcaseImage?: File;
  showcaseImagePreview?: string;
}

const CampaignWizard: React.FC<CampaignWizardProps> = ({
  open,
  onClose,
  onSuccess,
  editCampaign
}) => {
  const { token } = useAuth();
  const { refreshCategories } = useCategories();
  const [activeStep, setActiveStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  // Data states
  const [categories, setCategories] = useState<any[]>([]);
  const [brands, setBrands] = useState<any[]>([]);
  const [campaignDetails, setCampaignDetails] = useState<any>(null);
  const [templates, setTemplates] = useState<any[]>([]);

  // Template creation states
  const [newFieldName, setNewFieldName] = useState('');
  const [newFieldType, setNewFieldType] = useState('text');

  // Form data
  const [formData, setFormData] = useState<CampaignFormData>({
    name: '',
    title: '',
    description: '',
    title2: '',
    description2: '',
    title3: '',
    description3: '',
    categoryId: '',
    details: {},
    isActive: true,
    startDate: '',
    endDate: '',
    campaignUrl: '',
    createNewBrand: false,
    brandId: '',
    newBrandData: {
      name: '',
      countryCode: '',
      brandUrl: '',
    },
    createNewTemplate: false,
    selectedTemplateId: '',
    newTemplateData: {
      name: '',
      details: {},
    },
    createNewCategory: false,
    newCategoryData: {
      name: '',
      parentCategoryId: null,
      campaignDetailId: null,
      isActive: true,
    },
    detailImages: [],
    detailImagePreviews: [],
    showcaseImage: undefined,
    showcaseImagePreview: undefined,
  });

  // Quick actions states - Removed as quick category/brand creation is now part of main wizard steps
  // const [showQuickCategory, setShowQuickCategory] = useState(false);
  // const [showQuickBrand, setShowQuickBrand] = useState(false);
  // const [newCategoryName, setNewCategoryName] = useState('');
  // const [selectedParentCategory, setSelectedParentCategory] = useState<string>('0');

  // File input refs for better control
  const showcaseImageInputRef = React.useRef<HTMLInputElement>(null);
  const detailImagesInputRef = React.useRef<HTMLInputElement>(null);

  // 1. State ekle
  const [categoryFormCompleted, setCategoryFormCompleted] = useState(false);

  useEffect(() => {
    if (open) {
      fetchInitialData();
      if (editCampaign) {
        populateEditData();
      } else {
        resetForm();
      }
    }
  }, [open, editCampaign]);

  useEffect(() => {
  }, [templates]);

  useEffect(() => {
    if (activeStep === 1) {
      setCategoryFormCompleted(false);
    }
  }, [activeStep]);

  const resetForm = () => {
    setFormData({
      name: '',
      title: '',
      description: '',
      title2: '',
      description2: '',
      title3: '',
      description3: '',
      categoryId: '',
      details: {},
      isActive: true,
      startDate: '',
      endDate: '',
      campaignUrl: '',
      createNewBrand: false,
      brandId: '',
      newBrandData: {
        name: '',
        countryCode: '',
        brandUrl: '',
      },
      createNewTemplate: false,
      selectedTemplateId: '',
      newTemplateData: {
        name: '',
        details: {},
      },
      createNewCategory: false,
      newCategoryData: {
        name: '',
        parentCategoryId: null,
        campaignDetailId: null,
        isActive: true,
      },
      detailImages: [],
      detailImagePreviews: [],
      showcaseImage: undefined,
      showcaseImagePreview: undefined,
    });
    setActiveStep(0);
    setErrors([]);
    setCampaignDetails(null);

    // Reset file inputs
    if (showcaseImageInputRef.current) {
      showcaseImageInputRef.current.value = '';
    }
    if (detailImagesInputRef.current) {
      detailImagesInputRef.current.value = '';
    }
  };

  const fetchInitialData = async () => {
    try {
      setIsLoading(true);
      const [categoriesRes, brandsRes, templatesRes] = await Promise.all([
        axios.get('https://360avantajli.com/api/Campaign_Service/category', { headers: { Authorization: `Bearer ${token}` } }),
        axios.get('https://360avantajli.com/api/Campaign_Service/brand', { headers: { Authorization: `Bearer ${token}` } }),
        axios.get('https://360avantajli.com/api/Campaign_Service/campaign-detail', { headers: { Authorization: `Bearer ${token}` } })
      ]);

      const activeCategories = categoriesRes.data.filter((cat: any) => cat.isActive);
      const activeBrands = brandsRes.data.filter((brand: any) => brand.isActive);
      const activeTemplates = templatesRes.data.filter((template: any) => template.isActive);


      setCategories(activeCategories);
      setBrands(activeBrands);
      setTemplates(activeTemplates);
    } catch (error) {
      setErrors(['Veriler yüklenirken hata oluştu']);
    } finally {
      setIsLoading(false);
    }
  };

  const populateEditData = () => {
    if (editCampaign) {
      setFormData({
        ...formData,
        name: editCampaign.name || '',
        title: editCampaign.title || '',
        description: editCampaign.description || '',
        title2: editCampaign.title2 || '',
        description2: editCampaign.description2 || '',
        title3: editCampaign.title3 || '',
        description3: editCampaign.description3 || '',
        details: editCampaign.details || {},
        isActive: editCampaign.isActive ?? true,
        startDate: editCampaign.startDate || '',
        endDate: editCampaign.endDate || '',
      });
    }
  };

  // Quick Category Creation - Removed
  // const handleQuickCategoryCreate = () => {
  //   if (!newCategoryName.trim()) {
  //     setErrors(['Kategori adı boş olamaz']);
  //     return;
  //   }

  //   // Kategori bilgilerini formData'ya ekle
  //   setFormData(prev => ({
  //     ...prev,
  //     createNewCategory: true,
  //     newCategoryData: {
  //       name: newCategoryName.trim(),
  //       parentCategoryId: selectedParentCategory === '0' ? 0 : parseInt(selectedParentCategory),
  //       campaignDetailId: null,
  //       isActive: true
  //     }
  //   }));

  //   // Formu kapat
  //   setShowQuickCategory(false);
  //   setNewCategoryName('');
  //   setSelectedParentCategory('0');
  // };

  // Quick Brand Creation - Removed as quick brand creation is now part of handleSubmit
  // const handleQuickBrandCreate = async () => {
  //   const { name, countryCode, brandUrl, logo } = formData.newBrandData;

  //   if (!name.trim() || !countryCode.trim() || !brandUrl.trim()) {
  //     setErrors(['Marka adı, ülke kodu ve URL alanları zorunludur']);
  //     return;
  //   }

  //   try {
  //     setIsLoading(true);
  //     setErrors([]); // Clear previous errors

  //     const formDataToSend = new FormData();
  //     formDataToSend.append('name', name.trim());
  //     formDataToSend.append('countryCode', countryCode.trim());
  //     formDataToSend.append('brandUrl', brandUrl.trim());
  //     formDataToSend.append('isActive', 'true');

  //     if (logo) {
  //       // Validate file size
  //       if (logo.size > 5 * 1024 * 1024) {
  //         setErrors(['Logo dosyası 5MB\'dan büyük olamaz']);
  //         return;
  //       }
  //       formDataToSend.append('image', logo);
  //     }

  //     const response = await axios.post(
  //       'https://360avantajli.com/api/Campaign_Service/brand',
  //       formDataToSend,
  //       {
  //         headers: { 'Content-Type': 'multipart/form-data' },
  //         timeout: 30000 // 30 second timeout
  //       }
  //     );

  //     if (response.data && response.data.id) {
  //       // Add new brand to local state instead of full refresh
  //       const newBrand = {
  //         id: response.data.id,
  //         name: name.trim(),
  //         countryCode: countryCode.trim(),
  //         brandUrl: brandUrl.trim(),
  //         isActive: true,
  //         imagePath: logo ? 'uploaded' : null
  //       };

  //       setBrands(prev => [...prev, newBrand]);

  //       // Auto-select the new brand
  //       setFormData(prev => ({
  //         ...prev,
  //         brandId: response.data.id.toString(),
  //         createNewBrand: false,
  //         newBrandData: { name: '', countryCode: '', brandUrl: '' }
  //       }));

  //       // Quick brand creation form is now handled by createNewBrand switch
  //       // setShowQuickBrand(false);

  //       // Create brand-category relationship if category is already selected
  //       if (formData.categoryId) {
  //         try {
  //             brandId: response.data.id,
  //             categoryId: formData.categoryId
  //           });

  //           // Check if relationship already exists
  //           const existingRelationships = await axios.get('https://360avantajli.com/api/Campaign_Service/brand-to-category');
  //           const existingRelationship = existingRelationships.data?.find((rel: any) =>
  //             rel.brand?.id === parseInt(response.data.id) &&
  //             rel.category?.id === parseInt(formData.categoryId) &&
  //             rel.isActive
  //           );

  //           if (!existingRelationship) {
  //             await axios.post('https://360avantajli.com/api/Campaign_Service/brand-to-category', {
  //               categoryId: parseInt(formData.categoryId),
  //               brandId: parseInt(response.data.id),
  //               isActive: true
  //             }, { timeout: 10000 });

  //           } else {
  //           }
  //         } catch (error: any) {
  //           console.warn('Brand selection relationship error:', error);
  //           // Don\'t fail the process for this
  //         }
  //       }
  //     }

  //   } catch (error: any) {
  //     const errorMessage = error.response?.data?.message || 'Marka oluşturulurken hata oluştu';
  //     setErrors([errorMessage]);
  //   } finally {
  //     setIsLoading(false);
  //   }
  // };

  // Fetch campaign details template (optional)
  const fetchCampaignDetails = async (categoryId: string) => {
    if (!categoryId) return;

    try {
      // Don't set global loading for this operation
      const response = await axios.get(
        `https://360avantajli.com/api/Campaign_Service/campaign-detail/category/${categoryId}`,
        { 
          timeout: 10000, // 10 second timeout
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      setCampaignDetails(response.data);

      // Initialize details form data
      if (response.data?.details && typeof response.data.details === 'object') {
        const initialDetails: any = {};
        Object.entries(response.data.details).forEach(([key, field]: [string, any]) => {
          if (field && typeof field === 'object') {
            initialDetails[key] = {
              ...field,
              value: field.value || (field.type === 'checkbox' ? false : '')
            };
          }
        });
        setFormData(prev => ({ ...prev, details: initialDetails }));
      } else {
        // No details template found for this category
        setFormData(prev => ({ ...prev, details: {} }));
        setCampaignDetails(null);
      }
    } catch (error: any) {
      // Campaign details are optional - don't show errors for 404
      if (error.response?.status === 404) {
      
      } else {
        console.warn('Kategori detayları yüklenirken hata oluştu:', error.message);
      }

      // Always set empty details and null campaign details for any error
      setFormData(prev => ({ ...prev, details: {} }));
      setCampaignDetails(null);
    }
  };

  // Handle step navigation
  const handleNext = () => {

    // Eğer yeni kategori oluşturulmuş ve form tamamlanmışsa, adımı ilerlet
    if (activeStep === 1 && formData.createNewCategory && categoryFormCompleted) {
      setActiveStep(prev => prev + 1);
      return;
    }

    if (validateCurrentStep()) {
      // Eğer kategori adımındaysak ve yeni kategori oluşturuluyorsa
      if (activeStep === 1 && formData.createNewCategory) {
        setCategoryFormCompleted(true); // Formu tamamlandı olarak işaretle
        setFormData(prev => ({
          ...prev,
          categoryId: 'new' // Geçici bir ID, gerçek ID kampanya oluşturulurken atanacak
        }));
        // NOT: setActiveStep burada çağrılmıyor, kullanıcı bilgi mesajını gördükten sonra tekrar Devam Et'e basmalı
        return;
      }
      setActiveStep(prev => prev + 1);
    } 
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const validateStep = (stepIndex: number): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    switch (stepIndex) {
      case 0: // Campaign Detail Template
        if (!formData.createNewTemplate && !formData.selectedTemplateId) {
          errors.push('Lütfen bir şablon seçin veya yeni şablon oluşturun');
        }
        if (formData.createNewTemplate) {
          if (Object.keys(formData.newTemplateData.details).length === 0) {
            errors.push('En az bir detay alanı eklemelisiniz');
          }
        }
        break;
      case 1: // Category
        // Yeni kategori oluşturulmuyorsa ve kategori seçilmemişse hata ver
        if (!formData.createNewCategory && !formData.categoryId) {
          errors.push('Kategori seçimi zorunludur');
        }
        // Yeni kategori oluşturuluyorsa ve kategori adı boşsa hata ver
        if (formData.createNewCategory && !formData.newCategoryData.name.trim()) {
          errors.push('Yeni kategori adı zorunludur');
        }
        break;
      case 2: // Basic Info & Template Details
        if (!formData.name.trim()) {
          errors.push('Kampanya adı zorunludur');
        }
        // Şablon detayları validasyonu
        if (formData.details && Object.keys(formData.details).length > 0) {
          Object.entries(formData.details).forEach(([key, field]: [string, any]) => {
            if (field.required && (!field.value || field.value === '')) {
              errors.push(`${field.label} zorunludur`);
            }
          });
        }
        break;
      case 3: // Brand
        if (!formData.brandId && !formData.createNewBrand) {
          errors.push('Marka seçimi veya yeni marka oluşturma zorunludur');
        }
        if (formData.createNewBrand) {
          const { name, countryCode, brandUrl } = formData.newBrandData;
          if (!name.trim() || !countryCode.trim() || !brandUrl.trim()) {
            errors.push('Yeni marka için tüm alanlar zorunludur');
          }
        }
        break;
      case 4: // Details & Images
        // Görsel validasyonları burada yapılabilir
        break;
      case 5: // Summary
        // Tüm adımların validasyonunu kontrol et
        for (let i = 0; i < 5; i++) {
          const stepValidation = validateStep(i);
          if (!stepValidation.isValid) {
            errors.push(...stepValidation.errors);
          }
        }
        break;
    }

    return { isValid: errors.length === 0, errors };
  };

  const validateCurrentStep = (): boolean => {
    const validation = validateStep(activeStep);
    setErrors(validation.errors);
    return validation.isValid;
  };

  // Handle showcase image upload with improved error handling
  const handleShowcaseImageUpload = async (file: File) => {
    if (!file) return;

    try {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setErrors(['Lütfen sadece resim dosyası seçin']);
        return;
      }

      // Validate file size
      if (file.size > 5 * 1024 * 1024) {
        setErrors(['Dosya boyutu 5MB\'dan büyük olamaz']);
        return;
      }

      // Show loading state for showcase image
      setFormData(prev => ({
        ...prev,
        showcaseImage: file,
        showcaseImagePreview: 'loading'
      }));

      // Create preview using Promise-based FileReader
      const preview = await readFileAsDataURL(file);

      setFormData(prev => ({
        ...prev,
        showcaseImage: file,
        showcaseImagePreview: preview
      }));

      setErrors([]); // Clear errors on successful upload
    } catch (error) {
      setErrors(['Vitrin görseli yüklenirken hata oluştu']);
      // Reset showcase image on error
      setFormData(prev => ({
        ...prev,
        showcaseImage: undefined,
        showcaseImagePreview: undefined
      }));
    }
  };

  // Promise-based FileReader helper
  const readFileAsDataURL = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = () => reject(new Error('Dosya okuma hatası'));
      reader.readAsDataURL(file);
    });
  };

  // Handle detail images upload with improved synchronization
  const handleDetailImagesUpload = async (files: File[]) => {
    if (!files || files.length === 0) return;

    try {
      // Validate all files first
      const validationResults = files.map(file => ({
        file,
        isValid: file.type.startsWith('image/') && file.size <= 5 * 1024 * 1024,
        error: !file.type.startsWith('image/')
          ? `${file.name} geçerli bir resim dosyası değil`
          : file.size > 5 * 1024 * 1024
          ? `${file.name} dosyası 5MB'dan büyük`
          : null
      }));

      const validFiles = validationResults.filter(result => result.isValid).map(result => result.file);
      const errors = validationResults.filter(result => !result.isValid).map(result => result.error!);

      if (errors.length > 0) {
        setErrors(errors);
        if (validFiles.length === 0) return;
      }

      // Show loading state for detail images
      setFormData(prev => ({
        ...prev,
        detailImages: [...prev.detailImages, ...validFiles],
        detailImagePreviews: [...prev.detailImagePreviews, ...validFiles.map(() => 'loading')]
      }));

      // Process all files in parallel with proper error handling
      const previewPromises = validFiles.map(async (file, index) => {
        try {
          const preview = await readFileAsDataURL(file);
          return { index, preview, success: true };
        } catch (error) {
          return { index, preview: null, success: false, fileName: file.name };
        }
      });

      const results = await Promise.allSettled(previewPromises);

      // Process results and update state synchronously
      const successfulPreviews: string[] = [];
      const successfulFiles: File[] = [];
      const failedFiles: string[] = [];

      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          successfulPreviews.push(result.value.preview!);
          successfulFiles.push(validFiles[index]);
        } else {
          const fileName = result.status === 'fulfilled' ? result.value.fileName : validFiles[index].name;
          failedFiles.push(fileName || `Dosya ${index + 1}`);
        }
      });

      // Update state with successful uploads only
      setFormData(prev => ({
        ...prev,
        detailImages: [...prev.detailImages.slice(0, -validFiles.length), ...successfulFiles],
        detailImagePreviews: [...prev.detailImagePreviews.slice(0, -validFiles.length), ...successfulPreviews]
      }));

      // Show errors for failed files
      if (failedFiles.length > 0) {
        setErrors([...errors, ...failedFiles.map(fileName => `${fileName} yüklenemedi`)]);
      } else if (errors.length === 0) {
        setErrors([]); // Clear all errors if everything succeeded
      }

    } catch (error) {
      setErrors(['Detay görselleri yüklenirken hata oluştu']);
    }
  };

  // Remove detail image
  const removeDetailImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      detailImages: prev.detailImages.filter((_, i) => i !== index),
      detailImagePreviews: prev.detailImagePreviews.filter((_, i) => i !== index)
    }));
  };

  // Handle final submission
  const handleSubmit = async () => {
    try {
      setIsLoading(true);
      setErrors([]);

      // 1. Yeni kategori oluştur
      let categoryId = formData.categoryId;
      if (formData.createNewCategory) {
        try {
          // Önce campaign detail oluştur
          const campaignDetailData = {
            details: formData.createNewTemplate ? formData.newTemplateData.details : formData.details,
            isActive: true
          };

          const campaignDetailResponse = await axios.post(
            'https://360avantajli.com/api/Campaign_Service/campaign-detail',
            campaignDetailData,
            { headers: { Authorization: `Bearer ${token}` } }
          );

          const newCampaignDetailId = campaignDetailResponse.data?.id;
          if (!newCampaignDetailId) {
            throw new Error('Campaign detail oluşturulamadı');
          }


          // Sonra kategoriyi oluştur
          const categoryData = {
            name: formData.newCategoryData.name.trim(),
            parentCategoryId: formData.newCategoryData.parentCategoryId,
            campaignDetailId: newCampaignDetailId,
            isActive: true,
          };


          const categoryResponse = await axios.post(
            'https://360avantajli.com/api/Campaign_Service/category',
            categoryData,
            { headers: { Authorization: `Bearer ${token}` } }
          );

          if (categoryResponse.data && categoryResponse.data.id) {
            categoryId = categoryResponse.data.id.toString();

            // Kategori listesini güncelle (UI için)
            setCategories(prev => [...prev, categoryResponse.data]);

            // CategoryContext'i güncelle (Navbar vb. için)
            await refreshCategories();

            // Yeni oluşturulan kategoriyi formData'ya set et
            setFormData(prev => ({ ...prev, categoryId: categoryResponse.data.id.toString() }));

          } else {
            throw new Error('Kategori oluşturulamadı: Geçersiz API yanıtı');
          }
        } catch (error: any) {

          let errorMessage = 'Yeni kategori oluşturulurken hata oluştu';
          if (error.response?.data?.message) {
            errorMessage = error.response.data.message;
          } else if (error.response?.status === 400) {
            errorMessage = error.response.data?.errors ? Object.values(error.response.data.errors).join(', ') : 'Geçersiz kategori bilgileri.';
          } else if (error.message) {
            errorMessage = 'İstek sırasında hata: ' + error.message;
          }

          throw new Error('Kategori oluşturulurken hata oluştu: ' + errorMessage);
        }
      }

      // 2. Yeni marka oluştur
      let brandId = formData.brandId;
      if (formData.createNewBrand) {
        try {
          const brandFormData = new FormData();
          brandFormData.append('name', formData.newBrandData.name);
          brandFormData.append('countryCode', formData.newBrandData.countryCode);
          brandFormData.append('brandUrl', formData.newBrandData.brandUrl);
          brandFormData.append('isActive', 'true');
          if (formData.newBrandData.logo) {
            brandFormData.append('image', formData.newBrandData.logo);
          }

          const brandResponse = await axios.post(
            'https://360avantajli.com/api/Campaign_Service/brand',
            brandFormData,
            {
              headers: {
                'Content-Type': 'multipart/form-data',
                'Authorization': `Bearer ${token}`
              }
            }
          );
          brandId = brandResponse.data.id.toString();
        } catch (error: any) {
          throw new Error('Marka oluşturulurken hata oluştu: ' + (error.response?.data?.message || error.message));
        }
      }

      // 3. Yeni şablon oluştur
      let templateId = formData.selectedTemplateId;
      if (formData.createNewTemplate) {
        // Yeni şablon için details'i kullan
        templateId = 'new';
      }

      // 4. Kampanyayı oluştur
      try {
        // Details alanını doğru formata dönüştür
        let formattedDetails: Record<string, any> = {};
        
        if (formData.createNewTemplate) {
          // Yeni şablon oluşturuluyorsa
          Object.entries(formData.newTemplateData.details).forEach(([key, field]: [string, any]) => {
            formattedDetails[key] = {
              type: field.type,
              label: field.label,
              value: field.value !== undefined ? field.value : (field.type === 'checkbox' ? false : ''),
              options: field.options || [],
              required: field.required || false
            };
          });
        } else {
          // Mevcut şablon kullanılıyorsa
          Object.entries(formData.details).forEach(([key, field]: [string, any]) => {
            formattedDetails[key] = {
              type: field.type,
              label: field.label,
              value: field.value !== undefined ? field.value : (field.type === 'checkbox' ? false : ''),
              options: field.options || [],
              required: field.required || false
            };
          });
        }

        const campaignData = {
          name: formData.name.trim(),
          title: formData.title,
          description: formData.description,
          title2: formData.title2,
          description2: formData.description2,
          title3: formData.title3,
          description3: formData.description3,
          categoryId: parseInt(categoryId),
          details: formattedDetails,
          isActive: formData.isActive,
          startDate: formData.startDate,
          endDate: formData.endDate
        };

        const campaignResponse = await axios.post(
          'https://360avantajli.com/api/Campaign_Service/campaign',
          campaignData,
          { headers: { Authorization: `Bearer ${token}` } }
        );
        const campaignId = campaignResponse.data.id;

        // Kampanya URL'si ekleme
        if (!formData.campaignUrl) {
          throw new Error('Lütfen kampanya URL\'si giriniz');
        }

        // URL formatı kontrolü (basit)
        try {
          new URL(formData.campaignUrl);
        } catch {
          throw new Error('Geçerli bir URL giriniz! (örn: https://ornek.com)');
        }

        await axios.post('https://360avantajli.com/api/Campaign_Service/campaign-url', {
          campaignId: campaignId,
          url: formData.campaignUrl,
          isActive: true
        }, { headers: { Authorization: `Bearer ${token}` } });

        // 5. Marka-Kampanya ilişkisini kur
        if (brandId) {
          try {
            await axios.post('https://360avantajli.com/api/Campaign_Service/brand-to-campaign', {
              campaignId: parseInt(campaignId),
              brandId: parseInt(brandId),
              isActive: true
            }, { headers: { Authorization: `Bearer ${token}` } });
       
          } catch (error: any) {
            console.warn('Marka-kampanya ilişkisi kurulurken hata:', error);
          }
        }

        // 6. Marka-Kategori ilişkisini kur
        if (brandId && categoryId) {
          try {
            // Önce mevcut ilişkileri kontrol et
            const existingRelationships = await axios.get('https://360avantajli.com/api/Campaign_Service/brand-to-category', {
              headers: { Authorization: `Bearer ${token}` }
            });
            const existingRelationship = existingRelationships.data?.find((rel: any) =>
              rel.brand?.id === parseInt(brandId) &&
              rel.category?.id === parseInt(categoryId) &&
              rel.isActive
            );

            if (!existingRelationship) {
              await axios.post('https://360avantajli.com/api/Campaign_Service/brand-to-category', {
                categoryId: parseInt(categoryId),
                brandId: parseInt(brandId),
                isActive: true
              }, { headers: { Authorization: `Bearer ${token}` } });
            } else {
              console.log('Marka-kategori ilişkisi zaten mevcut');
            }
          } catch (error: any) {
            console.warn('Marka-kategori ilişkisi kurulurken hata:', error);
          }
        }

        // 7. Kampanya detaylarını güncelle
        try {
          const campaignDetailData = {
            details: formattedDetails,
            isActive: true
          };

          await axios.put(
            `https://360avantajli.com/api/Campaign_Service/campaign/${campaignId}/detail`,
            campaignDetailData,
            { headers: { Authorization: `Bearer ${token}` } }
          );
        } catch (error: any) {
          console.warn('Kampanya detayları güncellenirken hata:', error);
        }

        // 8. Görselleri yükle
        if (formData.showcaseImage) {
          try {
            const showcaseFormData = new FormData();
            showcaseFormData.append('image', formData.showcaseImage);
            await axios.post(
              `https://360avantajli.com/api/Campaign_Service/campaign-image/showcase?campaignId=${campaignId}`,
              showcaseFormData,
              {
                headers: {
                  'Content-Type': 'multipart/form-data',
                  'Authorization': `Bearer ${token}`
                }
              }
            );
          } catch (error: any) {
            console.warn('Vitrin görseli yüklenirken hata:', error);
          }
        }

        if (formData.detailImages.length > 0) {
          try {
            const detailFormData = new FormData();
            formData.detailImages.forEach((file) => {
              detailFormData.append('images', file);
            });
            await axios.post(
              `https://360avantajli.com/api/Campaign_Service/campaign-image/details?campaignId=${campaignId}`,
              detailFormData,
              {
                headers: {
                  'Content-Type': 'multipart/form-data',
                  'Authorization': `Bearer ${token}`
                }
              }
            );
          } catch (error: any) {
            console.warn('Detay görselleri yüklenirken hata:', error);
          }
        }

        onSuccess();
        onClose();
      } catch (error: any) {
        throw new Error('Kampanya oluşturulurken hata oluştu: ' + (error.response?.data?.message || error.message));
      }
    } catch (error: any) {
      setErrors([error.message || 'Kampanya kaydedilirken bir hata oluştu. Lütfen tekrar deneyin.']);
    } finally {
      setIsLoading(false);
    }
  };

  // Template field handlers
  const handleAddField = () => {
    if (!newFieldName || !newFieldType) return;

    const fieldKey = newFieldName.toLowerCase().replace(/\s+/g, '_');
    setFormData(prev => ({
      ...prev,
      newTemplateData: {
        ...prev.newTemplateData,
        name: prev.newTemplateData.name || 'Yeni Şablon',
        details: {
          ...prev.newTemplateData.details,
          [fieldKey]: {
            type: newFieldType,
            label: newFieldName,
            required: false,
            options: newFieldType === 'text' ? undefined : [],
            value: newFieldType === 'checkbox' ? false : ''
          }
        }
      }
    }));

    setNewFieldName('');
    setNewFieldType('text');
  };

  const handleFieldChange = (fieldKey: string, field: string, value: any) => {
    const currentField = formData.newTemplateData.details[fieldKey];
    
    // Eğer tip değişiyorsa, değeri sıfırla
    if (field === 'type') {
      setFormData(prev => ({
        ...prev,
        newTemplateData: {
          ...prev.newTemplateData,
          details: {
            ...prev.newTemplateData.details,
            [fieldKey]: {
              ...currentField,
              type: value,
              value: value === 'checkbox' ? false : '',
              options: value === 'text' ? undefined : currentField.options || []
            }
          }
        }
      }));
      return;
    }

    setFormData(prev => ({
      ...prev,
      newTemplateData: {
        ...prev.newTemplateData,
        details: {
          ...prev.newTemplateData.details,
          [fieldKey]: {
            ...currentField,
            [field]: value
          }
        }
      }
    }));
  };

  const handleAddOption = (fieldKey: string) => {
    const currentField = formData.newTemplateData.details[fieldKey];
    setFormData(prev => ({
      ...prev,
      newTemplateData: {
        ...prev.newTemplateData,
        details: {
          ...prev.newTemplateData.details,
          [fieldKey]: {
            ...currentField,
            options: [...(currentField.options || []), '']
          }
        }
      }
    }));
  };

  const handleRemoveOption = (fieldKey: string, index: number) => {
    const currentField = formData.newTemplateData.details[fieldKey];
    const newOptions = currentField.options?.filter((_, i) => i !== index) || [];
    setFormData(prev => ({
      ...prev,
      newTemplateData: {
        ...prev.newTemplateData,
        details: {
          ...prev.newTemplateData.details,
          [fieldKey]: {
            ...currentField,
            options: newOptions
          }
        }
      }
    }));
  };

  const handleOptionChange = (fieldKey: string, index: number, value: string) => {
    const currentField = formData.newTemplateData.details[fieldKey];
    const newOptions = [...(currentField.options || [])];
    newOptions[index] = value;
    setFormData(prev => ({
      ...prev,
      newTemplateData: {
        ...prev.newTemplateData,
        details: {
          ...prev.newTemplateData.details,
          [fieldKey]: {
            ...currentField,
            options: newOptions
          }
        }
      }
    }));
  };

  const handleRemoveField = (fieldKey: string) => {
    setFormData(prev => {
      const newDetails = { ...prev.newTemplateData.details };
      delete newDetails[fieldKey];
      return {
        ...prev,
        newTemplateData: {
          ...prev.newTemplateData,
          details: newDetails
        }
      };
    });
  };

  const handleCategoryChange = async (e: SelectChangeEvent<string>) => {
    const categoryId = e.target.value;
    setFormData(prev => ({
      ...prev,
      categoryId,
      // Yeni kategori oluşturma kapatıldığında yeni kategori verilerini temizle
      createNewCategory: false,
      newCategoryData: { name: '', parentCategoryId: null, campaignDetailId: null, isActive: true }
    }));

    if (categoryId) {
      try {
        // Kategori detaylarını yükle
        const response = await axios.get(
          `https://360avantajli.com/api/Campaign_Service/campaign-detail/category/${categoryId}`,
          { 
            timeout: 10000,
            headers: { Authorization: `Bearer ${token}` }
          }
        );

        setCampaignDetails(response.data);

        // Detay alanlarını başlat
        if (response.data?.details && typeof response.data.details === 'object') {
          const initialDetails: any = {};
          Object.entries(response.data.details).forEach(([key, field]: [string, any]) => {
            if (field && typeof field === 'object') {
              initialDetails[key] = {
                ...field,
                value: field.value || (field.type === 'checkbox' ? false : '')
              };
            }
          });
          setFormData(prev => ({ ...prev, details: initialDetails }));
        } else {
          setFormData(prev => ({ ...prev, details: {} }));
        }

        // Marka-kategori ilişkisi kur
        if (formData.brandId) {
          try {
            const existingRelationships = await axios.get('https://360avantajli.com/api/Campaign_Service/brand-to-category', {
              headers: { Authorization: `Bearer ${token}` }
            });
            const existingRelationship = existingRelationships.data?.find((rel: any) =>
              rel.brand?.id === parseInt(formData.brandId) &&
              rel.category?.id === parseInt(categoryId) &&
              rel.isActive
            );

            if (!existingRelationship) {
              await axios.post('https://360avantajli.com/api/Campaign_Service/brand-to-category', {
                categoryId: parseInt(categoryId),
                brandId: parseInt(formData.brandId),
                isActive: true
              }, { 
                timeout: 10000,
                headers: { Authorization: `Bearer ${token}` }
              });
            }
          } catch (error) {
            console.warn('Brand-category relationship error:', error);
          }
        }
      } catch (error: any) {
        console.warn('Category details fetch error:', error.message);
        setFormData(prev => ({ ...prev, details: {} }));
        setCampaignDetails(null);
      }
    }
  };

  // Şablon seçimi için handler'ı güncelle
  const handleTemplateChange = async (templateId: string) => {
    try {
      if (!templateId) {
        setFormData(prev => ({
          ...prev,
          selectedTemplateId: '',
          details: {}
        }));
        return;
      }

      // Şablonun detaylarını al
      const response = await axios.get(`https://360avantajli.com/api/Campaign_Service/campaign-detail/${templateId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      const template = response.data;

      // Şablonun bağlı olduğu kategoriyi bul
      const categoryResponse = await axios.get('https://360avantajli.com/api/Campaign_Service/category', {
        headers: { Authorization: `Bearer ${token}` }
      });
      const categories = categoryResponse.data;
      const categoryWithTemplate = categories.find((cat: any) => cat.campaignDetail?.id === parseInt(templateId));

      if (categoryWithTemplate) {
        // Form verilerini güncelle
        setFormData(prev => ({
          ...prev,
          selectedTemplateId: templateId,
          categoryId: categoryWithTemplate.id.toString(),
          details: template.details || {}
        }));

        // Kategori detaylarını yükle
        await fetchCampaignDetails(categoryWithTemplate.id.toString());
      } else {
        // Eğer kategori bulunamazsa sadece şablonu güncelle
        setFormData(prev => ({
          ...prev,
          selectedTemplateId: templateId,
          details: template.details || {}
        }));
      }

      // Detay alanlarını başlat
      if (template.details) {
        const initialDetails: any = {};
        Object.entries(template.details).forEach(([key, field]: [string, any]) => {
          initialDetails[key] = {
            ...field,
            value: field.value || (field.type === 'checkbox' ? false : '')
          };
        });
        setFormData(prev => ({ ...prev, details: initialDetails }));
      }
    } catch (error) {
      setErrors(['Şablon yüklenirken bir hata oluştu. Lütfen tekrar deneyin.']);
    }
  };

  const selectedCategory = categories.find(c => c.id.toString() === formData.categoryId);
  const selectedBrand = brands.find(b => b.id.toString() === formData.brandId);
  const selectedTemplate = templates.find(t => t.id.toString() === formData.selectedTemplateId);

  // Helper function to format dates for summary (YYYY-MM-DDTHH:mm to DD/MM/YYYY HH:mm)
  const formatDateForSummary = (dateString: string | null): string => {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return '-';
      }
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0'); // Month is 0-indexed
      const year = date.getFullYear();
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${day}/${month}/${year} ${hours}:${minutes}`;
    } catch (error) {
      console.error('Error formatting date:', error);
      return '-';
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { minHeight: '80vh' }
      }}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={2}>
          <CategoryIcon />
          <Typography variant="h6">
            {editCampaign ? 'Kampanya Düzenle' : 'Yeni Kampanya Oluştur'}
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        {errors.length > 0 && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {errors.map((error, index) => (
              <div key={index}>{error}</div>
            ))}
          </Alert>
        )}

        <Stepper activeStep={activeStep} orientation="vertical">
          {/* Step 1: Campaign Detail Template */}
          <Step>
            <StepLabel>Kampanya Detay Şablonu</StepLabel>
            <StepContent>
              <Box sx={{ mb: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.createNewTemplate}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        createNewTemplate: e.target.checked,
                        selectedTemplateId: e.target.checked ? '' : prev.selectedTemplateId,
                        details: {} // Şablon değiştiğinde detayları sıfırla
                      }))}
                    />
                  }
                  label="Yeni Şablon Oluştur"
                  sx={{ mb: 2 }}
                />

                {!formData.createNewTemplate ? (
                  <FormControl fullWidth>
                    <InputLabel>Şablon Seç *</InputLabel>
                    <Select
                      value={formData.selectedTemplateId}
                      label="Şablon Seç *"
                      onChange={(e) => handleTemplateChange(e.target.value)}
                    >
                      <MenuItem value="">
                        <em>Seçiniz</em>
                      </MenuItem>
                      {templates.map((template) => (
                        <MenuItem key={template.id} value={template.id.toString()}>
                          <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                            <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                              {template.name || `Şablon ${template.id}`}
                            </Typography>
                            {template.details && Object.entries(template.details).map(([key, field]: [string, any]) => (
                              <Typography key={key} variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                                • {field.label} ({field.type === 'text' ? 'Metin' : field.type === 'radio' ? 'Seçenek' : 'Onay Kutusu'})
                                {field.required && ' *'}
                              </Typography>
                            ))}
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                ) : (
                  <Card sx={{ p: 2, bgcolor: 'action.hover' }}>
                    <Typography variant="subtitle2" sx={{ mb: 2 }}>
                      Yeni Şablon Bilgileri
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" sx={{ mb: 1 }}>
                          Detay Alanları
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                          <TextField
                            size="small"
                            label="Alan Adı"
                            value={newFieldName}
                            onChange={(e) => setNewFieldName(e.target.value)}
                          />
                          <FormControl size="small" sx={{ minWidth: 120 }}>
                            <InputLabel>Tip</InputLabel>
                            <Select
                              value={newFieldType}
                              label="Tip"
                              onChange={(e) => setNewFieldType(e.target.value)}
                            >
                              <MenuItem value="text">Metin</MenuItem>
                              <MenuItem value="radio">Seçenek</MenuItem>
                              <MenuItem value="checkbox">Onay Kutusu</MenuItem>
                            </Select>
                          </FormControl>
                          <Button
                            variant="contained"
                            size="small"
                            onClick={handleAddField}
                            disabled={!newFieldName || !newFieldType}
                          >
                            Ekle
                          </Button>
                        </Box>
                        {Object.entries(formData.newTemplateData.details).map(([key, field]: [string, any]) => (
                          <Box key={key} sx={{ mb: 2, p: 2, border: '1px solid #ccc', borderRadius: 1 }}>
                            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                              <TextField
                                label="Alan Adı"
                                value={field.label}
                                onChange={(e) => handleFieldChange(key, 'label', e.target.value)}
                                fullWidth
                              />
                              <FormControl fullWidth>
                                <InputLabel>Tip</InputLabel>
                                <Select
                                  value={field.type}
                                  label="Tip"
                                  onChange={(e) => handleFieldChange(key, 'type', e.target.value)}
                                >
                                  <MenuItem value="text">Metin</MenuItem>
                                  <MenuItem value="radio">Seçenek</MenuItem>
                                  <MenuItem value="checkbox">Onay Kutusu</MenuItem>
                                </Select>
                              </FormControl>
                              <IconButton
                                color="error"
                                onClick={() => handleRemoveField(key)}
                                sx={{ mt: 1 }}
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Box>

                            {(field.type === 'radio' || field.type === 'checkbox') && (
                              <Box sx={{ mb: 2 }}>
                                <Typography variant="subtitle2" sx={{ mb: 1 }}>Seçenekler</Typography>
                                {field.options?.map((option: string, index: number) => (
                                  <Box key={index} sx={{ display: 'flex', gap: 1, mb: 1 }}>
                                    <TextField
                                      value={option}
                                      onChange={(e) => handleOptionChange(key, index, e.target.value)}
                                      fullWidth
                                    />
                                    <IconButton
                                      color="error"
                                      onClick={() => handleRemoveOption(key, index)}
                                    >
                                      <DeleteIcon />
                                    </IconButton>
                                  </Box>
                                ))}
                                <Button
                                  variant="outlined"
                                  onClick={() => handleAddOption(key)}
                                  startIcon={<AddIcon />}
                                >
                                  Seçenek Ekle
                                </Button>
                              </Box>
                            )}

                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={field.required || false}
                                  onChange={(e) => handleFieldChange(key, 'required', e.target.checked)}
                                />
                              }
                              label="Zorunlu Alan"
                            />
                          </Box>
                        ))}
                      </Grid>
                    </Grid>
                  </Card>
                )}
              </Box>

              {/* Seçilen şablonun detaylarını göster */}
              {formData.selectedTemplateId && !formData.createNewTemplate && (
                <Card sx={{ p: 2, mt: 2, bgcolor: 'action.hover' }}>
                  <Typography variant="subtitle2" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <SettingsIcon />
                    Seçilen Şablon Detayları
                  </Typography>
                  <Grid container spacing={2}>
                    {Object.entries(formData.details).map(([key, field]: [string, any]) => (
                      <Grid item xs={12} sm={6} key={key}>
                        <Box sx={{
                          p: 2,
                          border: '1px solid',
                          borderColor: 'grey.300',
                          borderRadius: 1,
                          '&:hover': {
                            borderColor: 'primary.main',
                            boxShadow: 1
                          }
                        }}>
                          {field.type === 'text' && (
                            <TextField
                              fullWidth
                              label={field.label}
                              value={field.value || ''}
                              onChange={(e) => {
                                setFormData(prev => ({
                                  ...prev,
                                  details: {
                                    ...prev.details,
                                    [key]: { ...field, value: e.target.value }
                                  }
                                }));
                              }}
                              required={field.required}
                              variant="outlined"
                              size="small"
                              helperText={field.required ? 'Bu alan zorunludur' : 'Opsiyonel'}
                            />
                          )}

                          {field.type === 'radio' && (
                            <FormControl fullWidth size="small">
                              <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'medium' }}>
                                {field.label} {field.required && <span style={{ color: 'red' }}>*</span>}
                              </Typography>
                              <Select
                                value={field.value || ''}
                                onChange={(e) => {
                                  setFormData(prev => ({
                                    ...prev,
                                    details: {
                                      ...prev.details,
                                      [key]: { ...field, value: e.target.value }
                                    }
                                  }));
                                }}
                                displayEmpty
                              >
                                <MenuItem value="" disabled>
                                  <em>Seçiniz...</em>
                                </MenuItem>
                                {field.options?.map((option: string) => (
                                  <MenuItem key={option} value={option}>
                                    {option}
                                  </MenuItem>
                                ))}
                              </Select>
                              <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                                {field.required ? 'Bu alan zorunludur' : 'Opsiyonel'}
                              </Typography>
                            </FormControl>
                          )}

                          {field.type === 'checkbox' && (
                            <Box>
                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={field.value || false}
                                    onChange={(e) => {
                                      setFormData(prev => ({
                                        ...prev,
                                        details: {
                                          ...prev.details,
                                          [key]: { ...field, value: e.target.checked }
                                        }
                                      }));
                                    }}
                                    color="primary"
                                  />
                                }
                                label={
                                  <Typography variant="subtitle2" sx={{ fontWeight: 'medium' }}>
                                    {field.label} {field.required && <span style={{ color: 'red' }}>*</span>}
                                  </Typography>
                                }
                              />
                              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', ml: 4 }}>
                                {field.value ? 'Aktif' : 'Pasif'} - {field.required ? 'Bu alan zorunludur' : 'Opsiyonel'}
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                </Card>
              )}

              <Box sx={{ mb: 1 }}>
                <Button
                  variant="contained"
                  onClick={handleNext}
                  sx={{ mt: 1, mr: 1 }}
                  disabled={
                    (!formData.createNewTemplate && !formData.selectedTemplateId) ||
                    (formData.createNewTemplate && Object.keys(formData.newTemplateData.details).length === 0)
                  }
                >
                  Devam Et
                </Button>
              </Box>
            </StepContent>
          </Step>

          {/* Step 2: Category Selection */}
          <Step>
            <StepLabel>Kategori Seçimi</StepLabel>
            <StepContent>
              <Box sx={{ mb: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.createNewCategory}
                      onChange={(e) => {
                        setFormData(prev => ({
                          ...prev,
                          createNewCategory: e.target.checked,
                          categoryId: e.target.checked ? '' : prev.categoryId,
                          newCategoryData: e.target.checked ? {
                            name: '',
                            parentCategoryId: 0,
                            campaignDetailId: null,
                            isActive: true
                          } : { name: '', parentCategoryId: null, campaignDetailId: null, isActive: true },
                          details: {}
                        }));
                        setCategoryFormCompleted(false); // Switch değiştiğinde formu sıfırla
                      }}
                    />
                  }
                  label="Yeni Kategori Oluştur"
                  sx={{ mb: 2 }}
                />

                {/* Kategori seçimi veya yeni kategori oluşturma */}
                {formData.createNewCategory ? (
                  !categoryFormCompleted ? (
                    <Card sx={{ p: 2, bgcolor: 'action.hover' }}>
                      <Typography variant="subtitle2" sx={{ mb: 2 }}>
                        Yeni Kategori Bilgileri
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Kategori Adı *"
                            value={formData.newCategoryData.name}
                            onChange={(e) => setFormData(prev => ({
                              ...prev,
                              newCategoryData: { ...prev.newCategoryData, name: e.target.value }
                            }))}
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <FormControl fullWidth>
                            <InputLabel id="parent-category-label">Üst Kategori</InputLabel>
                            <Select
                              labelId="parent-category-label"
                              value={formData.newCategoryData.parentCategoryId?.toString() || '0'}
                              label="Üst Kategori"
                              onChange={(e) => setFormData(prev => ({
                                ...prev,
                                newCategoryData: { ...prev.newCategoryData, parentCategoryId: parseInt(e.target.value) }
                              }))}
                              sx={{
                                '.MuiSelect-select': {
                                  paddingTop: '12px',
                                  paddingBottom: '12px',
                                },
                              }}
                              renderValue={(selected) => {
                                if (selected === '0') {
                                  return <em>Ana Kategori</em>;
                                }
                                const selectedCat = categories.find(cat => cat.id.toString() === selected);
                                return selectedCat ? selectedCat.name : '';
                              }}
                            >
                              <MenuItem value="0">Ana Kategori</MenuItem>
                              {categories
                                .filter(cat => cat.parentCategoryId === 0 || !cat.parentCategoryId)
                                .map((category) => (
                                  <MenuItem key={category.id} value={category.id.toString()}>
                                    {category.name}
                                  </MenuItem>
                                ))}
                            </Select>
                          </FormControl>
                        </Grid>
                      </Grid>
                    </Card>
                  ) : (
                    <Box sx={{ mt: 2, mb: 2 }}>
                      <Alert severity="info">
                        Yeni kategori bilgileri kaydedildi: <strong>{formData.newCategoryData.name.trim()}</strong>
                        {formData.newCategoryData.parentCategoryId !== 0 && (
                          <span> (Üst Kategori: {categories.find(cat => cat.id === formData.newCategoryData.parentCategoryId)?.name || '?'})</span>
                        )}
                      </Alert>
                    </Box>
                  )
                ) : (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <FormControl fullWidth>
                      <InputLabel>Kategori Seç *</InputLabel>
                      <Select
                        value={formData.categoryId}
                        label="Kategori Seç *"
                        onChange={handleCategoryChange}
                      >
                        {categories.map((category) => (
                          <MenuItem key={category.id} value={category.id.toString()}>
                            {category.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Box>
                )}

                {/* Seçili kategoriye dair bilgi ve uyarılar */}
                {!formData.createNewCategory && formData.categoryId && (
                  <Box sx={{ mb: 2 }}>
                    <Alert severity="success" sx={{ mb: 1 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>
                          Seçilen kategori: <strong>{categories.find(c => c.id.toString() === formData.categoryId)?.name}</strong>
                        </span>
                        {campaignDetails && Object.keys(formData.details).length > 0 && (
                          <Chip
                            label={`${Object.keys(formData.details).length} detay alanı yüklendi`}
                            size="small"
                            color="success"
                            variant="outlined"
                          />
                        )}
                      </Box>
                    </Alert>

                    {campaignDetails && Object.keys(formData.details).length > 0 && (
                      <Alert severity="info" icon={<SettingsIcon />}>
                        Bu kategori için özel detay alanları hazırlandı. Temel Bilgiler ve Detay Şablonu adımında bu bilgileri doldurabilirsiniz.
                      </Alert>
                    )}

                    {(!campaignDetails || Object.keys(formData.details).length === 0) && (
                      <Alert severity="warning">
                        Bu kategori için henüz detay şablonu tanımlanmamış. Kampanya temel bilgilerle oluşturulacak. Çoğu durumda bu normaldir.
                      </Alert>
                    )}
                  </Box>
                )}

              </Box>
              <Box sx={{ mb: 1 }}>
                <Button
                  variant="contained"
                  onClick={handleNext}
                  sx={{ mt: 1, mr: 1 }}
                  disabled={!validateStep(activeStep).isValid} // Adım validasyonu başarılı ise butonu aktif yap
                >
                  Devam Et
                </Button>
                <Button onClick={handleBack} sx={{ mt: 1, mr: 1 }}>
                  Geri
                </Button>
              </Box>
            </StepContent>
          </Step>

          {/* Step 3: Basic Information & Template Details */}
          <Step>
            <StepLabel>Temel Bilgiler ve Detay Şablonu</StepLabel>
            <StepContent>
              <Box sx={{ mb: 2 }}>
                <TextField
                  fullWidth
                  label="Kampanya Adı *"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  label="Başlık"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  label="Açıklama"
                  multiline
                  rows={3}
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  label="Başlık 2"
                  value={formData.title2}
                  onChange={(e) => setFormData(prev => ({ ...prev, title2: e.target.value }))}
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  label="Açıklama 2"
                  multiline
                  rows={3}
                  value={formData.description2}
                  onChange={(e) => setFormData(prev => ({ ...prev, description2: e.target.value }))}
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  label="Başlık 3"
                  value={formData.title3}
                  onChange={(e) => setFormData(prev => ({ ...prev, title3: e.target.value }))}
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  label="Açıklama 3"
                  multiline
                  rows={3}
                  value={formData.description3}
                  onChange={(e) => setFormData(prev => ({ ...prev, description3: e.target.value }))}
                  sx={{ mb: 2 }}
                />
                <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                  <TextField
                    fullWidth
                    label="Başlangıç Tarihi"
                    type="datetime-local"
                    value={formData.startDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                    InputLabelProps={{ shrink: true }}
                  />
                  <TextField
                    fullWidth
                    label="Bitiş Tarihi"
                    type="datetime-local"
                    value={formData.endDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                    InputLabelProps={{ shrink: true }}
                  />
                </Box>
                <TextField
                  fullWidth
                  label="Kampanya URL"
                  value={formData.campaignUrl}
                  onChange={(e) => setFormData(prev => ({ ...prev, campaignUrl: e.target.value }))}
                  helperText="Kampanya için özel URL (örn: ozel-kampanya)"
                  sx={{ mb: 2 }}
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isActive}
                      onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                    />
                  }
                  label="Aktif"
                />

                {/* Şablon Detayları */}
                {formData.createNewTemplate ? (
                  <Card sx={{ p: 3, mt: 3, border: '1px solid', borderColor: 'primary.light', borderRadius: 2 }}>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1, color: 'primary.main' }}>
                      <SettingsIcon />
                      Yeni Şablon Detayları
                    </Typography>

                    <Grid container spacing={3}>
                      {Object.entries(formData.newTemplateData.details).map(([key, field]: [string, any]) => (
                        <Grid item xs={12} sm={6} key={key}>
                          <Box sx={{
                            p: 2,
                            border: '1px solid',
                            borderColor: 'grey.300',
                            borderRadius: 1,
                            '&:hover': {
                              borderColor: 'primary.main',
                              boxShadow: 1
                            }
                          }}>
                            {field.type === 'text' && (
                              <TextField
                                fullWidth
                                label={field.label}
                                value={field.value || ''}
                                onChange={(e) => {
                                  setFormData(prev => ({
                                    ...prev,
                                    newTemplateData: {
                                      ...prev.newTemplateData,
                                      details: {
                                        ...prev.newTemplateData.details,
                                        [key]: { ...field, value: e.target.value }
                                      }
                                    }
                                  }));
                                }}
                                required={field.required}
                                variant="outlined"
                                size="small"
                                helperText={field.required ? 'Bu alan zorunludur' : 'Opsiyonel'}
                              />
                            )}

                            {field.type === 'radio' && (
                              <FormControl fullWidth size="small">
                                <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'medium' }}>
                                  {field.label} {field.required && <span style={{ color: 'red' }}>*</span>}
                                </Typography>
                                <Select
                                  value={field.value || ''}
                                  onChange={(e) => {
                                    setFormData(prev => ({
                                      ...prev,
                                      newTemplateData: {
                                        ...prev.newTemplateData,
                                        details: {
                                          ...prev.newTemplateData.details,
                                          [key]: { ...field, value: e.target.value }
                                        }
                                      }
                                    }));
                                  }}
                                  displayEmpty
                                >
                                  <MenuItem value="" disabled>
                                    <em>Seçiniz...</em>
                                  </MenuItem>
                                  {field.options?.map((option: string) => (
                                    <MenuItem key={option} value={option}>
                                      {option}
                                    </MenuItem>
                                  ))}
                                </Select>
                                <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                                  {field.required ? 'Bu alan zorunludur' : 'Opsiyonel'}
                                </Typography>
                              </FormControl>
                            )}

                            {field.type === 'checkbox' && (
                              <Box>
                                <FormControlLabel
                                  control={
                                    <Switch
                                      checked={field.value || false}
                                      onChange={(e) => {
                                        setFormData(prev => ({
                                          ...prev,
                                          newTemplateData: {
                                            ...prev.newTemplateData,
                                            details: {
                                              ...prev.newTemplateData.details,
                                              [key]: { ...field, value: e.target.checked }
                                            }
                                          }
                                        }));
                                      }}
                                      color="primary"
                                    />
                                  }
                                  label={
                                    <Typography variant="subtitle2" sx={{ fontWeight: 'medium' }}>
                                      {field.label} {field.required && <span style={{ color: 'red' }}>*</span>}
                                    </Typography>
                                  }
                                />
                                <Typography variant="caption" color="text.secondary" sx={{ display: 'block', ml: 4 }}>
                                  {field.value ? 'Aktif' : 'Pasif'} - {field.required ? 'Bu alan zorunludur' : 'Opsiyonel'}
                                </Typography>
                              </Box>
                            )}
                          </Box>
                        </Grid>
                      ))}
                    </Grid>
                  </Card>
                ) : formData.selectedTemplateId && (
                  <Card sx={{ p: 3, mt: 3, border: '1px solid', borderColor: 'primary.light', borderRadius: 2 }}>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1, color: 'primary.main' }}>
                      <SettingsIcon />
                      Seçilen Şablon Detayları
                    </Typography>

                    <Grid container spacing={3}>
                      {Object.entries(formData.details).map(([key, field]: [string, any]) => (
                        <Grid item xs={12} sm={6} key={key}>
                          <Box sx={{
                            p: 2,
                            border: '1px solid',
                            borderColor: 'grey.300',
                            borderRadius: 1,
                            '&:hover': {
                              borderColor: 'primary.main',
                              boxShadow: 1
                            }
                          }}>
                            {field.type === 'text' && (
                              <TextField
                                fullWidth
                                label={field.label}
                                value={field.value || ''}
                                onChange={(e) => {
                                  setFormData(prev => ({
                                    ...prev,
                                    details: {
                                      ...prev.details,
                                      [key]: { ...field, value: e.target.value }
                                    }
                                  }));
                                }}
                                required={field.required}
                                variant="outlined"
                                size="small"
                                helperText={field.required ? 'Bu alan zorunludur' : 'Opsiyonel'}
                              />
                            )}

                            {field.type === 'radio' && (
                              <FormControl fullWidth size="small">
                                <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'medium' }}>
                                  {field.label} {field.required && <span style={{ color: 'red' }}>*</span>}
                                </Typography>
                                <Select
                                  value={field.value || ''}
                                  onChange={(e) => {
                                    setFormData(prev => ({
                                      ...prev,
                                      details: {
                                        ...prev.details,
                                        [key]: { ...field, value: e.target.value }
                                      }
                                    }));
                                  }}
                                  displayEmpty
                                >
                                  <MenuItem value="" disabled>
                                    <em>Seçiniz...</em>
                                  </MenuItem>
                                  {field.options?.map((option: string) => (
                                    <MenuItem key={option} value={option}>
                                      {option}
                                    </MenuItem>
                                  ))}
                                </Select>
                                <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                                  {field.required ? 'Bu alan zorunludur' : 'Opsiyonel'}
                                </Typography>
                              </FormControl>
                            )}

                            {field.type === 'checkbox' && (
                              <Box>
                                <FormControlLabel
                                  control={
                                    <Switch
                                      checked={field.value || false}
                                      onChange={(e) => {
                                        setFormData(prev => ({
                                          ...prev,
                                          details: {
                                            ...prev.details,
                                            [key]: { ...field, value: e.target.checked }
                                          }
                                        }));
                                      }}
                                      color="primary"
                                    />
                                  }
                                  label={
                                    <Typography variant="subtitle2" sx={{ fontWeight: 'medium' }}>
                                      {field.label} {field.required && <span style={{ color: 'red' }}>*</span>}
                                    </Typography>
                                  }
                                />
                                <Typography variant="caption" color="text.secondary" sx={{ display: 'block', ml: 4 }}>
                                  {field.value ? 'Aktif' : 'Pasif'} - {field.required ? 'Bu alan zorunludur' : 'Opsiyonel'}
                                </Typography>
                              </Box>
                            )}
                          </Box>
                        </Grid>
                      ))}
                    </Grid>
                  </Card>
                )}
              </Box>
              <Box sx={{ mb: 1 }}>
                <Button
                  variant="contained"
                  onClick={handleNext}
                  sx={{ mt: 1, mr: 1 }}
                >
                  Devam Et
                </Button>
                <Button onClick={handleBack} sx={{ mt: 1, mr: 1 }}>
                  Geri
                </Button>
              </Box>
            </StepContent>
          </Step>

          {/* Step 4: Brand Selection */}
          <Step>
            <StepLabel>Marka Seçimi</StepLabel>
            <StepContent>
              <Box sx={{ mb: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.createNewBrand}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        createNewBrand: e.target.checked,
                        brandId: e.target.checked ? '' : prev.brandId
                      }))}
                    />
                  }
                  label="Yeni Marka Oluştur"
                  sx={{ mb: 2 }}
                />

                {!formData.createNewBrand ? (
                  <FormControl fullWidth>
                    <InputLabel>Marka Seç *</InputLabel>
                    <Select
                      value={formData.brandId}
                      label="Marka Seç *"
                      onChange={async (e) => {
                        const brandId = e.target.value;
                        setFormData(prev => ({ ...prev, brandId }));

                        // Create brand-category relationship if category is already selected
                        if (brandId && formData.categoryId) {
                          try {
                            const existingRelationships = await axios.get('https://360avantajli.com/api/Campaign_Service/brand-to-category');
                            const existingRelationship = existingRelationships.data?.find((rel: any) =>
                              rel.brand?.id === parseInt(brandId) &&
                              rel.category?.id === parseInt(formData.categoryId) &&
                              rel.isActive
                            );

                            if (!existingRelationship) {
                              await axios.post('https://360avantajli.com/api/Campaign_Service/brand-to-category', {
                                categoryId: parseInt(formData.categoryId),
                                brandId: parseInt(brandId),
                                isActive: true
                              }, { timeout: 10000 });
                            } 
                          } catch (error: any) {
                            console.warn('Brand selection relationship error:', error);
                            // Don't fail the process for this
                          }
                        }
                      }}
                    >
                      {brands.map((brand) => (
                        <MenuItem key={brand.id} value={brand.id.toString()}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {brand.imagePath && (
                              <Avatar
                                src={`https://360avantajli.com/api/Campaign_Service/brand/${brand.id}/image`}
                                sx={{ width: 24, height: 24 }}
                              />
                            )}
                            {brand.name}
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                ) : (
                  <Card sx={{ p: 2, bgcolor: 'action.hover' }}>
                    <Typography variant="subtitle2" sx={{ mb: 2 }}>
                      Yeni Marka Bilgileri
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Marka Adı *"
                          value={formData.newBrandData.name}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            newBrandData: { ...prev.newBrandData, name: e.target.value }
                          }))}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth>
                          <InputLabel id="country-code-label">Ülke Kodu *</InputLabel>
                          <Select
                            labelId="country-code-label"
                            value={formData.newBrandData.countryCode}
                            label="Ülke Kodu *"
                            onChange={(e) => setFormData(prev => ({
                              ...prev,
                              newBrandData: { ...prev.newBrandData, countryCode: e.target.value }
                            }))}
                          >
                            <MenuItem value=""><em>Seçiniz</em></MenuItem>
                            {/* BrandsPage.tsx'ten alınan ülke kodları */}
                            <MenuItem value="90">Türkiye (+90)</MenuItem>
                            <MenuItem value="49">Almanya (+49)</MenuItem>
                            <MenuItem value="31">Hollanda (+31)</MenuItem>
                            <MenuItem value="32">Belçika (+32)</MenuItem>
                            <MenuItem value="43">Avusturya (+43)</MenuItem>
                            <MenuItem value="41">İsviçre (+41)</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Marka URL *"
                          value={formData.newBrandData.brandUrl}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            newBrandData: { ...prev.newBrandData, brandUrl: e.target.value }
                          }))}
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <Button
                          variant="outlined"
                          component="label"
                          startIcon={<UploadIcon />}
                          fullWidth
                        >
                          Logo Yükle
                          <input
                            type="file"
                            hidden
                            accept="image/*"
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) {
                                setFormData(prev => ({
                                  ...prev,
                                  newBrandData: { ...prev.newBrandData, logo: file }
                                }));
                              }
                            }}
                          />
                        </Button>
                        {formData.newBrandData.logo && (
                          <Box sx={{ mt: 2, textAlign: 'center' }}>
                            <Typography variant="caption" sx={{ mt: 1, display: 'block' }}>
                              Seçilen dosya: {formData.newBrandData.logo.name}
                            </Typography>
                            {formData.newBrandData.logo && (
                              <img
                                src={URL.createObjectURL(formData.newBrandData.logo)}
                                alt="Marka Logo Önizleme"
                                style={{
                                  maxWidth: '100px',
                                  maxHeight: '100px',
                                  marginTop: '8px',
                                  border: '1px solid #ccc',
                                  borderRadius: '4px',
                                }}
                              />
                            )}
                          </Box>
                        )}
                      </Grid>
                    </Grid>
                  </Card>
                )}
              </Box>
              <Box sx={{ mb: 1 }}>
                <Button
                  variant="contained"
                  onClick={handleNext}
                  sx={{ mt: 1, mr: 1 }}
                  disabled={!formData.brandId && !formData.createNewBrand}
                >
                  Devam Et
                </Button>
                <Button onClick={handleBack} sx={{ mt: 1, mr: 1 }}>
                  Geri
                </Button>
              </Box>
            </StepContent>
          </Step>

          {/* Step 5: Details & Images */}
          <Step>
            <StepLabel>Detaylar ve Görseller</StepLabel>
            <StepContent>
              <Box sx={{ mb: 2 }}>
                {/* Campaign Details */}
                {campaignDetails && formData.details && Object.keys(formData.details).length > 0 ? (
                  <Card sx={{ p: 3, mb: 3, border: '1px solid', borderColor: 'primary.light', borderRadius: 2 }}>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1, color: 'primary.main' }}>
                      <SettingsIcon />
                      Kampanya Detayları
                    </Typography>

                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      Seçilen kategoriye özel detay bilgilerini doldurun. Bu bilgiler kampanya sayfasında görüntülenecektir.
                    </Typography>

                    <Grid container spacing={3}>
                      {Object.entries(formData.details).map(([key, field]: [string, any]) => (
                        <Grid item xs={12} sm={6} key={key}>
                          <Box sx={{
                            p: 2,
                            border: '1px solid',
                            borderColor: 'grey.300',
                            borderRadius: 1,
                            '&:hover': {
                              borderColor: 'primary.main',
                              boxShadow: 1
                            }
                          }}>
                            {field.type === 'text' && (
                              <TextField
                                fullWidth
                                label={field.label}
                                value={field.value || ''}
                                onChange={(e) => {
                                  setFormData(prev => ({
                                    ...prev,
                                    details: {
                                      ...prev.details,
                                      [key]: { ...field, value: e.target.value }
                                    }
                                  }));
                                }}
                                required={field.required}
                                variant="outlined"
                                size="small"
                                helperText={field.required ? 'Bu alan zorunludur' : 'Opsiyonel'}
                                sx={{
                                  '& .MuiOutlinedInput-root': {
                                    '&:hover fieldset': {
                                      borderColor: 'primary.main',
                                    },
                                  },
                                }}
                              />
                            )}

                            {field.type === 'radio' && (
                              <FormControl fullWidth size="small">
                                <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'medium' }}>
                                  {field.label} {field.required && <span style={{ color: 'red' }}>*</span>}
                                </Typography>
                                <Select
                                  value={field.value || ''}
                                  onChange={(e) => {
                                    setFormData(prev => ({
                                      ...prev,
                                      details: {
                                        ...prev.details,
                                        [key]: { ...field, value: e.target.value }
                                      }
                                    }));
                                  }}
                                  displayEmpty
                                  sx={{
                                    '&:hover .MuiOutlinedInput-notchedOutline': {
                                      borderColor: 'primary.main',
                                    },
                                  }}
                                >
                                  <MenuItem value="" disabled>
                                    <em>Seçiniz...</em>
                                  </MenuItem>
                                  {field.options?.map((option: string) => (
                                    <MenuItem key={option} value={option}>
                                      {option}
                                    </MenuItem>
                                  ))}
                                </Select>
                                <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                                  {field.required ? 'Bu alan zorunludur' : 'Opsiyonel'}
                                </Typography>
                              </FormControl>
                            )}

                            {field.type === 'checkbox' && (
                              <Box>
                                <FormControlLabel
                                  control={
                                    <Switch
                                      checked={field.value || false}
                                      onChange={(e) => {
                                        setFormData(prev => ({
                                          ...prev,
                                          details: {
                                            ...prev.details,
                                            [key]: { ...field, value: e.target.checked }
                                          }
                                        }));
                                      }}
                                      color="primary"
                                    />
                                  }
                                  label={
                                    <Typography variant="subtitle2" sx={{ fontWeight: 'medium' }}>
                                      {field.label} {field.required && <span style={{ color: 'red' }}>*</span>}
                                    </Typography>
                                  }
                                />
                                <Typography variant="caption" color="text.secondary" sx={{ display: 'block', ml: 4 }}>
                                  {field.value ? 'Aktif' : 'Pasif'} - {field.required ? 'Bu alan zorunludur' : 'Opsiyonel'}
                                </Typography>
                              </Box>
                            )}
                          </Box>
                        </Grid>
                      ))}
                    </Grid>

                    {/* Summary */}
                    <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'medium' }}>
                        Detay Özeti:
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {Object.entries(formData.details)
                          .filter(([_, field]: [string, any]) => field.value && field.value !== '')
                          .map(([key, field]: [string, any]) => (
                            <Chip
                              key={key}
                              label={`${field.label}: ${field.type === 'checkbox' ? (field.value ? 'Evet' : 'Hayır') : field.value}`}
                              size="small"
                              variant="outlined"
                              color="primary"
                            />
                          ))}
                        {Object.entries(formData.details).filter(([_, field]: [string, any]) => field.value && field.value !== '').length === 0 && (
                          <Typography variant="caption" color="text.secondary">
                            Henüz detay bilgisi girilmemiş
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Card>
                ) : (
                  <Card sx={{ p: 2, mb: 3, bgcolor: 'info.light', borderRadius: 2 }}>
                    <Typography variant="body2" color="info.dark" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <SettingsIcon />
                      Bu kategori için özel detay şablonu bulunmuyor. Bu normal bir durumdur - yeni kategoriler için detay şablonları sonradan eklenebilir.
                    </Typography>
                  </Card>
                )}

                {/* Image Upload */}
                <Card sx={{ p: 2 }}>
                  <Typography variant="subtitle1" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <ImageIcon />
                    Kampanya Görselleri
                  </Typography>

                  {/* Showcase Image */}
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle2" sx={{ mb: 2 }}>
                      Vitrin Görseli
                    </Typography>

                    {!formData.showcaseImage ? (
                      <Button
                        variant="outlined"
                        component="label"
                        startIcon={<UploadIcon />}
                        fullWidth
                        sx={{
                          mb: 1,
                          minHeight: 120,
                          borderStyle: 'dashed',
                          borderWidth: 2,
                          '&:hover': {
                            borderStyle: 'dashed',
                            borderWidth: 2,
                          }
                        }}
                      >
                        <Box sx={{ textAlign: 'center' }}>
                          <UploadIcon sx={{ fontSize: 48, mb: 1, opacity: 0.5 }} />
                          <Typography variant="body2">
                            Vitrin Görseli Seç
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            (JPG, PNG, GIF - Max 5MB)
                          </Typography>
                        </Box>
                        <input
                          ref={showcaseImageInputRef}
                          type="file"
                          hidden
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              if (file.size > 5 * 1024 * 1024) {
                                setErrors(['Dosya boyutu 5MB\'dan büyük olamaz']);
                                return;
                              }
                              handleShowcaseImageUpload(file);
                              // Clear input to allow re-selecting same file
                              e.target.value = '';
                            }
                          }}
                        />
                      </Button>
                    ) : (
                      <Grid container spacing={2} sx={{ mb: 2 }}> {/* Vitrin görseli için de Grid container kullanıldı */}
                        <Grid item xs={12} sm={6} md={4} key="showcase-preview"> {/* Detay görselleri gibi Grid item içine alındı */}
                          <Card sx={{ position: 'relative' }}>
                            <Box sx={{ position: 'relative' }}>
                              {formData.showcaseImagePreview === 'loading' ? (
                                <Box
                                  sx={{
                                    width: '100%',
                                    height: 120,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    bgcolor: 'grey.100',
                                    borderRadius: '4px 4px 0 0'
                                  }}
                                >
                                  <CircularProgress size={24} />
                                </Box>
                              ) : (
                                <img
                                  src={formData.showcaseImagePreview}
                                  alt="Vitrin Görseli Önizleme"
                                  style={{
                                    width: '100%',
                                    height: 120,
                                    objectFit: 'cover',
                                    borderRadius: '4px 4px 0 0'
                                  }}
                                />
                              )}
                              <IconButton
                                sx={{
                                  position: 'absolute',
                                  top: 4,
                                  right: 4,
                                  bgcolor: 'rgba(0,0,0,0.7)',
                                  color: 'white',
                                  width: 24,
                                  height: 24,
                                  '&:hover': {
                                    bgcolor: 'rgba(0,0,0,0.9)',
                                  }
                                }}
                                size="small"
                                onClick={() => setFormData(prev => ({
                                  ...prev,
                                  showcaseImage: undefined,
                                  showcaseImagePreview: undefined
                                }))}
                              >
                                <CloseIcon sx={{ fontSize: 16 }} />
                              </IconButton>
                            </Box>
                            {formData.showcaseImage && (
                              <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
                                <Typography variant="caption" color="text.secondary">
                                  {formData.showcaseImage.name} ({(formData.showcaseImage.size / 1024 / 1024).toFixed(2)} MB)
                                </Typography>
                              </CardContent>
                            )}
                          </Card>
                        </Grid>
                      </Grid>
                    )}
                  </Box>

                  {/* Detail Images */}
                  <Box>
                    <Typography variant="subtitle2" sx={{ mb: 2 }}>
                      Detay Görselleri
                    </Typography>

                    <Button
                      variant="outlined"
                      component="label"
                      startIcon={<UploadIcon />}
                      fullWidth
                      sx={{
                        mb: 2,
                        minHeight: 80,
                        borderStyle: 'dashed',
                        borderWidth: 2,
                        '&:hover': {
                          borderStyle: 'dashed',
                          borderWidth: 2,
                        }
                      }}
                    >
                      <Box sx={{ textAlign: 'center' }}>
                        <UploadIcon sx={{ fontSize: 32, mb: 1, opacity: 0.5 }} />
                        <Typography variant="body2">
                          Detay Görselleri Seç (Çoklu)
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          (JPG, PNG, GIF - Max 5MB her biri)
                        </Typography>
                      </Box>
                      <input
                        ref={detailImagesInputRef}
                        type="file"
                        hidden
                        accept="image/*"
                        multiple
                        onChange={(e) => {
                          const files = Array.from(e.target.files || []);
                          if (files.length > 0) {
                            // Check file sizes
                            const oversizedFiles = files.filter(file => file.size > 5 * 1024 * 1024);
                            if (oversizedFiles.length > 0) {
                              setErrors([`${oversizedFiles.length} dosya 5MB'dan büyük. Lütfen daha küçük dosyalar seçin.`]);
                              return;
                            }
                            handleDetailImagesUpload(files);
                            // Clear input to allow re-selecting same files
                            e.target.value = '';
                          }
                        }}
                      />
                    </Button>

                    {/* Detail Images Preview Grid */}
                    {formData.detailImages.length > 0 && (
                      <Grid container spacing={2}>
                        {formData.detailImages.map((file, index) => (
                          <Grid item xs={6} sm={4} md={3} key={index}>
                            <Card sx={{ position: 'relative' }}>
                              <Box sx={{ position: 'relative' }}>
                                {formData.detailImagePreviews[index] === 'loading' ? (
                                  <Box
                                    sx={{
                                      width: '100%',
                                      height: 120,
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      bgcolor: 'grey.100',
                                      borderRadius: '4px 4px 0 0'
                                    }}
                                  >
                                    <CircularProgress size={24} />
                                  </Box>
                                ) : (
                                  <img
                                    src={formData.detailImagePreviews[index]}
                                    alt={`Detay Görseli ${index + 1}`}
                                    style={{
                                      width: '100%',
                                      height: 120,
                                      objectFit: 'cover',
                                      borderRadius: '4px 4px 0 0'
                                    }}
                                  />
                                )}
                                <IconButton
                                  sx={{
                                    position: 'absolute',
                                    top: 4,
                                    right: 4,
                                    bgcolor: 'rgba(0,0,0,0.7)',
                                    color: 'white',
                                    width: 24,
                                    height: 24,
                                    '&:hover': {
                                      bgcolor: 'rgba(0,0,0,0.9)',
                                    }
                                  }}
                                  size="small"
                                  onClick={() => removeDetailImage(index)}
                                >
                                  <CloseIcon sx={{ fontSize: 16 }} />
                                </IconButton>
                              </Box>
                              <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
                                <Typography
                                  variant="caption"
                                  color="text.secondary"
                                  sx={{
                                    display: 'block',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap'
                                  }}
                                >
                                  {file.name}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {(file.size / 1024 / 1024).toFixed(2)} MB
                                </Typography>
                              </CardContent>
                            </Card>
                          </Grid>
                        ))}
                      </Grid>
                    )}

                    {formData.detailImages.length > 0 && (
                      <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                        Toplam {formData.detailImages.length} detay görseli seçildi
                      </Typography>
                    )}
                  </Box>
                </Card>
              </Box>

              <Box sx={{ mb: 1 }}>
                <Button
                  variant="contained"
                  onClick={handleNext}
                  sx={{ mt: 1, mr: 1 }}
                >
                  Devam Et
                </Button>
                <Button onClick={handleBack} sx={{ mt: 1, mr: 1 }}>
                  Geri
                </Button>
              </Box>
            </StepContent>
          </Step>

          {/* Step 6: Summary and Confirmation */}
          <Step>
            <StepLabel>Özet ve Onay</StepLabel>
            <StepContent>
              <Box sx={{ mb: 2 }}>
                <Typography variant="h6" gutterBottom>Kampanya Bilgileri</Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Paper sx={{ p: 2 }}>
                      <Typography variant="subtitle1" gutterBottom>Genel Bilgiler</Typography>
                      <Box sx={{ mb: 2 }}>
                        <Typography><strong>Kampanya Adı:</strong> {formData.name}</Typography>
                        <Typography><strong>Başlık:</strong> {formData.title || '-'}</Typography>
                        <Typography><strong>Açıklama:</strong> {formData.description || '-'}</Typography>
                        <Typography><strong>Kategori:</strong> {selectedCategory?.name || '-'}</Typography>
                        <Typography><strong>Durum:</strong> {formData.isActive ? 'Aktif' : 'Pasif'}</Typography>
                        <Typography><strong>Başlangıç Tarihi:</strong> {formatDateForSummary(formData.startDate)}</Typography>
                        <Typography><strong>Bitiş Tarihi:</strong> {formatDateForSummary(formData.endDate)}</Typography>
                      </Box>
                    </Paper>
                  </Grid>

                  {/* Detay Şablonu Bilgileri */}
                  <Grid item xs={12}>
                    <Paper sx={{ p: 2 }}>
                      <Typography variant="subtitle1" gutterBottom>Detay Şablonu Bilgileri</Typography>
                      {formData.createNewTemplate ? (
                        <Box>
                          <Typography><strong>Yeni Şablon Adı:</strong> {formData.newTemplateData.name}</Typography>
                          <Typography variant="subtitle2" sx={{ mt: 2 }}>Şablon Alanları:</Typography>
                          {Object.entries(formData.newTemplateData.details).map(([key, field]: [string, any]) => (
                            <Box key={key} sx={{ ml: 2, mb: 1 }}>
                              <Box>
                                <Typography component="div">
                                  <strong>{field.label}</strong> ({field.type})
                                  {field.required && <span style={{ color: 'red' }}> *</span>}
                                </Typography>
                                {field.options && (
                                  <Typography component="div" variant="body2" color="text.secondary">
                                    Seçenekler: {field.options.join(', ')}
                                  </Typography>
                                )}
                              </Box>
                            </Box>
                          ))}
                        </Box>
                      ) : (
                        <Box>
                          <Typography><strong>Seçilen Şablon:</strong> {selectedTemplate?.name || `Şablon ${formData.selectedTemplateId}`}</Typography>
                          {formData.details && Object.entries(formData.details).length > 0 && (
                            <>
                              <Typography variant="subtitle2" sx={{ mt: 2 }}>Şablon Alanları:</Typography>
                              {Object.entries(formData.details).map(([key, field]: [string, any]) => (
                                <Box key={key} sx={{ ml: 2, mb: 1 }}>
                                  <Box>
                                    <Typography component="div">
                                      <strong>{field.label}</strong> ({field.type})
                                      {field.required && <span style={{ color: 'red' }}> *</span>}
                                    </Typography>
                                    {field.options && (
                                      <Typography component="div" variant="body2" color="text.secondary">
                                        Seçenekler: {field.options.join(', ')}
                                      </Typography>
                                    )}
                                  </Box>
                                </Box>
                              ))}
                            </>
                          )}
                        </Box>
                      )}
                    </Paper>
                  </Grid>

                  {/* Marka Bilgileri */}
                  <Grid item xs={12}>
                    <Paper sx={{ p: 2 }}>
                      <Typography variant="subtitle1" gutterBottom>Marka Bilgileri</Typography>
                      {formData.createNewBrand ? (
                        <Box>
                          <Typography><strong>Yeni Marka Adı:</strong> {formData.newBrandData.name}</Typography>
                          <Typography><strong>Ülke Kodu:</strong> {formData.newBrandData.countryCode}</Typography>
                          <Typography><strong>Marka URL:</strong> {formData.newBrandData.brandUrl}</Typography>
                          {formData.newBrandData.logo && (
                            <Box sx={{ mt: 2 }}>
                              <Typography><strong>Logo:</strong></Typography>
                              <img
                                src={URL.createObjectURL(formData.newBrandData.logo)}
                                alt="Marka Logo"
                                style={{ maxWidth: '200px', maxHeight: '200px' }}
                              />
                            </Box>
                          )}
                        </Box>
                      ) : (
                        <Typography><strong>Seçilen Marka:</strong> {selectedBrand?.name || '-'}</Typography>
                      )}
                    </Paper>
                  </Grid>

                  {/* Görsel Bilgileri */}
                  <Grid item xs={12}>
                    <Paper sx={{ p: 2 }}>
                      <Typography variant="subtitle1" gutterBottom>Görsel Bilgileri</Typography>
                      {formData.showcaseImagePreview && (
                        <Box sx={{ mb: 2 }}>
                          <Typography><strong>Vitrin Görseli:</strong></Typography>
                          <img
                            src={formData.showcaseImagePreview}
                            alt="Vitrin Görseli"
                            style={{ maxWidth: '200px', maxHeight: '200px' }}
                          />
                        </Box>
                      )}
                      {formData.detailImagePreviews.length > 0 && (
                        <Box>
                          <Typography><strong>Detay Görselleri:</strong></Typography>
                          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mt: 1 }}>
                            {formData.detailImagePreviews.map((preview, index) => (
                              <img
                                key={index}
                                src={preview}
                                alt={`Detay Görsel ${index + 1}`}
                                style={{ maxWidth: '100px', maxHeight: '100px' }}
                              />
                            ))}
                          </Box>
                        </Box>
                      )}
                    </Paper>
                  </Grid>
                </Grid>
              </Box>

              <Box sx={{ mb: 1 }}>
                <Button
                  variant="contained"
                  onClick={handleSubmit}
                  sx={{ mt: 1, mr: 1 }}
                >
                  {editCampaign ? 'Kampanyayı Güncelle' : 'Kampanyayı Oluştur'}
                </Button>
                <Button onClick={handleBack} sx={{ mt: 1, mr: 1 }}>
                  Geri
                </Button>
              </Box>
            </StepContent>
          </Step>
        </Stepper>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={isLoading}>İptal</Button>
        {activeStep === 5 && (
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={isLoading}
            startIcon={isLoading ? <CircularProgress size={20} /> : <CheckIcon />}
            size="large"
            sx={{
              minWidth: 200,
              background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
              '&:hover': {
                background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)',
              }
            }}
          >
            {editCampaign ? 'Kampanyayı Güncelle' : 'Kampanyayı Oluştur'}
          </Button>
        )}
      </DialogActions>

      <LoadingOverlay
        open={isLoading}
        message="Kampanya işleniyor..."
        variant="backdrop"
      />
    </Dialog>
  );
};

export default CampaignWizard;
