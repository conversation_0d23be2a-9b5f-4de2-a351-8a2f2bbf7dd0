import React, { createContext, useState, useContext, useEffect } from 'react';
import { IntlProvider } from 'react-intl';
import trMessages from './messages/tr-TR';
import enMessages from './messages/en-US';

type Locale = 'tr-TR' | 'en-US';

interface LanguageContextType {
  locale: Locale;
  setLocale: (locale: Locale) => void;
}

const LanguageContext = createContext<LanguageContextType>({
  locale: 'tr-TR',
  setLocale: () => {},
});

export const useLanguage = () => useContext(LanguageContext);

const messages = {
  'tr-TR': trMessages,
  'en-US': enMessages,
};

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [locale, setLocale] = useState<Locale>(() => {
    const savedLocale = localStorage.getItem('locale');
    return (savedLocale as Locale) || 'tr-TR';
  });

  useEffect(() => {
    localStorage.setItem('locale', locale);
  }, [locale]);

  return (
    <LanguageContext.Provider value={{ locale, setLocale }}>
      <IntlProvider
        messages={messages[locale]}
        locale={locale}
        defaultLocale="tr-TR"
      >
        {children}
      </IntlProvider>
    </LanguageContext.Provider>
  );
};

export default LanguageContext; 